# -*- coding: utf-8 -*-
"""
Intelligent Guardrails System for AI Instructor
==============================================

This system works WITH the AI as it generates responses, providing intelligent
constraints that ensure:
1. 100% lesson objectives coverage before quiz transitions
2. Age/level appropriate content delivery
3. Pedagogical rule enforcement during content generation

The guardrails act as intelligent guides rather than hard blocks, helping the
AI instructor deliver optimal educational experiences.
"""

import logging
import re
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class GuardrailSeverity(Enum):
    """Severity levels for guardrail violations"""
    INFO = "info"           # Informational guidance
    WARNING = "warning"     # Should be addressed but not blocking
    CRITICAL = "critical"   # Must be addressed before proceeding
    BLOCKING = "blocking"   # Prevents progression entirely

@dataclass
class GuardrailViolation:
    """Represents a guardrail violation or guidance"""
    rule_id: str
    severity: GuardrailSeverity
    message: str
    suggestion: str
    context: Dict[str, Any]
    timestamp: datetime

class IntelligentGuardrailsManager:
    """
    Manages intelligent guardrails for the AI instructor.
    Works WITH the AI to ensure optimal educational delivery.
    """
    
    def __init__(self):
        self.active_rules = self._initialize_teaching_rules()
        self.objective_tracker = ObjectiveCoverageTracker()
        self.content_validator = AgeAppropriateContentValidator()
        self.pedagogical_enforcer = PedagogicalRuleEnforcer()
        
    def _initialize_teaching_rules(self) -> Dict[str, Dict]:
        """Initialize the core teaching rules that act as guardrails"""
        return {
            # OBJECTIVE COVERAGE RULES
            "objective_coverage_100": {
                "description": "Ensure 100% objective coverage before quiz transition",
                "priority": 1,
                "enforcement_level": "critical",
                "validation_function": self._validate_objective_coverage
            },
            
            "objective_depth_validation": {
                "description": "Ensure sufficient depth of coverage for each objective",
                "priority": 2,
                "enforcement_level": "warning",
                "validation_function": self._validate_objective_depth
            },
            
            # AGE APPROPRIATENESS RULES
            "cognitive_level_matching": {
                "description": "Match content complexity to student's cognitive level",
                "priority": 1,
                "enforcement_level": "critical",
                "validation_function": self._validate_cognitive_level
            },
            
            "vocabulary_appropriateness": {
                "description": "Use age-appropriate vocabulary and concepts",
                "priority": 2,
                "enforcement_level": "warning",
                "validation_function": self._validate_vocabulary
            },
            
            # PEDAGOGICAL RULES
            "scaffolding_progression": {
                "description": "Ensure proper scaffolding from simple to complex",
                "priority": 1,
                "enforcement_level": "warning",
                "validation_function": self._validate_scaffolding
            },
            
            "engagement_maintenance": {
                "description": "Maintain student engagement through varied activities",
                "priority": 2,
                "enforcement_level": "info",
                "validation_function": self._validate_engagement
            },
            
            "assessment_readiness": {
                "description": "Ensure student is ready for assessment",
                "priority": 1,
                "enforcement_level": "blocking",
                "validation_function": self._validate_assessment_readiness
            }
        }
    
    def validate_ai_response(
        self, 
        ai_response: str, 
        lesson_context: Dict[str, Any],
        session_data: Dict[str, Any],
        teaching_truly_complete: bool,
        request_id: str
    ) -> Tuple[bool, List[GuardrailViolation], str]:
        """
        Validate AI response against all guardrails.
        
        Args:
            ai_response: The AI-generated response to validate
            lesson_context: Context about the lesson and student
            session_data: Current session state
            teaching_truly_complete: Whether teaching is truly complete (from validate_teaching_completion)
            request_id: Request identifier for logging
            
        Returns: (is_valid, violations, enhanced_response)
        """
        logger.info(f"[{request_id}] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: {teaching_truly_complete})")
        
        violations = []
        enhanced_response = ai_response
        
        # Use the teaching_truly_complete status for assessment readiness
        # If teaching is not truly complete, block any attempts to transition to quiz
        if not teaching_truly_complete:
            # Check if AI is trying to transition to quiz
            quiz_transition_phrases = [
                'quiz', 'test', 'assessment', 'question 1:', 'question 2:',
                'let\'s test', 'time for a quiz', 'ready for the quiz',
                'choose the correct answer', 'select the best answer'
            ]
            
            if any(phrase in ai_response.lower() for phrase in quiz_transition_phrases):
                logger.warning(f"[{request_id}] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete")
                violations.append(GuardrailViolation(
                    rule_id="teaching_incomplete_quiz_block",
                    severity=GuardrailSeverity.BLOCKING,
                    message="Quiz transition blocked - teaching not complete",
                    suggestion="Continue teaching until all completion criteria are met",
                    context={
                        "teaching_truly_complete": teaching_truly_complete,
                        "blocked_content": "quiz_transition"
                    },
                    timestamp=datetime.now(timezone.utc)
                ))
        
        # Run all validation rules
        for rule_id, rule_config in self.active_rules.items():
            try:
                violation = rule_config["validation_function"](
                    ai_response, lesson_context, session_data, request_id
                )
                if violation:
                    violations.append(violation)
                    logger.info(f"[{request_id}] 🛡️ GUARDRAIL VIOLATION: {rule_id} - {violation.severity.value}")
            except Exception as e:
                logger.error(f"[{request_id}] Error in guardrail {rule_id}: {e}")
        
        # Determine if response is valid based on violations
        blocking_violations = [v for v in violations if v.severity == GuardrailSeverity.BLOCKING]
        critical_violations = [v for v in violations if v.severity == GuardrailSeverity.CRITICAL]
        
        is_valid = len(blocking_violations) == 0
        
        # Enhance response with guardrail guidance
        if violations:
            enhanced_response = self._enhance_response_with_guidance(
                ai_response, violations, lesson_context, request_id
            )
        
        logger.info(f"[{request_id}] 🛡️ GUARDRAILS RESULT: Valid={is_valid}, Violations={len(violations)}")
        return is_valid, violations, enhanced_response
    
    def _validate_objective_coverage(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate that learning objectives are being adequately covered"""
        
        # Check if AI is trying to transition to quiz
        is_quiz_transition = any(phrase in ai_response.lower() for phrase in [
            "let's test", "quiz time", "time for a quiz", "ready for the quiz"
        ])
        
        if not is_quiz_transition:
            return None
        
        # Get objective coverage from session data
        objectives_tracking = session_data.get('objectives_tracking', {})
        completion_percentage = objectives_tracking.get('completion_percentage', 0)
        
        # Apply the 100% coverage rule with time-based fallback
        elapsed_minutes = session_data.get('elapsed_minutes', 0)
        
        if elapsed_minutes < 30:  # Normal conditions - require 100%
            required_coverage = 100
        elif elapsed_minutes < 35:  # Time pressure - allow 80% fallback
            required_coverage = 80
        else:  # Emergency - allow 70% minimum
            required_coverage = 70
        
        if completion_percentage < required_coverage:
            return GuardrailViolation(
                rule_id="objective_coverage_100",
                severity=GuardrailSeverity.BLOCKING,
                message=f"Objective coverage insufficient: {completion_percentage:.1f}% < {required_coverage}%",
                suggestion=f"Continue teaching to reach {required_coverage}% objective coverage before quiz",
                context={
                    "current_coverage": completion_percentage,
                    "required_coverage": required_coverage,
                    "elapsed_minutes": elapsed_minutes
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _validate_objective_depth(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate depth of objective coverage"""
        
        objectives_tracking = session_data.get('objectives_tracking', {})
        objectives_status = objectives_tracking.get('objectives_status', {})
        
        shallow_objectives = []
        for obj_id, obj_data in objectives_status.items():
            if obj_data.get('covered', False):
                confidence = obj_data.get('coverage_confidence', 0)
                if confidence < 0.7:  # Shallow coverage
                    shallow_objectives.append(obj_id)
        
        if len(shallow_objectives) > 0:
            return GuardrailViolation(
                rule_id="objective_depth_validation",
                severity=GuardrailSeverity.WARNING,
                message=f"Shallow coverage detected for {len(shallow_objectives)} objectives",
                suggestion="Provide more detailed explanations and examples for better understanding",
                context={"shallow_objectives": shallow_objectives},
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _validate_cognitive_level(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate content matches student's cognitive level"""
        
        student_level = session_data.get('assigned_level_for_teaching', 5)
        grade = lesson_context.get('grade', 'Primary 5')
        
        # Analyze response complexity
        complexity_score = self._analyze_content_complexity(ai_response)
        expected_complexity = self._get_expected_complexity_for_level(student_level, grade)
        
        # Check if content is too complex or too simple
        if complexity_score > expected_complexity + 2:
            return GuardrailViolation(
                rule_id="cognitive_level_matching",
                severity=GuardrailSeverity.CRITICAL,
                message=f"Content too complex for level {student_level}",
                suggestion="Simplify language and concepts to match student's cognitive level",
                context={
                    "complexity_score": complexity_score,
                    "expected_complexity": expected_complexity,
                    "student_level": student_level
                },
                timestamp=datetime.now(timezone.utc)
            )
        elif complexity_score < expected_complexity - 2:
            return GuardrailViolation(
                rule_id="cognitive_level_matching",
                severity=GuardrailSeverity.WARNING,
                message=f"Content may be too simple for level {student_level}",
                suggestion="Consider adding more challenging concepts appropriate for the student's level",
                context={
                    "complexity_score": complexity_score,
                    "expected_complexity": expected_complexity,
                    "student_level": student_level
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _validate_vocabulary(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate vocabulary appropriateness for age/grade level"""
        
        grade = lesson_context.get('grade', 'Primary 5')
        
        # Extract grade number for analysis
        grade_number = self._extract_grade_number(grade)
        
        # Check for overly complex vocabulary
        complex_words = self._identify_complex_vocabulary(ai_response, grade_number)
        
        if len(complex_words) > 3:  # Too many complex words
            return GuardrailViolation(
                rule_id="vocabulary_appropriateness",
                severity=GuardrailSeverity.WARNING,
                message=f"Vocabulary may be too advanced for {grade}",
                suggestion=f"Consider simpler alternatives for: {', '.join(complex_words[:3])}",
                context={
                    "complex_words": complex_words,
                    "grade": grade,
                    "grade_number": grade_number
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _validate_scaffolding(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate proper scaffolding progression"""
        
        current_interactions = session_data.get('teaching_interactions', 0)
        
        # Check if jumping too quickly to complex concepts
        if current_interactions < 3:  # Early in lesson
            complexity_indicators = [
                "advanced", "complex", "sophisticated", "intricate",
                "furthermore", "consequently", "nevertheless"
            ]
            
            if any(indicator in ai_response.lower() for indicator in complexity_indicators):
                return GuardrailViolation(
                    rule_id="scaffolding_progression",
                    severity=GuardrailSeverity.WARNING,
                    message="May be introducing complex concepts too early",
                    suggestion="Start with foundational concepts before advancing to complex topics",
                    context={"current_interactions": current_interactions},
                    timestamp=datetime.now(timezone.utc)
                )
        
        return None
    
    def _validate_engagement(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate content maintains student engagement"""
        
        # Check for engagement elements
        engagement_elements = [
            r'\?',  # Questions
            r'!',   # Excitement
            r'emoji',  # Emojis (simplified check)
            r'let\'s',  # Interactive language
            r'you can',  # Empowering language
            r'imagine',  # Creative elements
        ]
        
        engagement_score = sum(
            len(re.findall(pattern, ai_response, re.IGNORECASE)) 
            for pattern in engagement_elements
        )
        
        response_length = len(ai_response.split())
        
        # Expect at least 1 engagement element per 50 words
        expected_engagement = max(1, response_length // 50)
        
        if engagement_score < expected_engagement:
            return GuardrailViolation(
                rule_id="engagement_maintenance",
                severity=GuardrailSeverity.INFO,
                message="Response could be more engaging",
                suggestion="Add questions, excitement, or interactive elements to maintain engagement",
                context={
                    "engagement_score": engagement_score,
                    "expected_engagement": expected_engagement,
                    "response_length": response_length
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _validate_assessment_readiness(
        self, ai_response: str, lesson_context: Dict, session_data: Dict, request_id: str
    ) -> Optional[GuardrailViolation]:
        """Validate student is ready for assessment"""
        
        # Check if AI is trying to transition to quiz
        is_quiz_transition = any(phrase in ai_response.lower() for phrase in [
            "let's test", "quiz time", "time for a quiz", "ready for the quiz"
        ])
        
        if not is_quiz_transition:
            return None
        
        # Check multiple readiness indicators
        readiness_criteria = {
            'objectives_coverage': session_data.get('objectives_tracking', {}).get('completion_percentage', 0) >= 80,
            'content_depth': session_data.get('content_depth_score', 0) >= 0.7,
            'min_interactions': session_data.get('teaching_interactions', 0) >= 8,
            'min_time': session_data.get('elapsed_minutes', 0) >= 12,
            'understanding_confirmed': session_data.get('student_understanding_confirmed', False)
        }
        
        failed_criteria = [k for k, v in readiness_criteria.items() if not v]
        
        if len(failed_criteria) > 1:  # Allow 1 failed criterion
            return GuardrailViolation(
                rule_id="assessment_readiness",
                severity=GuardrailSeverity.BLOCKING,
                message=f"Student not ready for assessment: {len(failed_criteria)} criteria unmet",
                suggestion="Continue teaching to meet assessment readiness criteria",
                context={
                    "failed_criteria": failed_criteria,
                    "readiness_criteria": readiness_criteria
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        return None
    
    def _enhance_response_with_guidance(
        self, 
        ai_response: str, 
        violations: List[GuardrailViolation],
        lesson_context: Dict,
        request_id: str
    ) -> str:
        """Enhance AI response with guardrail guidance"""
        
        # Separate violations by severity
        blocking = [v for v in violations if v.severity == GuardrailSeverity.BLOCKING]
        critical = [v for v in violations if v.severity == GuardrailSeverity.CRITICAL]
        warnings = [v for v in violations if v.severity == GuardrailSeverity.WARNING]
        
        enhanced_response = ai_response
        
        # Handle blocking violations - prevent quiz transition
        if blocking:
            logger.info(f"[{request_id}] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition")
            # Remove quiz transition language
            quiz_phrases = [
                r"let's test.*?understanding",
                r"quiz time",
                r"time for.*?quiz",
                r"ready for.*?quiz"
            ]
            for phrase in quiz_phrases:
                enhanced_response = re.sub(phrase, "let's continue learning", enhanced_response, flags=re.IGNORECASE)
            
            # Add guidance message
            guidance = blocking[0].suggestion
            enhanced_response += f"\n\n{guidance}"
        
        # Handle critical violations - adjust content
        elif critical:
            logger.info(f"[{request_id}] 🛡️ CRITICAL VIOLATIONS: Adjusting content complexity")
            guidance = critical[0].suggestion
            enhanced_response = f"Let me adjust that explanation. {guidance}\n\n{enhanced_response}"
        
        # Handle warnings - add gentle guidance
        elif warnings and len(warnings) >= 2:
            logger.info(f"[{request_id}] 🛡️ WARNING VIOLATIONS: Adding gentle guidance")
            guidance = warnings[0].suggestion
            enhanced_response += f"\n\n💡 {guidance}"
        
        return enhanced_response
    
    # Helper methods for content analysis
    def _analyze_content_complexity(self, content: str) -> float:
        """Analyze content complexity (simplified implementation)"""
        words = content.split()
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        sentence_count = len([s for s in content.split('.') if s.strip()])
        avg_sentence_length = len(words) / sentence_count if sentence_count > 0 else 0
        
        # Simple complexity score based on word and sentence length
        complexity = (avg_word_length * 0.5) + (avg_sentence_length * 0.1)
        return min(complexity, 10)  # Cap at 10
    
    def _get_expected_complexity_for_level(self, student_level: int, grade: str) -> float:
        """Get expected complexity score for student level and grade"""
        base_complexity = {
            1: 2.0, 2: 2.5, 3: 3.0, 4: 3.5, 5: 4.0,
            6: 4.5, 7: 5.0, 8: 5.5, 9: 6.0, 10: 6.5
        }
        return base_complexity.get(student_level, 4.0)
    
    def _extract_grade_number(self, grade: str) -> int:
        """Extract numeric grade from grade string"""
        numbers = re.findall(r'\d+', grade)
        return int(numbers[0]) if numbers else 5
    
    def _identify_complex_vocabulary(self, content: str, grade_number: int) -> List[str]:
        """Identify vocabulary that may be too complex for grade level"""
        # Simplified implementation - in practice, you'd use a vocabulary database
        complex_patterns = {
            1: r'\b\w{8,}\b',  # Words longer than 7 letters for grade 1
            2: r'\b\w{9,}\b',  # Words longer than 8 letters for grade 2
            3: r'\b\w{10,}\b', # Words longer than 9 letters for grade 3
            4: r'\b\w{11,}\b', # Words longer than 10 letters for grade 4
            5: r'\b\w{12,}\b', # Words longer than 11 letters for grade 5
        }
        
        pattern = complex_patterns.get(grade_number, r'\b\w{12,}\b')
        complex_words = re.findall(pattern, content, re.IGNORECASE)
        
        # Filter out common long words that are actually simple
        simple_long_words = {'understand', 'important', 'different', 'remember', 'everything'}
        return [word for word in complex_words if word.lower() not in simple_long_words]


class ObjectiveCoverageTracker:
    """Tracks learning objective coverage in real-time"""
    
    def __init__(self):
        self.coverage_threshold = 0.8  # 80% minimum coverage
    
    def analyze_coverage(self, session_data: Dict) -> Dict[str, Any]:
        """Analyze current objective coverage status"""
        objectives_tracking = session_data.get('objectives_tracking', {})
        
        return {
            'completion_percentage': objectives_tracking.get('completion_percentage', 0),
            'covered_objectives': objectives_tracking.get('covered_objectives', 0),
            'total_objectives': objectives_tracking.get('total_objectives', 0),
            'is_sufficient': objectives_tracking.get('completion_percentage', 0) >= (self.coverage_threshold * 100)
        }


class AgeAppropriateContentValidator:
    """Validates content appropriateness for student age/grade"""
    
    def __init__(self):
        self.grade_vocabulary_levels = {
            1: {'max_syllables': 2, 'max_word_length': 6},
            2: {'max_syllables': 3, 'max_word_length': 7},
            3: {'max_syllables': 3, 'max_word_length': 8},
            4: {'max_syllables': 4, 'max_word_length': 9},
            5: {'max_syllables': 4, 'max_word_length': 10},
        }
    
    def validate_content(self, content: str, grade: str, student_level: int) -> Dict[str, Any]:
        """Validate content appropriateness"""
        grade_number = self._extract_grade_number(grade)
        
        return {
            'is_appropriate': True,  # Simplified - would implement full validation
            'complexity_score': self._calculate_complexity(content),
            'vocabulary_issues': [],
            'suggestions': []
        }
    
    def _extract_grade_number(self, grade: str) -> int:
        """Extract numeric grade from grade string"""
        numbers = re.findall(r'\d+', grade)
        return int(numbers[0]) if numbers else 5
    
    def _calculate_complexity(self, content: str) -> float:
        """Calculate content complexity score"""
        # Simplified implementation
        words = content.split()
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        return min(avg_word_length / 2, 5.0)


class PedagogicalRuleEnforcer:
    """Enforces pedagogical best practices"""
    
    def __init__(self):
        self.scaffolding_rules = {
            'start_simple': True,
            'build_gradually': True,
            'check_understanding': True,
            'provide_examples': True
        }
    
    def enforce_scaffolding(self, content: str, lesson_progress: Dict) -> Dict[str, Any]:
        """Enforce scaffolding principles"""
        return {
            'follows_scaffolding': True,  # Simplified implementation
            'progression_appropriate': True,
            'engagement_level': 'high',
            'suggestions': []
        }


# Integration function for the main lesson system
def apply_intelligent_guardrails(
    ai_response: str,
    lesson_context: Dict[str, Any],
    session_data: Dict[str, Any],
    teaching_truly_complete: bool,  # Add this parameter
    request_id: str
) -> Tuple[bool, str, List[Dict]]:
    """
    Apply intelligent guardrails to AI response.
    
    Args:
        ai_response: The AI-generated response to validate
        lesson_context: Context about the lesson and student
        session_data: Current session state
        teaching_truly_complete: Whether teaching is truly complete (from validate_teaching_completion)
        request_id: Request identifier for logging
    
    Returns:
        (is_valid, enhanced_response, violation_details)
    """
    
    guardrails_manager = IntelligentGuardrailsManager()
    
    is_valid, violations, enhanced_response = guardrails_manager.validate_ai_response(
        ai_response, lesson_context, session_data, teaching_truly_complete, request_id
    )
    
    # Convert violations to serializable format
    violation_details = [
        {
            'rule_id': v.rule_id,
            'severity': v.severity.value,
            'message': v.message,
            'suggestion': v.suggestion,
            'context': v.context
        }
        for v in violations
    ]
    
    logger.info(f"[{request_id}] 🛡️ GUARDRAILS APPLIED: Valid={is_valid}, Violations={len(violations)}, Teaching Complete={teaching_truly_complete}")
    
    return is_valid, enhanced_response, violation_details
