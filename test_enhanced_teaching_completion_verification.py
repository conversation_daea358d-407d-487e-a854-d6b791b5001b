#!/usr/bin/env python3
"""
Enhanced Teaching Completion System Verification Test
Tests all the fixes implemented in the previous conversation.
"""

import sys
import os
import json
import time
import requests
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_backend_connectivity():
    """Test basic backend connectivity"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=10)
        if response.status_code == 200:
            logger.info("✅ Backend connectivity test passed")
            return True
        else:
            logger.error(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Backend connectivity test failed: {e}")
        return False

def test_enhanced_teaching_completion():
    """Test the enhanced teaching completion system"""
    try:
        # Test data for a teaching phase interaction
        test_payload = {
            "lesson_ref": "P5_ENT_046",
            "student_input": "I understand entrepreneurship is about starting businesses and taking risks to create value.",
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 8,
                "objectives_covered": 85,
                "student_engagement": "high",
                "lesson_context": {
                    "subject": "Entrepreneurship",
                    "grade": "P5",
                    "lesson_title": "Introduction to Entrepreneurship"
                }
            }
        }
        
        logger.info("🧪 Testing enhanced teaching completion system...")
        
        response = requests.post(
            'http://localhost:5000/enhance_content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Check for key indicators of the enhanced system
            checks = {
                "AI Instructor Integration": "ai_instructor" in str(result).lower(),
                "Teaching Completion Validation": "teaching_complete" in str(result).lower() or "completion" in str(result).lower(),
                "Intelligent Guardrails": "guardrails" in str(result).lower() or "validation" in str(result).lower(),
                "Phase Transition Logic": "phase" in str(result).lower(),
                "Response Quality": len(result.get('enhanced_content', '')) > 50
            }
            
            logger.info("📊 Enhanced Teaching Completion System Test Results:")
            for check_name, passed in checks.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"  {status} {check_name}")
            
            # Log response details for analysis
            logger.info(f"📝 Response length: {len(result.get('enhanced_content', ''))}")
            logger.info(f"📝 Response keys: {list(result.keys())}")
            
            return all(checks.values())
            
        else:
            logger.error(f"❌ Enhanced teaching completion test failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Enhanced teaching completion test error: {e}")
        return False

def test_teaching_rules_engine():
    """Test that teaching rules engine is accessible"""
    try:
        # Test a simple teaching rules validation
        test_payload = {
            "lesson_ref": "P5_ENT_046",
            "student_input": "What is entrepreneurship?",
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 3,
                "objectives_covered": 40
            }
        }
        
        logger.info("🔧 Testing teaching rules engine accessibility...")
        
        response = requests.post(
            'http://localhost:5000/enhance_content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=20
        )
        
        if response.status_code == 200:
            logger.info("✅ Teaching rules engine test passed")
            return True
        else:
            logger.error(f"❌ Teaching rules engine test failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Teaching rules engine test error: {e}")
        return False

def test_intelligent_guardrails():
    """Test intelligent guardrails with teaching completion parameter"""
    try:
        # Test payload that should trigger guardrails validation
        test_payload = {
            "lesson_ref": "P5_ENT_046",
            "student_input": "Can we do a quiz now?",
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 2,
                "objectives_covered": 30  # Low coverage should block quiz
            }
        }
        
        logger.info("🛡️ Testing intelligent guardrails system...")
        
        response = requests.post(
            'http://localhost:5000/enhance_content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if guardrails are working (should not transition to quiz with low coverage)
            response_text = result.get('enhanced_content', '').lower()
            quiz_blocked = 'quiz' not in response_text or 'continue' in response_text or 'more' in response_text
            
            if quiz_blocked:
                logger.info("✅ Intelligent guardrails test passed - quiz appropriately blocked")
                return True
            else:
                logger.warning("⚠️ Intelligent guardrails may not be working - quiz not blocked")
                return False
                
        else:
            logger.error(f"❌ Intelligent guardrails test failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Intelligent guardrails test error: {e}")
        return False

def test_session_state_management():
    """Test session state management fixes"""
    try:
        # Test creating a new session
        test_payload = {
            "lesson_ref": "P5_ENT_046",
            "student_input": "Hello, I'm ready to learn about entrepreneurship.",
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 0,
                "objectives_covered": 0
            }
        }
        
        logger.info("💾 Testing session state management...")
        
        response = requests.post(
            'http://localhost:5000/enhance_content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if session state is properly managed
            has_content = len(result.get('enhanced_content', '')) > 0
            has_state_info = any(key in result for key in ['phase', 'session', 'state'])
            
            if has_content and has_state_info:
                logger.info("✅ Session state management test passed")
                return True
            else:
                logger.warning("⚠️ Session state management may have issues")
                return False
                
        else:
            logger.error(f"❌ Session state management test failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Session state management test error: {e}")
        return False

def run_comprehensive_verification():
    """Run all verification tests"""
    logger.info("🚀 Starting Enhanced Teaching Completion System Verification")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Run all tests
    tests = [
        ("Backend Connectivity", test_backend_connectivity),
        ("Enhanced Teaching Completion", test_enhanced_teaching_completion),
        ("Teaching Rules Engine", test_teaching_rules_engine),
        ("Intelligent Guardrails", test_intelligent_guardrails),
        ("Session State Management", test_session_state_management)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            test_results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED {test_name}: {e}")
            test_results[test_name] = False
    
    # Generate summary report
    logger.info("\n" + "=" * 80)
    logger.info("📊 VERIFICATION SUMMARY REPORT")
    logger.info("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n📈 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Enhanced Teaching Completion System is working!")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} tests failed - System needs attention")
        return False

if __name__ == "__main__":
    success = run_comprehensive_verification()
    sys.exit(0 if success else 1)