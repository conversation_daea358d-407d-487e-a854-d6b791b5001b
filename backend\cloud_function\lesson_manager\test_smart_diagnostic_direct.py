#!/usr/bin/env python3
"""
Direct Smart Diagnostic Test - bypassing full lesson manager initialization
"""

import os
import sys
import time
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_smart_diagnostic_direct():
    """Test Smart Diagnostic system directly"""
    try:
        logger.info("🚀 Starting Direct Smart Diagnostic Test")
        
        # Load environment variables
        load_dotenv()
        
        # Import only the smart diagnostic functions
        from main import (
            get_smart_diagnostic_config,
            determine_adaptive_questions_q4_q5,
            calculate_final_teaching_level,
            get_initial_probing_level
        )
        
        logger.info("✅ Smart diagnostic functions imported successfully")
        
        # Test 1: Smart diagnostic configuration
        logger.info("📝 Testing smart diagnostic configuration...")
        config = get_smart_diagnostic_config('Primary 5')
        logger.info(f"✅ Config for Primary 5: {config}")
        
        # Test 2: Initial probing level
        logger.info("📝 Testing initial probing level...")
        initial_level = get_initial_probing_level('Primary 5')
        logger.info(f"✅ Initial probing level: {initial_level}")
        
        # Test 3: Adaptive questions determination
        logger.info("📝 Testing adaptive questions Q4/Q5...")
        adaptive_config = determine_adaptive_questions_q4_q5(
            q1_score=0.8,  # Good foundation
            q2_score=0.7,  # Good grade level
            q3_score=0.6,  # Moderate ceiling
            grade='Primary 5'
        )
        logger.info(f"✅ Adaptive config: {adaptive_config}")
        
        # Test 4: Final teaching level calculation
        logger.info("📝 Testing final teaching level calculation...")
        final_assessment = calculate_final_teaching_level(
            q1_score=0.8,
            q2_score=0.7,
            q3_score=0.6,
            q4_score=0.7,
            q5_score=0.8,
            grade='Primary 5'
        )
        logger.info(f"✅ Final assessment: {final_assessment}")
        
        # Test 5: Smart diagnostic state management
        logger.info("📝 Testing smart diagnostic state...")
        
        # Simulate a smart diagnostic session
        session_state = {
            'smart_diagnostic': {
                'current_question': 1,
                'questions_completed': 0,
                'diagnostic_complete': False,
                'grade': 'Primary 5'
            }
        }
        
        # Import session management functions
        from main import (
            record_smart_diagnostic_answer,
            get_smart_diagnostic_teaching_level,
            is_smart_diagnostic_complete
        )
        
        logger.info("✅ Session management functions imported")
        
        # Simulate answering Q1
        session_state = record_smart_diagnostic_answer(
            session_state=session_state,
            question_number=1,
            question_score=0.8,
            student_answer="A computer is a machine that helps us work and play games.",
            grade='Primary 5',
            request_id='test_q1'
        )
        
        logger.info(f"✅ Q1 recorded: {session_state['smart_diagnostic']}")
        
        # Continue with Q2-Q5
        for q_num in range(2, 6):
            session_state = record_smart_diagnostic_answer(
                session_state=session_state,
                question_number=q_num,
                question_score=0.7,  # Consistent performance
                student_answer=f"Answer to question {q_num}",
                grade='Primary 5',
                request_id=f'test_q{q_num}'
            )
            logger.info(f"✅ Q{q_num} recorded")
        
        # Check if diagnostic is complete
        is_complete = is_smart_diagnostic_complete(session_state)
        logger.info(f"✅ Diagnostic complete: {is_complete}")
        
        # Get final teaching level
        if is_complete:
            teaching_level = get_smart_diagnostic_teaching_level(
                session_state=session_state,
                grade='Primary 5',
                request_id='test_final'
            )
            logger.info(f"✅ Final teaching level: {teaching_level}")
        
        logger.info("🎉 Direct Smart Diagnostic Test Complete!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    success = await test_smart_diagnostic_direct()
    
    if success:
        print("\n🎉 DIRECT SMART DIAGNOSTIC TEST: SUCCESS!")
        print("✅ Smart Diagnostic core functions are working")
    else:
        print("\n⚠️  DIRECT SMART DIAGNOSTIC TEST: FAILED")
        print("❌ Smart Diagnostic core functions have issues")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
