"""
Teaching Rules System - Comprehensive Teaching Validation and Enforcement

This module contains all teaching rules logic to ensure students receive adequate
teaching before transitioning to quiz phases. It addresses the critical issue where
the frontend shows "teaching" phase but quiz content is being delivered.

Key Features:
- Enforces minimum teaching interactions (10+)
- Requires 85%+ objective coverage
- Validates content depth score (0.75+)
- Ensures minimum teaching time (15+ minutes)
- Prevents early quiz transitions
- Maintains phase/content consistency
"""

import logging
import time
from typing import Dict, Any, Tuple, Optional, List
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class TeachingRulesEngine:
    """
    Comprehensive teaching rules engine that enforces proper teaching progression
    and prevents premature quiz transitions.
    """
    
    def __init__(self):
        # ENHANCED: Adaptive teaching completion thresholds based on context
        # PRIMARY DRIVER: 100% objective coverage is the main goal
        self.OPTIMAL_OBJECTIVE_COVERAGE = 100.0  # PRIMARY: 100% objective coverage
        self.FALLBACK_OBJECTIVE_COVERAGE = 85.0  # FALLBACK: 85% when time constrained
        
        # Base thresholds (will be adjusted based on context)
        self.BASE_MIN_INTERACTIONS = 10  # Base minimum, adjusted by context
        self.BASE_CONTENT_DEPTH_SCORE = 0.75
        self.BASE_TEACHING_TIME_MINUTES = 12
        
        # Grade-based interaction multipliers
        self.GRADE_INTERACTION_MULTIPLIERS = {
            'nursery': 0.7,    # Younger students need fewer interactions
            'primary_1': 0.8,  # P1-P3 need moderate interactions
            'primary_2': 0.8,
            'primary_3': 0.8,
            'primary_4': 0.9,  # P4-P6 need more interactions
            'primary_5': 1.0,  # Base level
            'primary_6': 1.1,
            'jss_1': 1.2,     # JSS students need more interactions
            'jss_2': 1.2,
            'jss_3': 1.3,
            'sss_1': 1.4,     # SSS students need most interactions
            'sss_2': 1.4,
            'sss_3': 1.5
        }
        
        # Teaching level multipliers (higher levels = more complex content)
        self.TEACHING_LEVEL_MULTIPLIERS = {
            1: 0.6,   # Very basic level
            2: 0.7,   # Basic level
            3: 0.8,   # Elementary level
            4: 0.9,   # Intermediate level
            5: 1.0,   # Standard level (base)
            6: 1.1,   # Advanced level
            7: 1.2,   # Complex level
            8: 1.3,   # Very complex level
            9: 1.4,   # Expert level
            10: 1.5   # Master level
        }
        
        # Lesson complexity factors (based on content analysis)
        self.COMPLEXITY_FACTORS = {
            'simple': 0.8,     # Simple lessons need fewer interactions
            'moderate': 1.0,   # Moderate complexity (base)
            'complex': 1.2,    # Complex lessons need more interactions
            'advanced': 1.4    # Advanced lessons need most interactions
        }
        
        # Emergency thresholds (safety nets)
        self.EMERGENCY_INTERACTION_LIMIT = 25
        self.EMERGENCY_TIME_LIMIT_MINUTES = 45
        
        # UI Timer Integration - 37.5-minute last resort trigger
        self.UI_TIMER_LIMIT_MINUTES = 37.5  # Matches the UI timer
        self.UI_TIMER_WARNING_MINUTES = 35.0  # Warning 2.5 minutes before timer
        
        logger.info("🎓 Enhanced Teaching Rules Engine initialized with adaptive validation")
    
    def calculate_adaptive_requirements(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate adaptive teaching requirements based on student context and lesson complexity.
        
        Args:
            context: Lesson context with student and lesson information
            
        Returns:
            Dictionary with adaptive requirements
        """
        try:
            # Extract context information
            grade = context.get('grade', 'primary_5').lower()
            teaching_level = context.get('teaching_level', 5)
            lesson_content = context.get('instructional_content', '') or context.get('lesson_description', '')
            total_objectives = context.get('total_objectives', 1)
            
            # Determine lesson complexity based on content analysis
            lesson_complexity = self._analyze_lesson_complexity(lesson_content, total_objectives)
            
            # Calculate grade multiplier
            grade_multiplier = self.GRADE_INTERACTION_MULTIPLIERS.get(grade, 1.0)
            
            # Calculate teaching level multiplier
            level_multiplier = self.TEACHING_LEVEL_MULTIPLIERS.get(teaching_level, 1.0)
            
            # Calculate complexity multiplier
            complexity_multiplier = self.COMPLEXITY_FACTORS.get(lesson_complexity, 1.0)
            
            # Calculate adaptive minimum interactions
            # Formula: base * grade_factor * level_factor * complexity_factor
            adaptive_min_interactions = max(
                4,  # Absolute minimum
                int(self.BASE_MIN_INTERACTIONS * grade_multiplier * level_multiplier * complexity_multiplier)
            )
            
            # Calculate adaptive content depth requirement
            # Higher levels need deeper content understanding
            adaptive_content_depth = min(
                0.9,  # Maximum requirement
                self.BASE_CONTENT_DEPTH_SCORE + (teaching_level - 5) * 0.02
            )
            
            # Calculate adaptive teaching time
            # More complex lessons may need more time
            adaptive_teaching_time = max(
                8,  # Minimum time
                int(self.BASE_TEACHING_TIME_MINUTES * complexity_multiplier)
            )
            
            adaptive_requirements = {
                'min_interactions': adaptive_min_interactions,
                'min_content_depth': adaptive_content_depth,
                'min_teaching_time': adaptive_teaching_time,
                'grade_multiplier': grade_multiplier,
                'level_multiplier': level_multiplier,
                'complexity_multiplier': complexity_multiplier,
                'lesson_complexity': lesson_complexity,
                'reasoning': {
                    'grade': f"{grade} (×{grade_multiplier:.1f})",
                    'teaching_level': f"Level {teaching_level} (×{level_multiplier:.1f})",
                    'complexity': f"{lesson_complexity} (×{complexity_multiplier:.1f})",
                    'calculation': f"{self.BASE_MIN_INTERACTIONS} × {grade_multiplier:.1f} × {level_multiplier:.1f} × {complexity_multiplier:.1f} = {adaptive_min_interactions}"
                }
            }
            
            logger.info(f"🎯 ADAPTIVE REQUIREMENTS CALCULATED:")
            logger.info(f"   Grade: {grade} → Multiplier: {grade_multiplier:.1f}")
            logger.info(f"   Teaching Level: {teaching_level} → Multiplier: {level_multiplier:.1f}")
            logger.info(f"   Lesson Complexity: {lesson_complexity} → Multiplier: {complexity_multiplier:.1f}")
            logger.info(f"   Adaptive Min Interactions: {adaptive_min_interactions}")
            logger.info(f"   Adaptive Content Depth: {adaptive_content_depth:.2f}")
            logger.info(f"   Adaptive Teaching Time: {adaptive_teaching_time} min")
            
            return adaptive_requirements
            
        except Exception as e:
            logger.error(f"❌ Error calculating adaptive requirements: {e}")
            # Return safe defaults
            return {
                'min_interactions': self.BASE_MIN_INTERACTIONS,
                'min_content_depth': self.BASE_CONTENT_DEPTH_SCORE,
                'min_teaching_time': self.BASE_TEACHING_TIME_MINUTES,
                'grade_multiplier': 1.0,
                'level_multiplier': 1.0,
                'complexity_multiplier': 1.0,
                'lesson_complexity': 'moderate',
                'reasoning': {'error': str(e)}
            }
    
    def _analyze_lesson_complexity(self, lesson_content: str, total_objectives: int) -> str:
        """
        Analyze lesson complexity based on content and objectives.
        
        Args:
            lesson_content: The lesson content text
            total_objectives: Number of learning objectives
            
        Returns:
            Complexity level: 'simple', 'moderate', 'complex', or 'advanced'
        """
        try:
            if not lesson_content:
                return 'moderate'  # Default for missing content
            
            content_length = len(lesson_content)
            word_count = len(lesson_content.split())
            
            # Complexity indicators
            complexity_score = 0
            
            # Content length factor
            if content_length > 2000:
                complexity_score += 2
            elif content_length > 1000:
                complexity_score += 1
            
            # Word count factor
            if word_count > 300:
                complexity_score += 2
            elif word_count > 150:
                complexity_score += 1
            
            # Objectives count factor
            if total_objectives > 5:
                complexity_score += 2
            elif total_objectives > 3:
                complexity_score += 1
            
            # Advanced vocabulary indicators
            advanced_terms = [
                'analyze', 'synthesize', 'evaluate', 'critique', 'hypothesis',
                'methodology', 'theoretical', 'conceptual', 'paradigm',
                'correlation', 'causation', 'inference', 'deduction'
            ]
            
            content_lower = lesson_content.lower()
            advanced_count = sum(1 for term in advanced_terms if term in content_lower)
            if advanced_count > 3:
                complexity_score += 2
            elif advanced_count > 1:
                complexity_score += 1
            
            # Determine complexity level
            if complexity_score >= 6:
                return 'advanced'
            elif complexity_score >= 4:
                return 'complex'
            elif complexity_score >= 2:
                return 'moderate'
            else:
                return 'simple'
                
        except Exception as e:
            logger.error(f"❌ Error analyzing lesson complexity: {e}")
            return 'moderate'
    
    def validate_teaching_completion(self, session_data: Dict[str, Any], 
                                   context: Dict[str, Any]) -> Tuple[bool, str, Dict[str, Any]]:
        """
        ENHANCED: Adaptive teaching completion validation with 100% objective coverage as primary driver.
        
        Args:
            session_data: Current session state data
            context: Lesson context with interaction data
            
        Returns:
            Tuple of (is_complete, reason, validation_details)
        """
        try:
            # Calculate adaptive requirements based on student context and lesson complexity
            adaptive_reqs = self.calculate_adaptive_requirements(context)
            
            # Extract current metrics
            teaching_interactions = context.get('teaching_interactions', 0)
            objectives_covered = context.get('objectives_covered', 0)
            total_objectives = context.get('total_objectives', 1)
            content_depth_score = context.get('content_depth_score', 0.0)
            
            # Calculate teaching time and check UI timer status
            teaching_start_time = session_data.get('teaching_start_time')
            lesson_start_time = session_data.get('lesson_start_time', teaching_start_time)
            teaching_time_minutes = 0
            total_lesson_time_minutes = 0
            
            if teaching_start_time:
                current_time = datetime.now(timezone.utc)
                if isinstance(teaching_start_time, str):
                    teaching_start_time = datetime.fromisoformat(teaching_start_time.replace('Z', '+00:00'))
                teaching_time_minutes = (current_time - teaching_start_time).total_seconds() / 60
            
            if lesson_start_time:
                current_time = datetime.now(timezone.utc)
                if isinstance(lesson_start_time, str):
                    lesson_start_time = datetime.fromisoformat(lesson_start_time.replace('Z', '+00:00'))
                elif isinstance(lesson_start_time, (int, float)):
                    lesson_start_time = datetime.fromtimestamp(lesson_start_time, tz=timezone.utc)
                total_lesson_time_minutes = (current_time - lesson_start_time).total_seconds() / 60
            
            # Calculate objective coverage percentage
            objective_coverage_pct = (objectives_covered / total_objectives) * 100 if total_objectives > 0 else 0
            
            # Use adaptive requirements for validation
            min_interactions = adaptive_reqs['min_interactions']
            min_content_depth = adaptive_reqs['min_content_depth']
            min_teaching_time = adaptive_reqs['min_teaching_time']
            
            # ENHANCED: Adaptive teaching criteria validation
            criteria_results = {
                'interactions_sufficient': teaching_interactions >= min_interactions,
                'objectives_coverage_met': objective_coverage_pct >= self.FALLBACK_OBJECTIVE_COVERAGE,
                'content_depth_sufficient': content_depth_score >= min_content_depth,
                'teaching_time_sufficient': teaching_time_minutes >= min_teaching_time,
                'optimal_objectives_met': objective_coverage_pct >= self.OPTIMAL_OBJECTIVE_COVERAGE
            }
            
            # Count criteria met
            core_criteria_met = sum([
                criteria_results['interactions_sufficient'],
                criteria_results['objectives_coverage_met'],
                criteria_results['content_depth_sufficient'],
                criteria_results['teaching_time_sufficient']
            ])
            
            # Determine completion status with PRIMARY FOCUS on 100% objective coverage
            is_complete = False
            completion_reason = ""
            
            # PRIMARY DRIVER: 100% objective completion is the absolute priority
            if criteria_results['optimal_objectives_met']:
                # 100% objectives achieved - minimum interactions is secondary safeguard
                if criteria_results['interactions_sufficient']:
                    is_complete = True
                    completion_reason = "primary_driver_100_percent_objectives_with_sufficient_interactions"
                    logger.info(f"🎯 PRIMARY SUCCESS: 100% objectives covered with {teaching_interactions}/{min_interactions} interactions")
                else:
                    # 100% objectives achieved but need minimum interactions for full lesson delivery
                    remaining_interactions = min_interactions - teaching_interactions
                    if remaining_interactions <= 2:  # Allow completion if very close to minimum
                        is_complete = True
                        completion_reason = "primary_driver_100_percent_objectives_nearly_sufficient_interactions"
                        logger.info(f"🎯 PRIMARY SUCCESS: 100% objectives covered, {remaining_interactions} interactions short of minimum")
                    else:
                        is_complete = False
                        completion_reason = f"primary_driver_100_percent_objectives_need_{remaining_interactions}_more_interactions"
                        logger.info(f"🎯 PRIMARY PENDING: 100% objectives covered, need {remaining_interactions} more interactions for full lesson delivery")
                        logger.info(f"🎯 PRIMARY SUCCESS: 100% objectives covered, {remaining_interactions} interactions short but acceptable")
                    else:
                        completion_reason = f"primary_driver_100_percent_objectives_need_{remaining_interactions}_more_interactions"
                        logger.info(f"🎯 PRIMARY PROGRESS: 100% objectives covered, need {remaining_interactions} more interactions")
            
            # SECONDARY: Time-constrained completion with adaptive requirements
            # This should ONLY activate when approaching time limits (30+ minutes)
            elif (total_lesson_time_minutes >= 30.0 and  # Time pressure threshold
                  core_criteria_met >= 3 and 
                  criteria_results['objectives_coverage_met']):
                is_complete = True
                completion_reason = f"time_constrained_completion_{core_criteria_met}_of_4_criteria_met_at_{total_lesson_time_minutes:.1f}_minutes"
                logger.info(f"🎯 TIME-CONSTRAINED SUCCESS: {core_criteria_met}/4 criteria met due to time pressure ({total_lesson_time_minutes:.1f} min)")
                logger.info(f"🎯 NOTE: Secondary completion activated due to approaching time limits")
            
            # UI Timer Integration: 37.5-minute last resort trigger
            elif total_lesson_time_minutes >= self.UI_TIMER_LIMIT_MINUTES:
                is_complete = True
                completion_reason = "ui_timer_37_5_minute_last_resort_trigger"
                logger.warning(f"🕐 UI TIMER TRIGGER: 37.5-minute limit reached - forcing quiz transition as last resort")
            
            # UI Timer Warning: Approaching 37.5-minute limit - CONTINUE TEACHING
            elif total_lesson_time_minutes >= self.UI_TIMER_WARNING_MINUTES:
                completion_reason = "ui_timer_warning_continue_teaching_until_full_limit"
                logger.info(f"⏰ UI TIMER WARNING: Approaching 37.5-minute limit - continuing teaching until full limit reached")
            
            # Emergency completion: Prevent infinite teaching
            elif (teaching_interactions >= self.EMERGENCY_INTERACTION_LIMIT or 
                  teaching_time_minutes >= self.EMERGENCY_TIME_LIMIT_MINUTES):
                is_complete = True
                completion_reason = "emergency_completion_time_limit_reached"
            
            else:
                completion_reason = "teaching_incomplete_continue_instruction"
            
            # Enhanced validation details with adaptive requirements
            validation_details = {
                'teaching_interactions': teaching_interactions,
                'adaptive_min_interactions': min_interactions,
                'adaptive_min_content_depth': min_content_depth,
                'adaptive_min_teaching_time': min_teaching_time,
                'objective_coverage_pct': objective_coverage_pct,
                'optimal_objective_coverage': self.OPTIMAL_OBJECTIVE_COVERAGE,
                'fallback_objective_coverage': self.FALLBACK_OBJECTIVE_COVERAGE,
                'content_depth_score': content_depth_score,
                'teaching_time_minutes': teaching_time_minutes,
                'total_lesson_time_minutes': total_lesson_time_minutes,
                'ui_timer_limit_minutes': self.UI_TIMER_LIMIT_MINUTES,
                'ui_timer_warning_minutes': self.UI_TIMER_WARNING_MINUTES,
                'core_criteria_met': core_criteria_met,
                'criteria_results': criteria_results,
                'completion_reason': completion_reason,
                'adaptive_requirements': adaptive_reqs
            }
            
            # Enhanced logging with adaptive requirements
            logger.info(f"🎓 ENHANCED TEACHING VALIDATION RESULTS:")
            logger.info(f"   📊 ADAPTIVE REQUIREMENTS:")
            logger.info(f"      Grade: {adaptive_reqs['reasoning']['grade']}")
            logger.info(f"      Teaching Level: {adaptive_reqs['reasoning']['teaching_level']}")
            logger.info(f"      Lesson Complexity: {adaptive_reqs['reasoning']['complexity']}")
            logger.info(f"      Calculation: {adaptive_reqs['reasoning']['calculation']}")
            logger.info(f"   📈 CURRENT PROGRESS:")
            logger.info(f"      Interactions: {teaching_interactions}/{min_interactions} ({'✅' if criteria_results['interactions_sufficient'] else '❌'})")
            logger.info(f"      Objectives: {objective_coverage_pct:.1f}%/100% (PRIMARY) ({'✅' if criteria_results['optimal_objectives_met'] else '❌'})")
            logger.info(f"      Content Depth: {content_depth_score:.2f}/{min_content_depth:.2f} ({'✅' if criteria_results['content_depth_sufficient'] else '❌'})")
            logger.info(f"      Teaching Time: {teaching_time_minutes:.1f}/{min_teaching_time} min ({'✅' if criteria_results['teaching_time_sufficient'] else '❌'})")
            logger.info(f"   🎯 RESULT: {'✅ COMPLETE' if is_complete else '❌ INCOMPLETE'} - {completion_reason}")
            
            return is_complete, completion_reason, validation_details
            
        except Exception as e:
            logger.error(f"❌ Enhanced teaching validation error: {e}")
            return False, "validation_error", {}
    
    def enforce_teaching_phase_consistency(self, current_phase: str, ai_response: str, 
                                         session_data: Dict[str, Any], 
                                         context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Enforce consistency between phase and content to prevent quiz content 
        from appearing during teaching phase.
        
        Args:
            current_phase: Current lesson phase
            ai_response: AI-generated response
            session_data: Session state data
            context: Lesson context
            
        Returns:
            Tuple of (corrected_phase, corrected_response, correction_reason)
        """
        try:
            # Check if we're in teaching phase but AI is generating quiz content
            if current_phase.startswith('teaching'):
                # Detect quiz content patterns in AI response
                quiz_indicators = [
                    'question 1:', 'question 2:', 'question 3:',
                    'quiz question', 'test question', 'assessment question',
                    'choose the correct answer', 'select the best answer',
                    'multiple choice', 'true or false',
                    'a)', 'b)', 'c)', 'd)',
                    'which of the following', 'what is the correct'
                ]
                
                response_lower = ai_response.lower()
                quiz_content_detected = any(indicator in response_lower for indicator in quiz_indicators)
                
                if quiz_content_detected:
                    # Validate if teaching is actually complete
                    is_complete, reason, details = self.validate_teaching_completion(session_data, context)
                    
                    if not is_complete:
                        # Teaching not complete - force teaching content
                        logger.warning(f"🚨 PHASE MISMATCH DETECTED: Quiz content in teaching phase!")
                        logger.warning(f"   Teaching completion: {reason}")
                        logger.warning(f"   Forcing teaching content generation")
                        
                        # Generate appropriate teaching response
                        topic = context.get('topic', 'this topic')
                        student_name = context.get('student_name', 'Student')
                        teaching_interactions = context.get('teaching_interactions', 0)
                        
                        corrected_response = self._generate_teaching_response(
                            topic, student_name, teaching_interactions, details
                        )
                        
                        return current_phase, corrected_response, "quiz_content_blocked_teaching_incomplete"
                    
                    else:
                        # Teaching is complete - allow quiz transition
                        logger.info(f"✅ Teaching complete, allowing quiz transition: {reason}")
                        return "quiz_initiate", ai_response, "teaching_complete_quiz_allowed"
            
            # No correction needed
            return current_phase, ai_response, "no_correction_needed"
            
        except Exception as e:
            logger.error(f"❌ Phase consistency enforcement error: {e}")
            return current_phase, ai_response, "enforcement_error"
    
    def _generate_teaching_response(self, topic: str, student_name: str, 
                                  interactions: int, validation_details: Dict[str, Any]) -> str:
        """
        Generate appropriate teaching response when quiz content is blocked.
        """
        try:
            # Determine what needs improvement
            criteria = validation_details.get('criteria_results', {})
            missing_criteria = []
            
            if not criteria.get('interactions_sufficient'):
                missing_criteria.append(f"more practice (need {self.MIN_TEACHING_INTERACTIONS - interactions} more interactions)")
            
            if not criteria.get('objectives_coverage_met'):
                coverage = validation_details.get('objective_coverage_pct', 0)
                missing_criteria.append(f"better objective coverage (currently {coverage:.1f}%, need {self.MIN_OBJECTIVE_COVERAGE}%)")
            
            if not criteria.get('content_depth_sufficient'):
                depth = validation_details.get('content_depth_score', 0)
                missing_criteria.append(f"deeper understanding (score {depth:.2f}, need {self.MIN_CONTENT_DEPTH_SCORE})")
            
            if not criteria.get('teaching_time_sufficient'):
                time_spent = validation_details.get('teaching_time_minutes', 0)
                missing_criteria.append(f"more learning time (spent {time_spent:.1f} min, need {self.MIN_TEACHING_TIME_MINUTES} min)")
            
            # Create encouraging teaching response
            if missing_criteria:
                improvement_text = " and ".join(missing_criteria)
                response = f"Great progress so far, {student_name}! Before we move to the quiz, let's make sure you have a solid foundation in {topic}. You need {improvement_text}. Let me explain more about the key concepts to help you succeed."
            else:
                response = f"Excellent work, {student_name}! You're making great progress with {topic}. Let me continue teaching you the important concepts to ensure you're fully prepared for success."
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Teaching response generation error: {e}")
            return f"Let me continue teaching you about {topic} to ensure you have a solid understanding before we proceed."
    
    def get_teaching_progress_summary(self, session_data: Dict[str, Any], 
                                    context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive teaching progress summary for debugging and monitoring.
        """
        try:
            is_complete, reason, details = self.validate_teaching_completion(session_data, context)
            
            return {
                'teaching_complete': is_complete,
                'completion_reason': reason,
                'validation_details': details,
                'phase_consistency_enforced': True,
                'rules_engine_version': '1.0.0'
            }
            
        except Exception as e:
            logger.error(f"❌ Progress summary error: {e}")
            return {'error': str(e)}

# Global instance
teaching_rules_engine = TeachingRulesEngine()

def validate_teaching_completion(session_data: Dict[str, Any], 
                               context: Dict[str, Any]) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Convenience function for teaching completion validation.
    """
    return teaching_rules_engine.validate_teaching_completion(session_data, context)

def enforce_phase_consistency(current_phase: str, ai_response: str, 
                            session_data: Dict[str, Any], 
                            context: Dict[str, Any]) -> Tuple[str, str, str]:
    """
    Convenience function for phase consistency enforcement.
    """
    return teaching_rules_engine.enforce_teaching_phase_consistency(
        current_phase, ai_response, session_data, context
    )

def get_teaching_progress(session_data: Dict[str, Any], 
                        context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function for teaching progress summary.
    """
    return teaching_rules_engine.get_teaching_progress_summary(session_data, context)