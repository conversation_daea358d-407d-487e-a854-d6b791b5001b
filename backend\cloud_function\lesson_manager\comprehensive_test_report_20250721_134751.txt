================================================================================
COMPREHENSIVE END-TO-END LESSON SYSTEM TEST REPORT
================================================================================
Student: <PERSON> (andrea_ugono_33305)
Subject: Computing, Primary 5
Test Start: 2025-07-21T13:47:44.353769
Total Interactions: 16

📊 PERFORMANCE METRICS
----------------------------------------
Average Response Time: 0.00s (Target: <2s)
Average AI Quality: 0.0% (Target: >70%)
Max Response Time: 0.00s
Min Response Time: 0.00s

🔄 PHASE TRANSITIONS
----------------------------------------

🔍 INTERACTION-BY-INTERACTION ANALYSIS
----------------------------------------
Interaction 1 ❌
  Phase: smart_diagnostic_start
  Input: Hi! I'm ready to start learning about computers....
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 2 ❌
  Phase: smart_diagnostic_q1
  Input: A computer is a machine that helps us work and play games. I...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 3 ❌
  Phase: smart_diagnostic_q2
  Input: Software is like programs that make the computer do things. ...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 4 ❌
  Phase: smart_diagnostic_q3
  Input: Input devices help us put information into the computer, lik...
  Response: Session error: No session ID provided...
  Time: 0.0s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 5 ❌
  Phase: smart_diagnostic_q4
  Input: The CPU is like the brain of the computer that processes inf...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 6 ❌
  Phase: smart_diagnostic_q5
  Input: Memory stores information temporarily while the computer is ...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 7 ❌
  Phase: teaching_start_level_6
  Input: That makes sense! Can you tell me more about how computers p...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 8 ❌
  Phase: teaching
  Input: I understand now. What happens when we save a file?...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 9 ❌
  Phase: teaching
  Input: This is interesting! How do different input devices work?...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 10 ❌
  Phase: quiz_initiate
  Input: I'm ready for the quiz!...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 11 ❌
  Phase: quiz_questions
  Input: The CPU processes the instructions...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 12 ❌
  Phase: quiz_questions
  Input: RAM is temporary memory...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 13 ❌
  Phase: quiz_questions
  Input: The hard drive stores files permanently...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 14 ❌
  Phase: quiz_results
  Input: How did I do on the quiz?...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 15 ❌
  Phase: conclusion_summary
  Input: Can you summarize what I learned today?...
  Response: Session error: No session ID provided...
  Time: 0.001s, Quality: 0.0%
  ⚠️  Contains error message

Interaction 16 ❌
  Phase: completed
  Input: Thank you for the lesson!...
  Response: Session error: No session ID provided...
  Time: 0.0s, Quality: 0.0%
  ⚠️  Contains error message

✅ SUCCESS CRITERIA EVALUATION
----------------------------------------
complete_9_phases: ✅ PASS
zero_backward_transitions: ✅ PASS
target_response_times: ✅ PASS
target_ai_quality: ❌ FAIL
authentic_ai_content: ❌ FAIL

🎯 FINAL ASSESSMENT
----------------------------------------
❌ SOME SUCCESS CRITERIA NOT MET
⚠️  System requires additional fixes before deployment