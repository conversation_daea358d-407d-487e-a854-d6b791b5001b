#!/usr/bin/env python3
"""
Final Smart Diagnostic Test - Testing the complete 9-phase flow with real API calls
"""

import os
import sys
import time
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Tuple, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartDiagnosticTester:
    """Smart Diagnostic system tester using real API calls"""
    
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.test_results = {
            'test_start_time': datetime.now().isoformat(),
            'student_id': 'andrea_ugono_33305',
            'interactions': [],
            'phase_transitions': [],
            'performance_metrics': {},
            'ai_quality_scores': [],
            'errors': [],
            'success_criteria': {
                'complete_smart_diagnostic': False,
                'zero_backward_transitions': True,
                'target_response_times': True,
                'target_ai_quality': True,
                'authentic_ai_content': True
            }
        }
        
        # Smart diagnostic responses
        self.diagnostic_responses = [
            "A computer is a machine that helps us work and play games. It has a screen and keyboard.",
            "Software is like programs that make the computer do things. Hardware is the physical parts you can touch.",
            "Input devices help us put information into the computer, like keyboard and mouse. Output devices show us results like monitor and speakers.",
            "The CPU is like the brain of the computer that processes information and makes calculations.",
            "Memory stores information temporarily while the computer is working, and storage keeps files permanently."
        ]
    
    def start_lesson_manager_server(self) -> bool:
        """Start the lesson manager server"""
        try:
            # Check if server is already running
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Lesson manager server is already running")
                return True
        except:
            pass
        
        logger.info("🚀 Starting lesson manager server...")
        
        # Start server in background
        import subprocess
        import threading
        
        def run_server():
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            subprocess.run([sys.executable, "main.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        for i in range(30):  # Wait up to 30 seconds
            try:
                time.sleep(1)
                response = requests.get(f"{self.base_url}/api/health", timeout=2)
                if response.status_code == 200:
                    logger.info("✅ Lesson manager server started successfully")
                    return True
            except:
                continue
        
        logger.error("❌ Failed to start lesson manager server")
        return False
    
    def make_lesson_request(self, user_query: str, session_id: str = None) -> Dict:
        """Make a request to the lesson manager API"""
        try:
            payload = {
                "user_query": user_query,
                "student_id": "andrea_ugono_33305",
                "lesson_ref": "P5-ENT-046",
                "country": "Nigeria",
                "curriculum_name": "National Curriculum",
                "grade": "Primary 5",
                "level": "P5",
                "subject": "Entrepreneurship"
            }
            
            if session_id:
                payload["session_id"] = session_id
            
            response = requests.post(
                f"{self.base_url}/api/enhance-content",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def assess_ai_quality(self, response_text: str, phase: str) -> float:
        """Assess AI response quality"""
        score = 0.0
        
        # Content accuracy (25 points)
        if len(response_text) > 50:
            score += 10
        if any(word in response_text.lower() for word in ['andrea', 'computer', 'learn', 'question']):
            score += 10
        if 'error' not in response_text.lower():
            score += 5
            
        # Educational effectiveness (25 points)
        educational_indicators = ['question', 'explain', 'understand', 'example', 'concept']
        score += min(25, sum(5 for indicator in educational_indicators if indicator in response_text.lower()))
        
        # Student engagement (20 points)
        engagement_indicators = ['great', 'excellent', 'good', 'interesting', 'fantastic', 'welcome']
        score += min(20, sum(4 for indicator in engagement_indicators if indicator in response_text.lower()))
        
        # Phase appropriateness (15 points)
        if 'smart_diagnostic' in phase and any(word in response_text.lower() for word in ['question', 'tell me', 'what']):
            score += 15
        elif 'teaching' in phase and any(word in response_text.lower() for word in ['explain', 'learn', 'understand']):
            score += 15
            
        # Language clarity (15 points)
        if len(response_text.split()) > 10:
            score += 8
        if response_text.count('.') >= 2:
            score += 7
            
        return min(100.0, score)
    
    async def run_smart_diagnostic_test(self) -> bool:
        """Run the complete smart diagnostic test"""
        try:
            logger.info("🚀 Starting Smart Diagnostic Test")
            logger.info("=" * 80)
            
            # Start server
            if not self.start_lesson_manager_server():
                return False
            
            session_id = None
            current_phase = "smart_diagnostic_start"
            
            # Interaction 1: Initial greeting to start smart diagnostic
            logger.info("📝 Testing smart diagnostic start...")
            
            start_time = time.time()
            result = self.make_lesson_request("Hi! I'm ready to start learning about computers.")
            response_time = time.time() - start_time
            
            if "error" in result:
                logger.error(f"❌ Initial request failed: {result['error']}")
                return False
            
            session_id = result.get("session_id")
            response_text = result.get("ai_response", "")
            
            logger.info(f"✅ Initial response received in {response_time:.2f}s")
            logger.info(f"📄 Session ID: {session_id}")
            logger.info(f"📝 Response preview: {response_text[:200]}...")
            
            # Assess AI quality
            ai_quality = self.assess_ai_quality(response_text, current_phase)
            self.test_results['ai_quality_scores'].append(ai_quality)
            
            # Record interaction
            interaction_data = {
                'interaction_num': 1,
                'timestamp': datetime.now().isoformat(),
                'user_input': "Hi! I'm ready to start learning about computers.",
                'current_phase': current_phase,
                'response_text': response_text,
                'response_time_seconds': round(response_time, 3),
                'ai_quality_score': round(ai_quality, 1),
                'has_error': 'error' in response_text.lower(),
                'is_authentic_ai': len(response_text) > 50 and 'error' not in response_text.lower()
            }
            self.test_results['interactions'].append(interaction_data)
            
            # Interactions 2-6: Answer the 5 smart diagnostic questions
            for q_num in range(5):
                logger.info(f"📝 Testing smart diagnostic Q{q_num + 1}...")
                
                start_time = time.time()
                result = self.make_lesson_request(self.diagnostic_responses[q_num], session_id)
                response_time = time.time() - start_time
                
                if "error" in result:
                    logger.error(f"❌ Q{q_num + 1} request failed: {result['error']}")
                    continue
                
                response_text = result.get("ai_response", "")
                
                logger.info(f"✅ Q{q_num + 1} response received in {response_time:.2f}s")
                logger.info(f"📝 Q{q_num + 1} input: {self.diagnostic_responses[q_num][:50]}...")
                logger.info(f"📝 Q{q_num + 1} response preview: {response_text[:200]}...")
                
                # Assess AI quality
                ai_quality = self.assess_ai_quality(response_text, f"smart_diagnostic_q{q_num + 1}")
                self.test_results['ai_quality_scores'].append(ai_quality)
                
                # Record interaction
                interaction_data = {
                    'interaction_num': q_num + 2,
                    'timestamp': datetime.now().isoformat(),
                    'user_input': self.diagnostic_responses[q_num],
                    'current_phase': f"smart_diagnostic_q{q_num + 1}",
                    'response_text': response_text,
                    'response_time_seconds': round(response_time, 3),
                    'ai_quality_score': round(ai_quality, 1),
                    'has_error': 'error' in response_text.lower(),
                    'is_authentic_ai': len(response_text) > 50 and 'error' not in response_text.lower()
                }
                self.test_results['interactions'].append(interaction_data)
                
                # Check for diagnostic completion
                if "diagnostic" in response_text.lower() and "complete" in response_text.lower():
                    logger.info("🎯 Smart diagnostic completion detected!")
                    self.test_results['success_criteria']['complete_smart_diagnostic'] = True
                    break
            
            # Calculate performance metrics
            response_times = [i['response_time_seconds'] for i in self.test_results['interactions']]
            ai_qualities = self.test_results['ai_quality_scores']
            
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            avg_ai_quality = sum(ai_qualities) / len(ai_qualities) if ai_qualities else 0
            
            self.test_results['performance_metrics'] = {
                'avg_response_time': avg_response_time,
                'avg_ai_quality': avg_ai_quality,
                'max_response_time': max(response_times) if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0
            }
            
            # Check success criteria
            if avg_response_time <= 2.0:
                self.test_results['success_criteria']['target_response_times'] = True
            else:
                self.test_results['success_criteria']['target_response_times'] = False
                
            if avg_ai_quality >= 70.0:
                self.test_results['success_criteria']['target_ai_quality'] = True
            else:
                self.test_results['success_criteria']['target_ai_quality'] = False
            
            # Check for authentic AI content
            authentic_responses = sum(1 for i in self.test_results['interactions'] if i['is_authentic_ai'])
            if authentic_responses == len(self.test_results['interactions']):
                self.test_results['success_criteria']['authentic_ai_content'] = True
            else:
                self.test_results['success_criteria']['authentic_ai_content'] = False
            
            # Generate report
            self.generate_report()
            
            return all(self.test_results['success_criteria'].values())
            
        except Exception as e:
            logger.error(f"❌ Smart diagnostic test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def generate_report(self):
        """Generate test report"""
        print("\n" + "=" * 80)
        print("SMART DIAGNOSTIC SYSTEM TEST REPORT")
        print("=" * 80)
        print(f"Student: Andrea Ugono (andrea_ugono_33305)")
        print(f"Test Start: {self.test_results['test_start_time']}")
        print(f"Total Interactions: {len(self.test_results['interactions'])}")
        print("")
        
        # Performance Metrics
        metrics = self.test_results['performance_metrics']
        print("📊 PERFORMANCE METRICS")
        print("-" * 40)
        print(f"Average Response Time: {metrics['avg_response_time']:.2f}s (Target: <2s)")
        print(f"Average AI Quality: {metrics['avg_ai_quality']:.1f}% (Target: >70%)")
        print(f"Max Response Time: {metrics['max_response_time']:.2f}s")
        print(f"Min Response Time: {metrics['min_response_time']:.2f}s")
        print("")
        
        # Success Criteria
        print("✅ SUCCESS CRITERIA EVALUATION")
        print("-" * 40)
        criteria = self.test_results['success_criteria']
        for criterion, passed in criteria.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{criterion}: {status}")
        print("")
        
        # Final Assessment
        all_passed = all(criteria.values())
        print("🎯 FINAL ASSESSMENT")
        print("-" * 40)
        if all_passed:
            print("✅ ALL SUCCESS CRITERIA MET")
            print("✅ Smart Diagnostic system ready for production")
        else:
            print("❌ SOME SUCCESS CRITERIA NOT MET")
            print("⚠️  System requires additional fixes")

async def main():
    """Main test execution"""
    tester = SmartDiagnosticTester()
    success = await tester.run_smart_diagnostic_test()
    
    if success:
        print("\n🎉 SMART DIAGNOSTIC TEST: SUCCESS!")
        print("✅ All success criteria met")
    else:
        print("\n⚠️  SMART DIAGNOSTIC TEST: ISSUES DETECTED")
        print("❌ Some success criteria not met")
    
    return success

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
