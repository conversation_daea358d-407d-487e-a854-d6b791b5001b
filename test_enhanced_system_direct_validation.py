#!/usr/bin/env python3
"""
Direct Enhanced System Validation Test
=====================================

This test specifically validates the enhanced teaching completion system
by testing the teaching phase directly and checking for the enhanced features.
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_teaching_validation():
    """Test the enhanced teaching validation system directly"""
    
    url = "http://localhost:5000/api/enhance-content"
    
    # Test data that should trigger the enhanced teaching validation
    test_data = {
        "session_id": f"enhanced_test_{int(time.time())}",
        "lesson_ref": "P5_MAT_180",
        "subject": "mathematics",
        "student_input": "I understand fractions now. Can we move to the quiz?",
        "current_phase": "teaching",  # Force teaching phase
        "teaching_interactions": 3,   # Below adaptive minimum
        "objectives_covered": 2,      # Below 100%
        "total_objectives": 5,
        "content_depth_score": 0.6,   # Below threshold
        "grade": "primary_5",
        "teaching_level": 5,
        "lesson_start_time": time.time() - 600,  # 10 minutes ago
        "teaching_start_time": time.time() - 300  # 5 minutes ago
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-Testing-Mode': 'true',
        'X-Student-ID': f"enhanced_student_{int(time.time())}"
    }
    
    print("🧪 TESTING: Enhanced Teaching Completion Validation")
    print("=" * 70)
    print(f"Testing with incomplete teaching scenario:")
    print(f"  - Teaching Interactions: {test_data['teaching_interactions']}")
    print(f"  - Objectives Covered: {test_data['objectives_covered']}/{test_data['total_objectives']}")
    print(f"  - Content Depth: {test_data['content_depth_score']}")
    print(f"  - Student requesting quiz transition")
    print("=" * 70)
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Print the full response for analysis
            print("\nFULL RESPONSE:")
            print(json.dumps(response_data, indent=2, default=str))
            
            # Check for enhanced system indicators in the response
            response_str = json.dumps(response_data, default=str).lower()
            
            # Enhanced teaching completion indicators
            enhanced_indicators = {
                'adaptive_requirements': 'adaptive requirements' in response_str,
                'teaching_validation': 'teaching validation' in response_str or 'validate_teaching_completion' in response_str,
                'primary_driver': 'primary driver' in response_str or '100% objectives' in response_str,
                'teaching_incomplete': 'teaching incomplete' in response_str or 'continue instruction' in response_str,
                'guardrails_applied': 'guardrails applied' in response_str or 'guardrail' in response_str,
                'intelligent_blocking': 'quiz request blocked' in response_str or 'quiz transition blocked' in response_str,
                'completion_reason': 'completion_reason' in response_str or 'completion reason' in response_str,
                'enhanced_validation': 'enhanced' in response_str and 'validation' in response_str
            }
            
            print("\n" + "=" * 70)
            print("ENHANCED SYSTEM INDICATORS ANALYSIS:")
            print("=" * 70)
            
            found_count = 0
            for indicator, found in enhanced_indicators.items():
                status = "✅ FOUND" if found else "❌ NOT FOUND"
                print(f"{status}: {indicator}")
                if found:
                    found_count += 1
            
            print(f"\nTOTAL INDICATORS FOUND: {found_count}/{len(enhanced_indicators)}")
            
            # Analyze the AI response for teaching continuation
            ai_response = response_data.get('data', {}).get('enhanced_content', '')
            
            print(f"\n" + "=" * 70)
            print("AI RESPONSE ANALYSIS:")
            print("=" * 70)
            print(f"AI Response: {ai_response}")
            
            # Check if the system is continuing teaching instead of allowing quiz
            teaching_continuation_indicators = [
                'let\'s continue',
                'more practice',
                'understand better',
                'before we move',
                'need to cover',
                'let me explain',
                'practice more'
            ]
            
            quiz_blocking_indicators = [
                'not ready',
                'need more',
                'continue learning',
                'before quiz',
                'more preparation'
            ]
            
            teaching_continues = any(indicator in ai_response.lower() for indicator in teaching_continuation_indicators)
            quiz_blocked = any(indicator in ai_response.lower() for indicator in quiz_blocking_indicators)
            
            print(f"Teaching Continuation Detected: {'✅ YES' if teaching_continues else '❌ NO'}")
            print(f"Quiz Blocking Detected: {'✅ YES' if quiz_blocked else '❌ NO'}")
            
            # Overall assessment
            print(f"\n" + "=" * 70)
            print("OVERALL ASSESSMENT:")
            print("=" * 70)
            
            if found_count >= 4:
                print("🎉 ENHANCED SYSTEM FULLY OPERATIONAL!")
                print("   - Multiple enhanced indicators detected")
                print("   - Teaching completion validation active")
                print("   - Intelligent guardrails working")
            elif found_count >= 2:
                print("✅ ENHANCED SYSTEM PARTIALLY ACTIVE")
                print("   - Some enhanced features detected")
                print("   - System improvements are working")
            elif teaching_continues or quiz_blocked:
                print("⚠️ BASIC TEACHING LOGIC ACTIVE")
                print("   - Teaching continuation detected")
                print("   - May be using standard validation")
            else:
                print("❌ ENHANCED SYSTEM NOT DETECTED")
                print("   - No clear enhanced indicators found")
                print("   - System may need attention")
            
            return found_count >= 2
            
        else:
            print("ERROR RESPONSE:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def main():
    """Main test execution"""
    print("🚀 STARTING: Enhanced Teaching Completion System Validation")
    print("=" * 70)
    
    success = test_enhanced_teaching_validation()
    
    print(f"\n🎯 FINAL RESULT: {'✅ SUCCESS' if success else '❌ NEEDS ATTENTION'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)