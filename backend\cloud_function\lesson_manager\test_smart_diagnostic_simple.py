#!/usr/bin/env python3
"""
Simple Smart Diagnostic Test to validate the system is working
"""

import os
import sys
import time
import asyncio
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_smart_diagnostic():
    """Test Smart Diagnostic system with a simple interaction"""
    try:
        logger.info("🚀 Starting Simple Smart Diagnostic Test")
        
        # Import Flask app and create application context
        from main import app, enhance_lesson_content
        
        # Create application context
        app_context = app.app_context()
        app_context.push()
        
        # Create test context for smart diagnostic start
        context = {
            'student_info': {
                'first_name': 'Andrea',
                'student_id': 'andrea_ugono_33305',
                'email': '<EMAIL>'
            },
            'subject': 'Computing',
            'topic': 'Basic Computer Concepts',
            'grade': 'Primary 5',
            'lesson_phase': 'smart_diagnostic_start',
            'current_phase': 'smart_diagnostic_start',
            'current_probing_level_number_from_state': 5,
            'module_name': 'Introduction to Computers',
            'key_concepts_str': 'computer hardware, software, input devices, output devices, CPU, memory',
            'interaction_count': 1,
            'diagnostic_completed_this_session': False,
            'smart_diagnostic': {
                'current_question': 1,
                'questions_completed': 0,
                'diagnostic_complete': False,
                'grade': 'Primary 5'
            }
        }
        
        logger.info("📝 Testing smart_diagnostic_start phase...")
        
        # Test first interaction
        start_time = time.time()
        response_text, state_updates, state_json = await enhance_lesson_content(
            user_query="Hi! I'm ready to start learning about computers.",
            chat_history=[],
            context=context,
            request_id="test_smart_diagnostic_1"
        )
        response_time = time.time() - start_time
        
        logger.info(f"✅ Response received in {response_time:.2f}s")
        logger.info(f"📄 Response length: {len(response_text)} characters")
        logger.info(f"🔄 State updates: {state_updates}")
        logger.info(f"📝 Response preview: {response_text[:200]}...")
        
        # Check for AI state update block
        has_state_block = "AI_STATE_UPDATE_BLOCK_START" in response_text
        logger.info(f"🎯 AI State Update Block Present: {has_state_block}")
        
        # Check for expected phase transition
        new_phase = state_updates.get('new_phase', 'unknown')
        logger.info(f"🔄 Phase transition: smart_diagnostic_start → {new_phase}")
        
        # Test Q1 interaction if we got the right phase
        if new_phase == 'smart_diagnostic_q1':
            logger.info("📝 Testing smart_diagnostic_q1 phase...")
            
            context['lesson_phase'] = 'smart_diagnostic_q1'
            context['current_phase'] = 'smart_diagnostic_q1'
            context['smart_diagnostic']['current_question'] = 1
            
            start_time = time.time()
            response_text_q1, state_updates_q1, _ = await enhance_lesson_content(
                user_query="A computer is a machine that helps us work and play games. It has a screen and keyboard.",
                chat_history=[],
                context=context,
                request_id="test_smart_diagnostic_q1"
            )
            response_time_q1 = time.time() - start_time
            
            logger.info(f"✅ Q1 Response received in {response_time_q1:.2f}s")
            logger.info(f"🔄 Q1 State updates: {state_updates_q1}")
            logger.info(f"📝 Q1 Response preview: {response_text_q1[:200]}...")
            
            new_phase_q1 = state_updates_q1.get('new_phase', 'unknown')
            logger.info(f"🔄 Q1 Phase transition: smart_diagnostic_q1 → {new_phase_q1}")
        
        logger.info("🎉 Simple Smart Diagnostic Test Complete!")
        
        # Cleanup
        app_context.pop()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    success = await test_smart_diagnostic()
    
    if success:
        print("\n🎉 SIMPLE TEST: SUCCESS!")
        print("✅ Smart Diagnostic system is working")
    else:
        print("\n⚠️  SIMPLE TEST: FAILED")
        print("❌ Smart Diagnostic system has issues")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
