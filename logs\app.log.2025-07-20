2025-07-20 12:57:02,562 - INFO - [main.py:71] - ============================================================
2025-07-20 12:57:02,569 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-20 12:57:02,570 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-20 12:57:02,570 - INFO - [main.py:74] - Platform: win32
2025-07-20 12:57:02,571 - INFO - [main.py:75] - ============================================================
2025-07-20 12:57:05,569 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-20 12:57:05,610 - INFO - [teaching_rules.py:42] - 🎓 Teaching Rules Engine initialized with comprehensive validation
2025-07-20 12:57:05,837 - INFO - [main.py:154] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-20 12:57:05,837 - INFO - [main.py:474] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-20 12:57:05,838 - INFO - [main.py:632] - ================================================================================
2025-07-20 12:57:05,839 - INFO - [main.py:633] - LESSON MANAGER BACKEND STARTING UP
2025-07-20 12:57:05,839 - INFO - [main.py:634] - ================================================================================
2025-07-20 12:57:05,839 - INFO - [main.py:635] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-20 12:57:05,840 - INFO - [main.py:636] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-20 12:57:05,840 - INFO - [main.py:637] - Log level: DEBUG
2025-07-20 12:57:05,840 - INFO - [main.py:638] - ================================================================================
2025-07-20 12:57:05,841 - INFO - [main.py:640] - Logging configuration complete with immediate console output
2025-07-20 12:57:05,841 - INFO - [main.py:641] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-20 12:57:05,844 - INFO - [main.py:1338] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-20 12:57:05,844 - INFO - [main.py:1345] - [OK] Enhanced state and auth managers initialized successfully
2025-07-20 12:57:05,846 - INFO - [main.py:1577] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-20 12:57:05,862 - INFO - [main.py:1606] - Phase transition fixes imported successfully
2025-07-20 12:57:05,866 - INFO - [main.py:5006] - Successfully imported utils functions
2025-07-20 12:57:05,870 - INFO - [main.py:5014] - Successfully imported extract_ai_state functions
2025-07-20 12:57:05,878 - INFO - [main.py:5464] - FLASK: Using unified Firebase initialization approach...
2025-07-20 12:57:05,881 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-20 12:57:05,882 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-20 12:57:05,882 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-20 12:57:05,882 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-20 12:57:05,960 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-20 12:57:05,962 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-20 12:57:06,699 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-20 12:57:07,192 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-20 12:57:07,956 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-20 12:57:07,957 - INFO - [main.py:5472] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-20 12:57:07,957 - INFO - [main.py:5562] - Gemini API will be initialized on first use (lazy loading).
2025-07-20 12:57:07,966 - INFO - [main.py:19032] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-20 12:57:07,967 - INFO - [main.py:19075] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-20 12:57:07,976 - INFO - [main.py:2065] - Successfully imported timetable_generator functions
2025-07-20 12:57:07,985 - INFO - [main.py:25203] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-20 12:57:08,068 - INFO - [main.py:25206] - Google Cloud Storage client initialized successfully.
2025-07-20 12:57:08,070 - INFO - [main.py:5595] - [fix_script] ✅ Session fallback-ba2e2454-3009-470f-bec8-76994014b39c completely cleared - will re-initialize with updated instructions
