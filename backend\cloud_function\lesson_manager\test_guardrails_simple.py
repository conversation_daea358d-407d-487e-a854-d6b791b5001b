#!/usr/bin/env python3
"""
Simple test to verify guardrails fix without full main.py initialization
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_guardrails_fix():
    """Test that backend instructions don't leak to student UI"""
    try:
        print("🧪 Testing Guardrails UI Leak Fix")
        
        # Import the guardrails system
        from intelligent_guardrails import IntelligentGuardrailsManager
        
        guardrails = IntelligentGuardrailsManager()
        
        # Create test context
        lesson_context = {
            'student_info': {'first_name': '<PERSON>'},
            'topic': 'Transformations'
        }
        
        # Test the student-friendly message generation
        print("\n📝 Testing student-friendly message generation...")
        
        for i in range(3):
            request_id = f"test_{i}"
            message = guardrails._generate_student_friendly_continuation_message(
                lesson_context, request_id
            )
            print(f"✅ Message {i+1}: {message}")
            
            # Check for backend instruction leakage
            if "continue teaching until all completion criteria are met" in message.lower():
                print(f"❌ BACKEND INSTRUCTION LEAKED!")
                return False
            
            if "<PERSON>" in message and "Transformations" in message:
                print(f"   ✅ Contains student name and topic")
            else:
                print(f"   ⚠️ Missing student name or topic")
        
        print("\n🎉 GUARDRAILS FIX TEST COMPLETED SUCCESSFULLY")
        print("✅ No backend instructions leaked to student messages")
        print("✅ Student-friendly messages generated correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("🚀 Starting Simple Guardrails Test")
    print("=" * 60)
    
    success = test_guardrails_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TEST PASSED!")
        print("✅ Backend instruction leak issue has been fixed")
        print("✅ Students will see encouraging messages instead of backend instructions")
    else:
        print("❌ TEST FAILED!")
        print("⚠️ Backend instructions may still be leaking to student UI")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
