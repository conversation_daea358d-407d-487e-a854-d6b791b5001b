#!/usr/bin/env python3
"""
Test to verify that backend instruction messages are not leaking to student UI
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_guardrails_ui_leak_fix():
    """Test that backend instructions don't leak to student UI"""
    try:
        logger.info("🧪 Testing Guardrails UI Leak Fix")
        
        # Import the guardrails system
        from intelligent_guardrails import apply_intelligent_guardrails
        
        # Create test context that would trigger blocking violations
        lesson_context = {
            'student_info': {
                'first_name': '<PERSON>',
                'student_id': 'andrea_ugono_33305'
            },
            'subject': 'Mathematics',
            'topic': 'Transformations',
            'grade': 'Primary 5',
            'objectives_tracking': {
                'total_objectives': 5,
                'covered_objectives': 2,  # Only 40% coverage - should trigger blocking
                'completion_percentage': 40.0
            }
        }
        
        session_data = {
            'objectives_tracking': lesson_context['objectives_tracking'],
            'assigned_level_for_teaching': 7,
            'teaching_interactions': 8,
            'elapsed_minutes': 15.0
        }
        
        # Test AI response that tries to transition to quiz (should be blocked)
        ai_response_with_quiz = """
        Great work on transformations, Andrea! You've learned about translation and reflection. 
        Now I think you're ready for a quiz to test your understanding. Let's see how well you can apply these concepts!
        """
        
        # Apply guardrails (teaching is NOT complete due to low objective coverage)
        teaching_truly_complete = False  # This should trigger blocking violations
        
        is_valid, enhanced_response, violation_details = apply_intelligent_guardrails(
            ai_response=ai_response_with_quiz,
            lesson_context=lesson_context,
            session_data=session_data,
            teaching_truly_complete=teaching_truly_complete,
            request_id="test_ui_leak_fix"
        )
        
        logger.info(f"✅ Guardrails applied successfully")
        logger.info(f"📊 Valid: {is_valid}")
        logger.info(f"📊 Violations: {len(violation_details)}")
        logger.info(f"📝 Enhanced response length: {len(enhanced_response)}")
        
        # Check for backend instruction leakage
        backend_phrases = [
            "Continue teaching until all completion criteria are met",
            "completion criteria",
            "backend instruction",
            "teaching not complete"
        ]
        
        leaked_phrases = []
        for phrase in backend_phrases:
            if phrase.lower() in enhanced_response.lower():
                leaked_phrases.append(phrase)
        
        if leaked_phrases:
            logger.error(f"❌ BACKEND INSTRUCTION LEAK DETECTED!")
            logger.error(f"   Leaked phrases: {leaked_phrases}")
            logger.error(f"   Enhanced response: {enhanced_response}")
            return False
        else:
            logger.info(f"✅ NO BACKEND INSTRUCTION LEAKAGE DETECTED")
            
        # Check that student-friendly message is present
        student_friendly_indicators = [
            "Andrea",  # Student name should be used
            "Great",   # Encouraging language
            "explore", # Learning-focused language
            "practice", # Educational terms
            "understand" # Learning objectives
        ]
        
        found_indicators = []
        for indicator in student_friendly_indicators:
            if indicator.lower() in enhanced_response.lower():
                found_indicators.append(indicator)
        
        if len(found_indicators) >= 2:
            logger.info(f"✅ STUDENT-FRIENDLY CONTENT DETECTED: {found_indicators}")
        else:
            logger.warning(f"⚠️ Limited student-friendly content: {found_indicators}")
        
        # Display the enhanced response for manual review
        logger.info(f"\n📝 ENHANCED RESPONSE FOR MANUAL REVIEW:")
        logger.info(f"{'='*60}")
        logger.info(enhanced_response)
        logger.info(f"{'='*60}")
        
        # Check that quiz transition was blocked
        if not is_valid:
            logger.info(f"✅ QUIZ TRANSITION CORRECTLY BLOCKED")
        else:
            logger.warning(f"⚠️ Quiz transition was not blocked (unexpected)")
        
        # Verify violation details are for internal use only
        for violation in violation_details:
            if "Continue teaching until all completion criteria are met" in violation.get('suggestion', ''):
                logger.info(f"✅ BACKEND INSTRUCTION FOUND IN VIOLATION DETAILS (INTERNAL USE)")
                logger.info(f"   Rule: {violation.get('rule_id')}")
                logger.info(f"   Suggestion: {violation.get('suggestion')}")
        
        logger.info(f"🎉 GUARDRAILS UI LEAK FIX TEST COMPLETED SUCCESSFULLY")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_student_friendly_continuation():
    """Test the student-friendly continuation message generation"""
    try:
        logger.info("🧪 Testing Student-Friendly Continuation Messages")
        
        from intelligent_guardrails import IntelligentGuardrailsManager
        
        guardrails = IntelligentGuardrailsManager()
        
        lesson_context = {
            'student_info': {'first_name': 'Andrea'},
            'topic': 'Transformations'
        }
        
        # Test multiple request IDs to see different messages
        for i in range(3):
            request_id = f"test_continuation_{i}"
            message = guardrails._generate_student_friendly_continuation_message(
                lesson_context, request_id
            )
            
            logger.info(f"✅ Message {i+1}: {message}")
            
            # Verify message contains student name and topic
            if 'Andrea' in message and 'Transformations' in message:
                logger.info(f"   ✅ Contains student name and topic")
            else:
                logger.warning(f"   ⚠️ Missing student name or topic")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Student-friendly test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Starting Guardrails UI Leak Fix Tests")
    logger.info("=" * 80)
    
    test1_success = test_guardrails_ui_leak_fix()
    test2_success = test_student_friendly_continuation()
    
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    if test1_success:
        logger.info("✅ Guardrails UI Leak Fix: PASSED")
    else:
        logger.error("❌ Guardrails UI Leak Fix: FAILED")
    
    if test2_success:
        logger.info("✅ Student-Friendly Messages: PASSED")
    else:
        logger.error("❌ Student-Friendly Messages: FAILED")
    
    overall_success = test1_success and test2_success
    
    if overall_success:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("✅ Backend instructions will no longer leak to student UI")
        logger.info("✅ Students will see encouraging, friendly messages instead")
    else:
        logger.error("\n❌ SOME TESTS FAILED!")
        logger.error("⚠️ Additional fixes may be needed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
