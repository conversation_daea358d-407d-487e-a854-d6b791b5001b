#!/usr/bin/env python3
"""
Comprehensive End-to-End Lesson System Test for Andrea (andrea_ugono_33305)
Tests complete 9-phase progression with real Firebase authentication and granular analysis.
"""

import os
import sys
import time
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Tuple, Any
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'end_to_end_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class LessonSystemTester:
    """Comprehensive lesson system tester with granular analysis"""
    
    def __init__(self):
        self.test_results = {
            'test_start_time': datetime.now().isoformat(),
            'student_id': 'andrea_ugono_33305',
            'interactions': [],
            'phase_transitions': [],
            'performance_metrics': {},
            'ai_quality_scores': [],
            'errors': [],
            'success_criteria': {
                'complete_9_phases': False,
                'zero_backward_transitions': True,
                'target_response_times': True,
                'target_ai_quality': True,
                'authentic_ai_content': True
            }
        }
        
        # Expected phase sequence for Smart 5-Question Diagnostic System
        self.expected_phases = [
            'smart_diagnostic_start',
            'smart_diagnostic_q1',
            'smart_diagnostic_q2',
            'smart_diagnostic_q3',
            'smart_diagnostic_q4',
            'smart_diagnostic_q5',
            'teaching_start_level_4',  # Level 4 (if student scored 0-1/5)
            'teaching_start_level_5',  # Level 5 (if student scored 2-4/5)
            'teaching_start_level_6',  # Level 6 (if student scored 5/5)
            'teaching',
            'quiz_initiate',
            'quiz_questions',
            'quiz_results',
            'conclusion_summary',
            'final_assessment_pending',
            'completed'
        ]
        
        # Realistic student responses for diagnostic questions
        self.diagnostic_responses = [
            "A computer is a machine that helps us work and play games. It has a screen and keyboard.",
            "Software is like programs that make the computer do things. Hardware is the physical parts you can touch.",
            "Input devices help us put information into the computer, like keyboard and mouse. Output devices show us results like monitor and speakers.",
            "The CPU is like the brain of the computer that processes information and makes calculations.",
            "Memory stores information temporarily while the computer is working, and storage keeps files permanently."
        ]
        
        # Teaching and quiz responses
        self.teaching_responses = [
            "That makes sense! Can you tell me more about how computers process information?",
            "I understand now. What happens when we save a file?",
            "This is interesting! How do different input devices work?"
        ]
        
        self.quiz_responses = [
            "The CPU processes the instructions",
            "RAM is temporary memory",
            "The hard drive stores files permanently"
        ]
        
    async def setup_test_environment(self) -> bool:
        """Setup test environment with real Firebase authentication"""
        try:
            logger.info("🔧 Setting up test environment...")
            
            # Load environment variables
            load_dotenv()
            
            # Import Flask app and create application context
            from main import app, enhance_lesson_content
            self.app = app
            self.enhance_lesson_content = enhance_lesson_content
            
            # Create application context
            self.app_context = self.app.app_context()
            self.app_context.push()
            
            logger.info("✅ Test environment setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test environment setup failed: {e}")
            self.test_results['errors'].append(f"Setup failed: {e}")
            return False
    
    def create_test_context(self, phase: str, interaction_count: int, student_answers: Dict = None) -> Dict:
        """Create test context for lesson system calls with Smart Diagnostic support"""
        return {
            'student_info': {
                'first_name': 'Andrea',
                'student_id': 'andrea_ugono_33305',
                'email': '<EMAIL>'
            },
            'subject': 'Computing',
            'topic': 'Basic Computer Concepts',
            'grade': 'Primary 5',
            'lesson_phase': phase,
            'current_phase': phase,  # Add current_phase for Smart Diagnostic
            'current_probing_level_number_from_state': 5,
            'current_question_index_from_state': len(student_answers) if student_answers else 0,
            'student_answers_for_probing_level_from_state': student_answers or {},
            'module_name': 'Introduction to Computers',
            'key_concepts_str': 'computer hardware, software, input devices, output devices, CPU, memory',
            'interaction_count': interaction_count,
            'diagnostic_completed_this_session': False,
            'assigned_level_for_teaching': None,
            # Smart Diagnostic state
            'smart_diagnostic': {
                'current_question': 1,
                'questions_completed': 0,
                'diagnostic_complete': False,
                'q1_score': None, 'q1_level': None,
                'q2_score': None, 'q2_level': None,
                'q3_score': None, 'q3_level': None,
                'q4_score': None, 'q4_level': None,
                'q5_score': None, 'q5_level': None,
                'grade': 'Primary 5'
            }
        }
    
    def assess_ai_quality(self, response_text: str, phase: str, user_input: str) -> float:
        """Assess AI response quality using multi-criteria scoring"""
        score = 0.0
        max_score = 100.0
        
        # Content accuracy (25 points)
        if len(response_text) > 50:
            score += 10
        if any(word in response_text.lower() for word in ['andrea', 'computer', 'learn']):
            score += 10
        if 'error' not in response_text.lower():
            score += 5
            
        # Educational effectiveness (25 points)
        educational_indicators = ['question', 'explain', 'understand', 'example', 'concept']
        score += min(25, sum(5 for indicator in educational_indicators if indicator in response_text.lower()))
        
        # Student engagement (20 points)
        engagement_indicators = ['great', 'excellent', 'good', 'interesting', 'fantastic']
        score += min(20, sum(4 for indicator in engagement_indicators if indicator in response_text.lower()))
        
        # Phase appropriateness (15 points)
        if ('diagnostic' in phase or 'smart_diagnostic' in phase) and any(word in response_text.lower() for word in ['question', 'tell me', 'what']):
            score += 15
        elif 'teaching' in phase and any(word in response_text.lower() for word in ['explain', 'learn', 'understand']):
            score += 15
        elif 'quiz' in phase and any(word in response_text.lower() for word in ['quiz', 'test', 'question']):
            score += 15
            
        # Language clarity (15 points)
        if len(response_text.split()) > 10:
            score += 8
        if response_text.count('.') >= 2:
            score += 7
            
        return min(100.0, score)
    
    async def execute_interaction(self, user_input: str, context: Dict, interaction_num: int) -> Tuple[str, Dict, float, float]:
        """Execute a single interaction and measure performance"""
        start_time = time.time()
        
        try:
            # CRITICAL DEBUG: Log the context being passed to the function
            logger.info(f"🔍 TEST DEBUG: Calling enhance_lesson_content with context lesson_phase='{context.get('lesson_phase')}' for interaction {interaction_num}")

            # Call enhance_lesson_content from imported function (async function)
            response_text, state_updates, state_json = await self.enhance_lesson_content(
                user_query=user_input,
                chat_history=[],
                context=context,
                request_id=f"test_interaction_{interaction_num}"
            )
            
            response_time = time.time() - start_time
            
            # Assess AI quality
            ai_quality = self.assess_ai_quality(response_text, context['lesson_phase'], user_input)
            
            # Log interaction details
            interaction_data = {
                'interaction_num': interaction_num,
                'timestamp': datetime.now().isoformat(),
                'user_input': user_input,
                'current_phase': context['lesson_phase'],
                'response_text': response_text,
                'response_length': len(response_text),
                'state_updates': state_updates,
                'response_time_seconds': round(response_time, 3),
                'ai_quality_score': round(ai_quality, 1),
                'has_error': 'error' in response_text.lower(),
                'is_authentic_ai': len(response_text) > 50 and 'error' not in response_text.lower()
            }
            
            self.test_results['interactions'].append(interaction_data)
            self.test_results['ai_quality_scores'].append(ai_quality)
            
            # Check for phase transition
            new_phase = state_updates.get('new_phase', context['lesson_phase'])
            if new_phase != context['lesson_phase']:
                transition_data = {
                    'interaction_num': interaction_num,
                    'from_phase': context['lesson_phase'],
                    'to_phase': new_phase,
                    'is_forward': self.is_forward_transition(context['lesson_phase'], new_phase),
                    'timestamp': datetime.now().isoformat()
                }
                self.test_results['phase_transitions'].append(transition_data)
                
                # Check for backward transitions
                if not transition_data['is_forward']:
                    self.test_results['success_criteria']['zero_backward_transitions'] = False
                    self.test_results['errors'].append(f"Backward transition detected: {context['lesson_phase']} → {new_phase}")
            
            # Check performance criteria
            if response_time > 2.0:
                self.test_results['success_criteria']['target_response_times'] = False
                
            if ai_quality < 70.0:
                self.test_results['success_criteria']['target_ai_quality'] = False
                
            if 'error' in response_text.lower() or len(response_text) < 50:
                self.test_results['success_criteria']['authentic_ai_content'] = False
            
            logger.info(f"✅ Interaction {interaction_num}: {response_time:.2f}s, Quality: {ai_quality:.1f}%, Phase: {context['lesson_phase']} → {new_phase}")
            
            return response_text, state_updates, response_time, ai_quality
            
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"Interaction {interaction_num} failed: {e}"
            logger.error(f"❌ {error_msg}")
            self.test_results['errors'].append(error_msg)
            return f"Error: {e}", {}, response_time, 0.0
    
    def is_forward_transition(self, from_phase: str, to_phase: str) -> bool:
        """Check if transition is forward-only"""
        try:
            from_idx = self.expected_phases.index(from_phase) if from_phase in self.expected_phases else -1
            to_idx = self.expected_phases.index(to_phase) if to_phase in self.expected_phases else -1
            return to_idx > from_idx
        except:
            return True  # Allow unknown transitions

    async def run_diagnostic_phase(self) -> Tuple[str, int]:
        """Run complete Smart 5-Question Diagnostic phase and return final phase and assigned level"""
        logger.info("🧪 Starting Smart 5-Question Diagnostic Phase Testing...")

        current_phase = 'smart_diagnostic_start'
        student_answers = {}
        interaction_count = 0

        # Interaction 1: Initial smart diagnostic start
        interaction_count += 1
        context = self.create_test_context(current_phase, interaction_count, student_answers)
        context['smart_diagnostic'] = {
            'current_question': 1,
            'questions_completed': 0,
            'diagnostic_complete': False
        }

        response, state_updates, response_time, ai_quality = await self.execute_interaction(
            "Hi! I'm ready to start learning about computers.",
            context,
            interaction_count
        )

        current_phase = state_updates.get('new_phase', current_phase)
        logger.info(f"   🎯 After initial greeting: {current_phase}")

        # Interactions 2-6: Answer 5 smart diagnostic questions (Q1-Q5)
        for q_num in range(5):
            if q_num < len(self.diagnostic_responses):
                interaction_count += 1

                # Update smart diagnostic state
                context['smart_diagnostic']['current_question'] = q_num + 1
                context['smart_diagnostic']['questions_completed'] = q_num
                context[f'smart_diagnostic_q{q_num + 1}_answer'] = self.diagnostic_responses[q_num]

                # Update context for current question phase
                expected_phase = f'smart_diagnostic_q{q_num + 1}'
                context = self.create_test_context(expected_phase, interaction_count, student_answers)
                context['smart_diagnostic'] = {
                    'current_question': q_num + 1,
                    'questions_completed': q_num,
                    'diagnostic_complete': False
                }

                response, state_updates, response_time, ai_quality = await self.execute_interaction(
                    self.diagnostic_responses[q_num],
                    context,
                    interaction_count
                )

                current_phase = state_updates.get('new_phase', current_phase)

                logger.info(f"   📝 Smart Diagnostic Q{q_num + 1}: {self.diagnostic_responses[q_num][:50]}...")
                logger.info(f"   🎯 Phase after Q{q_num + 1}: {current_phase}")

        # Determine assigned level based on Smart Diagnostic scoring
        substantial_answers = sum(1 for answer in self.diagnostic_responses if len(answer) > 10)

        if substantial_answers >= 5:  # 5/5 = +1 level (from base level 5 to 6)
            assigned_level = 6
        elif substantial_answers >= 4:  # 4/5 = same level
            assigned_level = 5
        elif substantial_answers >= 2:  # 2-3/5 = same level
            assigned_level = 5
        else:  # 0-1/5 = -1 level (from base level 5 to 4)
            assigned_level = 4

        logger.info(f"🎯 Smart Diagnostic Complete: {substantial_answers}/5 substantial answers → Level {assigned_level}")

        return current_phase, assigned_level

    async def run_teaching_phase(self, current_phase: str, assigned_level: int) -> str:
        """Run teaching phase"""
        logger.info(f"📚 Starting Teaching Phase (Level {assigned_level})...")

        interaction_count = len(self.test_results['interactions'])

        # Ensure we're in teaching_start_level_X phase
        if not current_phase.startswith('teaching_start_level_'):
            current_phase = f'teaching_start_level_{assigned_level}'

        # Teaching interactions
        for i, teaching_input in enumerate(self.teaching_responses):
            interaction_count += 1
            context = self.create_test_context(current_phase, interaction_count)
            context['assigned_level_for_teaching'] = assigned_level
            context['diagnostic_completed_this_session'] = True

            response, state_updates, response_time, ai_quality = await self.execute_interaction(
                teaching_input,
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', current_phase)

            # Transition to general teaching phase after teaching_start
            if current_phase.startswith('teaching_start_level_'):
                current_phase = 'teaching'

            logger.info(f"   📖 Teaching interaction {i + 1}: {teaching_input[:50]}...")

        return current_phase

    async def run_quiz_phase(self, current_phase: str) -> str:
        """Run quiz phase"""
        logger.info("🧩 Starting Quiz Phase...")

        interaction_count = len(self.test_results['interactions'])

        # Quiz initiation
        if current_phase != 'quiz_initiate':
            interaction_count += 1
            context = self.create_test_context('quiz_initiate', interaction_count)

            response, state_updates, response_time, ai_quality = await self.execute_interaction(
                "I'm ready for the quiz!",
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', 'quiz_questions')

        # Quiz questions
        for i, quiz_input in enumerate(self.quiz_responses):
            interaction_count += 1
            context = self.create_test_context('quiz_questions', interaction_count)

            response, state_updates, response_time, ai_quality = await self.execute_interaction(
                quiz_input,
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', current_phase)

            logger.info(f"   🧩 Quiz answer {i + 1}: {quiz_input}")

        # Quiz results
        if current_phase != 'quiz_results':
            interaction_count += 1
            context = self.create_test_context('quiz_results', interaction_count)

            response, state_updates, response_time, ai_quality = await self.execute_interaction(
                "How did I do on the quiz?",
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', current_phase)

        return current_phase

    async def run_completion_phase(self, current_phase: str) -> str:
        """Run completion phase"""
        logger.info("🎯 Starting Completion Phase...")

        interaction_count = len(self.test_results['interactions'])

        # Conclusion summary
        if current_phase != 'conclusion_summary':
            interaction_count += 1
            context = self.create_test_context('conclusion_summary', interaction_count)

            _, state_updates, _, _ = await self.execute_interaction(
                "Can you summarize what I learned today?",
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', 'final_assessment_pending')

        # Final assessment
        if current_phase != 'final_assessment_pending':
            interaction_count += 1
            context = self.create_test_context('final_assessment_pending', interaction_count)

            _, state_updates, _, _ = await self.execute_interaction(
                "I'm ready for the final assessment.",
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', 'completed')

        # Completion
        if current_phase != 'completed':
            interaction_count += 1
            context = self.create_test_context('completed', interaction_count)

            _, state_updates, _, _ = await self.execute_interaction(
                "Thank you for the lesson!",
                context,
                interaction_count
            )

            current_phase = state_updates.get('new_phase', 'completed')

        return current_phase

    def generate_comprehensive_report(self) -> str:
        """Generate comprehensive test report"""
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE END-TO-END LESSON SYSTEM TEST REPORT")
        report.append("=" * 80)
        report.append(f"Student: Andrea Ugono (andrea_ugono_33305)")
        report.append(f"Subject: Computing, Primary 5")
        report.append(f"Test Start: {self.test_results['test_start_time']}")
        report.append(f"Total Interactions: {len(self.test_results['interactions'])}")
        report.append("")

        # Performance Metrics
        response_times = [i['response_time_seconds'] for i in self.test_results['interactions']]
        ai_qualities = self.test_results['ai_quality_scores']

        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        avg_ai_quality = sum(ai_qualities) / len(ai_qualities) if ai_qualities else 0

        report.append("📊 PERFORMANCE METRICS")
        report.append("-" * 40)
        report.append(f"Average Response Time: {avg_response_time:.2f}s (Target: <2s)")
        report.append(f"Average AI Quality: {avg_ai_quality:.1f}% (Target: >70%)")
        if response_times:
            report.append(f"Max Response Time: {max(response_times):.2f}s")
            report.append(f"Min Response Time: {min(response_times):.2f}s")
        else:
            report.append("Max Response Time: N/A (no successful interactions)")
            report.append("Min Response Time: N/A (no successful interactions)")
        report.append("")

        # Phase Transitions
        report.append("🔄 PHASE TRANSITIONS")
        report.append("-" * 40)
        for transition in self.test_results['phase_transitions']:
            direction = "✅ FORWARD" if transition['is_forward'] else "❌ BACKWARD"
            report.append(f"Interaction {transition['interaction_num']}: {transition['from_phase']} → {transition['to_phase']} ({direction})")
        report.append("")

        # Interaction-by-Interaction Analysis
        report.append("🔍 INTERACTION-BY-INTERACTION ANALYSIS")
        report.append("-" * 40)
        for interaction in self.test_results['interactions']:
            status = "✅" if interaction['is_authentic_ai'] else "❌"
            report.append(f"Interaction {interaction['interaction_num']} {status}")
            report.append(f"  Phase: {interaction['current_phase']}")
            report.append(f"  Input: {interaction['user_input'][:60]}...")
            report.append(f"  Response: {interaction['response_text'][:100]}...")
            report.append(f"  Time: {interaction['response_time_seconds']}s, Quality: {interaction['ai_quality_score']}%")
            if interaction['has_error']:
                report.append(f"  ⚠️  Contains error message")
            report.append("")

        # Success Criteria
        report.append("✅ SUCCESS CRITERIA EVALUATION")
        report.append("-" * 40)
        criteria = self.test_results['success_criteria']
        for criterion, passed in criteria.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            report.append(f"{criterion}: {status}")
        report.append("")

        # Errors
        if self.test_results['errors']:
            report.append("❌ ERRORS DETECTED")
            report.append("-" * 40)
            for error in self.test_results['errors']:
                report.append(f"• {error}")
            report.append("")

        # Final Assessment
        all_passed = all(criteria.values())
        report.append("🎯 FINAL ASSESSMENT")
        report.append("-" * 40)
        if all_passed:
            report.append("✅ ALL SUCCESS CRITERIA MET")
            report.append("✅ Andrea will receive proper AI-generated educational content")
            report.append("✅ System ready for production deployment")
        else:
            report.append("❌ SOME SUCCESS CRITERIA NOT MET")
            report.append("⚠️  System requires additional fixes before deployment")

        return "\n".join(report)

    async def run_comprehensive_test(self) -> bool:
        """Run the complete comprehensive test"""
        logger.info("🚀 Starting Comprehensive End-to-End Lesson System Test")
        logger.info("=" * 80)

        try:
            # Setup test environment
            if not await self.setup_test_environment():
                return False

            # Run diagnostic phase
            current_phase, assigned_level = await self.run_diagnostic_phase()

            # Run teaching phase
            current_phase = await self.run_teaching_phase(current_phase, assigned_level)

            # Run quiz phase
            current_phase = await self.run_quiz_phase(current_phase)

            # Run completion phase
            current_phase = await self.run_completion_phase(current_phase)

            # Check if we completed all phases
            if current_phase == 'completed':
                self.test_results['success_criteria']['complete_9_phases'] = True
                logger.info("✅ Successfully completed all 9 phases!")
            else:
                logger.warning(f"⚠️  Test ended at phase: {current_phase}")

            # Generate and display report
            report = self.generate_comprehensive_report()
            print(report)

            # Save report to file
            report_filename = f'comprehensive_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)

            logger.info(f"📄 Comprehensive report saved to: {report_filename}")

            return all(self.test_results['success_criteria'].values())

        except Exception as e:
            logger.error(f"❌ Comprehensive test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # Cleanup
            if hasattr(self, 'app_context'):
                self.app_context.pop()


async def main():
    """Main test execution"""
    tester = LessonSystemTester()
    success = await tester.run_comprehensive_test()

    if success:
        print("\n🎉 COMPREHENSIVE TEST: SUCCESS!")
        print("✅ All success criteria met - Andrea will receive proper AI educational content")
    else:
        print("\n⚠️  COMPREHENSIVE TEST: ISSUES DETECTED")
        print("❌ Some success criteria not met - review report for details")

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
