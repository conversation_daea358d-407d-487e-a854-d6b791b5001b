#!/usr/bin/env python3
"""
Direct Enhanced Teaching Completion System Test
==============================================

This test directly validates the enhanced teaching completion system
by calling the /api/enhance-content endpoint with proper parameters.
"""

import requests
import json
import time
import logging
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectEnhancedSystemTest:
    def __init__(self):
        self.base_url = "http://localhost:5000/api"
        self.session_id = f"test_session_{int(time.time())}"
        self.student_id = f"test_student_{int(time.time())}"
        self.lesson_ref = "P5_MAT_180"
        self.test_results = {
            'server_health': False,
            'enhanced_validation_active': False,
            'intelligent_guardrails_working': False,
            'teaching_progression': False,
            'quiz_blocking': False,
            'handoff_logic': False
        }
    
    def make_request(self, endpoint, data):
        """Make HTTP request with proper headers"""
        try:
            url = f"{self.base_url}/{endpoint}"
            headers = {
                'Content-Type': 'application/json',
                'X-Testing-Mode': 'true',  # Bypass auth for testing
                'X-Student-ID': self.student_id
            }
            
            response = requests.post(url, json=data, headers=headers, timeout=30)
            logger.info(f"🌐 REQUEST: POST {endpoint} - Status: {response.status_code}")
            
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.error(f"   Error: {response.text}")
                return False, None
                
        except Exception as e:
            logger.error(f"   Exception: {e}")
            return False, None
    
    def test_server_health(self):
        """Test server health"""
        logger.info("\n🏥 TEST 1: Server Health Check")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Server Health: OK")
                self.test_results['server_health'] = True
                return True
        except Exception as e:
            logger.error(f"❌ Server Health: {e}")
        
        return False
    
    def test_enhanced_teaching_validation(self):
        """Test enhanced teaching completion validation"""
        logger.info("\n🎓 TEST 2: Enhanced Teaching Completion Validation")
        
        # Test with incomplete teaching scenario
        incomplete_data = {
            "session_id": self.session_id,
            "lesson_ref": self.lesson_ref,
            "subject": "mathematics",  # Required parameter
            "student_input": "I want to learn about mathematics",
            "current_phase": "teaching",
            "teaching_interactions": 2,  # Below minimum
            "objectives_covered": 1,     # Below 100%
            "total_objectives": 5,
            "content_depth_score": 0.5,  # Below threshold
            "grade": "primary_5",
            "teaching_level": 5
        }
        
        success, response = self.make_request("enhance-content", incomplete_data)
        
        if success and response:
            # Check for enhanced validation indicators
            response_str = json.dumps(response, default=str).lower()
            
            enhanced_indicators = [
                'adaptive requirements',
                'teaching validation results',
                'primary driver',
                '100% objectives',
                'teaching incomplete'
            ]
            
            found_indicators = [indicator for indicator in enhanced_indicators 
                              if indicator in response_str]
            
            if found_indicators:
                logger.info(f"✅ Enhanced Validation Active: Found {len(found_indicators)} indicators")
                logger.info(f"   Indicators: {', '.join(found_indicators)}")
                self.test_results['enhanced_validation_active'] = True
                return True
            else:
                logger.info("❌ Enhanced Validation: No indicators found")
                return False
        
        logger.error("❌ Enhanced Validation: Request failed")
        return False
    
    def test_intelligent_guardrails(self):
        """Test intelligent guardrails quiz blocking"""
        logger.info("\n🛡️ TEST 3: Intelligent Guardrails Quiz Blocking")
        
        # Test with quiz content when teaching is incomplete
        quiz_attempt_data = {
            "session_id": self.session_id,
            "lesson_ref": self.lesson_ref,
            "subject": "mathematics",  # Required parameter
            "student_input": "I'm ready for the quiz now. Let's start with question 1.",
            "current_phase": "teaching",
            "teaching_interactions": 3,  # Still incomplete
            "objectives_covered": 2,     # Still below 100%
            "total_objectives": 5,
            "grade": "primary_5"
        }
        
        success, response = self.make_request("enhance-content", quiz_attempt_data)
        
        if success and response:
            response_str = json.dumps(response, default=str).lower()
            
            # Check for guardrail blocking indicators
            blocking_indicators = [
                'quiz request blocked',
                'teaching incomplete',
                'guardrails applied',
                'quiz transition blocked',
                'continue teaching'
            ]
            
            found_blocking = [indicator for indicator in blocking_indicators 
                            if indicator in response_str]
            
            if found_blocking:
                logger.info(f"✅ Intelligent Guardrails Working: Found {len(found_blocking)} blocking indicators")
                logger.info(f"   Blocking: {', '.join(found_blocking)}")
                self.test_results['intelligent_guardrails_working'] = True
                self.test_results['quiz_blocking'] = True
                return True
            else:
                logger.info("❌ Intelligent Guardrails: No blocking detected")
                return False
        
        logger.error("❌ Intelligent Guardrails: Request failed")
        return False
    
    def test_teaching_progression(self):
        """Test teaching progression with enhanced system"""
        logger.info("\n📈 TEST 4: Teaching Progression with Enhanced System")
        
        # Simulate progressive teaching interactions
        for interaction in range(1, 8):
            logger.info(f"\n   📝 Teaching Interaction #{interaction}")
            
            # Gradually improve metrics
            objectives_covered = min(interaction, 5)  # Cap at 5 objectives
            teaching_interactions = interaction + 2   # Start with some base interactions
            content_depth = min(0.5 + (interaction * 0.1), 1.0)  # Gradually increase depth
            
            teaching_data = {
                "session_id": self.session_id,
                "lesson_ref": self.lesson_ref,
                "subject": "mathematics",  # Required parameter
                "student_input": f"Please explain more about this concept (Interaction {interaction})",
                "current_phase": "teaching",
                "teaching_interactions": teaching_interactions,
                "objectives_covered": objectives_covered,
                "total_objectives": 5,
                "content_depth_score": content_depth,
                "grade": "primary_5",
                "teaching_level": 5
            }
            
            success, response = self.make_request("enhance-content", teaching_data)
            
            if success and response:
                response_str = json.dumps(response, default=str).lower()
                
                # Check for completion indicators
                completion_indicators = [
                    'teaching complete',
                    'primary success',
                    '100% objectives',
                    'quiz initiate',
                    'handoff'
                ]
                
                found_completion = [indicator for indicator in completion_indicators 
                                  if indicator in response_str]
                
                logger.info(f"      Objectives: {objectives_covered}/5")
                logger.info(f"      Interactions: {teaching_interactions}")
                logger.info(f"      Content Depth: {content_depth:.2f}")
                logger.info(f"      Completion Indicators: {len(found_completion)}")
                
                if found_completion:
                    logger.info(f"✅ Teaching Progression: Completion detected after {interaction} interactions")
                    logger.info(f"   Completion: {', '.join(found_completion)}")
                    self.test_results['teaching_progression'] = True
                    
                    # Check for handoff indicators
                    handoff_indicators = [
                        'handoff',
                        'ai instructor handoff',
                        'quiz system',
                        'teaching phase complete'
                    ]
                    
                    found_handoff = [indicator for indicator in handoff_indicators 
                                   if indicator in response_str]
                    
                    if found_handoff:
                        logger.info(f"✅ AI Instructor Handoff: Detected handoff indicators")
                        logger.info(f"   Handoff: {', '.join(found_handoff)}")
                        self.test_results['handoff_logic'] = True
                    
                    return True
            
            # Small delay between interactions
            time.sleep(0.5)
        
        logger.info("❌ Teaching Progression: No completion detected")
        return False
    
    def run_comprehensive_test(self):
        """Run all tests"""
        logger.info("🚀 STARTING: Direct Enhanced Teaching Completion System Test")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        # Run tests
        tests = [
            ("Server Health", self.test_server_health),
            ("Enhanced Validation", self.test_enhanced_teaching_validation),
            ("Intelligent Guardrails", self.test_intelligent_guardrails),
            ("Teaching Progression", self.test_teaching_progression)
        ]
        
        for test_name, test_method in tests:
            try:
                logger.info(f"\n{'='*20} {test_name} {'='*20}")
                success = test_method()
                if not success and test_name == "Server Health":
                    logger.error("❌ Critical failure - stopping tests")
                    break
            except Exception as e:
                logger.error(f"❌ Exception in {test_name}: {e}")
        
        # Generate report
        self.generate_report(time.time() - start_time)
        
        # Determine overall success
        critical_tests = ['server_health', 'enhanced_validation_active']
        enhanced_tests = ['intelligent_guardrails_working', 'teaching_progression', 'handoff_logic']
        
        critical_passed = all(self.test_results[test] for test in critical_tests)
        enhanced_passed = sum(self.test_results[test] for test in enhanced_tests) >= 2
        
        return critical_passed and enhanced_passed
    
    def generate_report(self, duration):
        """Generate test report"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 DIRECT ENHANCED SYSTEM TEST RESULTS")
        logger.info("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        passed = sum(1 for result in self.test_results.values() if result)
        total = len(self.test_results)
        
        logger.info(f"\n📈 STATISTICS:")
        logger.info(f"   Tests Passed: {passed}/{total}")
        logger.info(f"   Success Rate: {(passed/total)*100:.1f}%")
        logger.info(f"   Duration: {duration:.2f} seconds")
        
        # Enhanced system status
        enhanced_features = [
            'enhanced_validation_active',
            'intelligent_guardrails_working',
            'teaching_progression',
            'handoff_logic'
        ]
        
        enhanced_working = sum(1 for feature in enhanced_features if self.test_results[feature])
        
        logger.info(f"\n🔧 ENHANCED SYSTEM STATUS:")
        logger.info(f"   Enhanced Features Working: {enhanced_working}/{len(enhanced_features)}")
        
        if enhanced_working >= 3:
            logger.info("   🎉 Enhanced teaching completion system is FULLY OPERATIONAL!")
        elif enhanced_working >= 2:
            logger.info("   ✅ Enhanced system is ACTIVE with most features working")
        elif enhanced_working >= 1:
            logger.info("   ⚠️ Enhanced system partially active")
        else:
            logger.info("   ❌ Enhanced system not detected")
        
        # Overall result
        if passed >= 4:
            logger.info("\n🎯 OVERALL RESULT: ✅ SUCCESS - Enhanced system working perfectly!")
        elif passed >= 2:
            logger.info("\n🎯 OVERALL RESULT: ⚠️ PARTIAL SUCCESS - Some features working")
        else:
            logger.info("\n🎯 OVERALL RESULT: ❌ FAILURE - System needs attention")

def main():
    """Main test execution"""
    test_runner = DirectEnhancedSystemTest()
    
    try:
        success = test_runner.run_comprehensive_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)