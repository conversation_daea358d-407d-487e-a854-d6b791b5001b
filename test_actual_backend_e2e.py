#!/usr/bin/env python3
"""
Test Actual Backend E2E with Synchronization Fixes

This test actually calls the backend API to verify that our synchronization fixes
and AI response filtering are working correctly in the real system.
"""

import sys
import os
import json
import time
import requests
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test configuration
BACKEND_URL = "http://localhost:5000"
TEST_SESSION_ID = f"actual_test_{int(time.time())}"

def test_actual_backend_lesson_flow():
    """
    Test the actual backend lesson flow to verify synchronization fixes
    """
    logger.info("🧪 Testing actual backend lesson flow with synchronization fixes...")
    
    try:
        # Test the lesson content endpoint with teaching phase
        teaching_payload = {
            "student_id": "test_student_sync",
            "lesson_ref": "P5_ENT_046",
            "student_name": "Test Student",
            "student_response": "I understand marketing helps people know about products. Can you tell me more about target audiences?",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 8,  # Below minimum of 10
                "objectives_covered": 1,
                "session_id": TEST_SESSION_ID
            }
        }
        
        logger.info("📤 Sending teaching interaction to backend...")
        response = requests.post(f"{BACKEND_URL}/test-lesson-content", json=teaching_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            logger.info("✅ Backend responded successfully")
            
            # Check for our synchronization fixes in the logs/response
            synchronization_indicators = [
                "🤖 Using AI Instructor objectives assessment",
                "🔄 Synchronized backend calculation with AI assessment",
                "⚠️ GUARDRAIL VIOLATIONS for quiz transition",
                "🚫 BLOCKING PREMATURE QUIZ SUGGESTION from AI",
                "✅ AI response filtered to continue teaching"
            ]
            
            found_indicators = []
            for indicator in synchronization_indicators:
                if indicator in response_text:
                    found_indicators.append(indicator)
            
            if found_indicators:
                logger.info("✅ Synchronization fixes are working!")
                for indicator in found_indicators:
                    logger.info(f"   Found: {indicator}")
                return True
            else:
                logger.warning("⚠️ Synchronization indicators not found in response")
                logger.info(f"Response preview: {response_text[:500]}...")
                return False
        else:
            logger.error(f"Backend request failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing actual backend: {e}")
        return False

def test_ai_response_filtering():
    """
    Test that AI response filtering is working to block premature quiz suggestions
    """
    logger.info("🧪 Testing AI response filtering for premature quiz suggestions...")
    
    try:
        # Simulate a scenario where AI might suggest quiz prematurely
        quiz_suggestion_payload = {
            "student_id": "test_student_filter",
            "lesson_ref": "P5_ENT_046", 
            "student_name": "Test Student",
            "student_response": "I think I understand everything now. Ready to test your knowledge with a quiz!",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 6,  # Well below minimum of 10
                "objectives_covered": 0,  # No objectives covered
                "session_id": f"{TEST_SESSION_ID}_filter"
            }
        }
        
        logger.info("📤 Sending potential quiz suggestion scenario...")
        response = requests.post(f"{BACKEND_URL}/test-lesson-content", json=quiz_suggestion_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            # Check if quiz suggestion was blocked
            blocking_indicators = [
                "🚫 BLOCKING PREMATURE QUIZ SUGGESTION",
                "continue learning",
                "continue exploring this topic",
                "teaching_complete\": false"
            ]
            
            found_blocking = []
            for indicator in blocking_indicators:
                if indicator in response_text:
                    found_blocking.append(indicator)
            
            if found_blocking:
                logger.info("✅ AI response filtering is working!")
                for indicator in found_blocking:
                    logger.info(f"   Found: {indicator}")
                return True
            else:
                logger.warning("⚠️ AI response filtering may not be working")
                logger.info(f"Response preview: {response_text[:500]}...")
                return False
        else:
            logger.error(f"Filtering test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing AI response filtering: {e}")
        return False

def test_guardrail_system():
    """
    Test that the intelligent guardrail system is providing proper feedback
    """
    logger.info("🧪 Testing intelligent guardrail system...")
    
    try:
        # Test scenario where guardrails should activate
        guardrail_payload = {
            "student_id": "test_student_guardrail",
            "lesson_ref": "P5_ENT_046",
            "student_name": "Test Student", 
            "student_response": "Let's do the quiz now!",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 5,  # Below minimum
                "objectives_covered": 0,  # No objectives
                "session_id": f"{TEST_SESSION_ID}_guardrail"
            }
        }
        
        logger.info("📤 Sending guardrail activation scenario...")
        response = requests.post(f"{BACKEND_URL}/test-lesson-content", json=guardrail_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            # Check for guardrail activation
            guardrail_indicators = [
                "⚠️ GUARDRAIL VIOLATIONS",
                "guardrail_feedback",
                "violations",
                "Continue teaching to address these issues",
                "min_interactions_required"
            ]
            
            found_guardrails = []
            for indicator in guardrail_indicators:
                if indicator in response_text:
                    found_guardrails.append(indicator)
            
            if found_guardrails:
                logger.info("✅ Intelligent guardrail system is working!")
                for indicator in found_guardrails:
                    logger.info(f"   Found: {indicator}")
                return True
            else:
                logger.warning("⚠️ Guardrail system may not be working")
                logger.info(f"Response preview: {response_text[:500]}...")
                return False
        else:
            logger.error(f"Guardrail test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing guardrail system: {e}")
        return False

def run_actual_backend_tests():
    """
    Run all actual backend tests to verify synchronization fixes
    """
    logger.info("🚀 Starting Actual Backend E2E Test Suite")
    logger.info("=" * 60)
    
    test_results = {}
    
    # Test 1: Backend lesson flow
    test_results["backend_flow"] = test_actual_backend_lesson_flow()
    
    # Test 2: AI response filtering
    test_results["ai_filtering"] = test_ai_response_filtering()
    
    # Test 3: Guardrail system
    test_results["guardrail_system"] = test_guardrail_system()
    
    # Generate report
    logger.info("\n" + "=" * 60)
    logger.info("📊 ACTUAL BACKEND TEST RESULTS")
    logger.info("=" * 60)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    logger.info(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All actual backend tests PASSED!")
        logger.info("Synchronization fixes are working correctly in the real system.")
        return True
    else:
        logger.warning("⚠️ Some actual backend tests FAILED!")
        logger.warning("The fixes may need adjustment or the backend may need restart.")
        return False

def check_backend_availability():
    """
    Check if the backend server is running and accessible
    """
    try:
        response = requests.get(f"{BACKEND_URL}/api/simple-test", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Backend server is running and accessible")
            return True
        else:
            logger.error(f"Backend server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Cannot connect to backend server: {e}")
        return False

if __name__ == "__main__":
    logger.info("🔧 Actual Backend E2E Test with Synchronization Fixes")
    logger.info("This test verifies fixes are working in the real backend system")
    
    # Check backend availability first
    if not check_backend_availability():
        logger.error("❌ Backend server is not available. Please start the server first.")
        sys.exit(1)
    
    # Run actual backend tests
    success = run_actual_backend_tests()
    
    if success:
        logger.info("\n✅ Synchronization fixes are working correctly in the actual backend!")
        logger.info("The AI Instructor and backend are now properly synchronized.")
        sys.exit(0)
    else:
        logger.error("\n❌ Some issues detected in the actual backend!")
        logger.error("Please check the backend logs and restart if necessary.")
        sys.exit(1)