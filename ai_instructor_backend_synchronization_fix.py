#!/usr/bin/env python3
"""
AI Instructor Backend Synchronization Fix

This script addresses two critical synchronization issues:
1. Learning objectives coverage mismatch between AI Instructor and backend calculations
2. Phase transition mismatch where AI Instructor and backend are out of sync

The fix ensures:
- AI Instructor's objectives assessment is the authoritative source
- AI Instructor controls phase transitions with intelligent guardrails
- Backend calculations are synchronized with AI Instructor's assessments
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

def fix_objectives_coverage_synchronization():
    """
    Fix the mismatch between AI Instructor objectives coverage and backend calculations.
    The AI Instructor should be the authoritative source for objectives coverage.
    """
    
    # Read the main.py file
    main_py_path = 'backend/cloud_function/lesson_manager/main.py'
    
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the section where objectives_covered is calculated from analysis_result
    # This needs to be replaced to prioritize AI Instructor's assessment
    
    old_objectives_calculation = '''                # ENHANCED: Comprehensive teaching completion validation
                objectives_covered = analysis_result.get('objectives_covered', 0)'''
    
    new_objectives_calculation = '''                # ENHANCED: Comprehensive teaching completion validation
                # CRITICAL FIX: Prioritize AI Instructor's objectives assessment over backend calculation
                ai_objectives_assessment = state_updates_from_ai.get('objectives_covered')
                backend_objectives_calculation = analysis_result.get('objectives_covered', 0)
                
                # Use AI Instructor's assessment as authoritative source if available
                if ai_objectives_assessment is not None:
                    objectives_covered = ai_objectives_assessment
                    logger.info(f"[{request_id}] 🤖 Using AI Instructor objectives assessment: {objectives_covered}")
                else:
                    objectives_covered = backend_objectives_calculation
                    logger.info(f"[{request_id}] 🔧 Using backend objectives calculation: {objectives_covered}")
                
                # Sync backend state with AI Instructor's assessment
                if ai_objectives_assessment is not None:
                    analysis_result['objectives_covered'] = ai_objectives_assessment
                    logger.info(f"[{request_id}] 🔄 Synchronized backend calculation with AI assessment")'''
    
    if old_objectives_calculation in content:
        content = content.replace(old_objectives_calculation, new_objectives_calculation)
        print("✅ Fixed objectives coverage synchronization")
    else:
        print("⚠️ Objectives coverage calculation pattern not found - manual review needed")
    
    return content

def fix_phase_transition_synchronization():
    """
    Fix the phase transition mismatch where AI Instructor should control phase transitions
    but backend is overriding them. AI Instructor should be authoritative with guardrails.
    """
    
    # This will be applied to the content from the previous fix
    def apply_phase_fix(content):
        
        # Find where phase transitions are being overridden by backend logic
        # Look for places where new_phase is being set by backend instead of respecting AI decision
        
        old_quiz_blocking = '''                # CRITICAL ENFORCEMENT: Block quiz if objectives not sufficiently covered
                if objectives_covered < 1:  # At least 1 objective must be covered
                    if state_updates_from_ai.get('new_phase') == 'quiz_initiate':
                        state_updates_from_ai['new_phase'] = 'teaching'
                        state_updates_from_ai['teaching_complete'] = False
                        logger.warning(f"[{request_id}] 🔧 BLOCKED QUIZ: No objectives covered yet")'''
        
        new_quiz_blocking = '''                # CRITICAL ENFORCEMENT: Intelligent guardrails for quiz transition
                # AI Instructor controls phase transitions, but guardrails ensure quality
                ai_requested_phase = state_updates_from_ai.get('new_phase')
                
                if ai_requested_phase == 'quiz_initiate' or ai_requested_phase == 'quiz':
                    # Apply intelligent guardrails while respecting AI Instructor's decision
                    guardrail_violations = []
                    
                    if objectives_covered < 1:
                        guardrail_violations.append("No objectives covered yet")
                    
                    if current_interactions < min_interactions:
                        guardrail_violations.append(f"Insufficient interactions ({current_interactions} < {min_interactions})")
                    
                    # If guardrails are violated, provide feedback but let AI decide
                    if guardrail_violations:
                        logger.warning(f"[{request_id}] ⚠️ GUARDRAIL VIOLATIONS for quiz transition: {', '.join(guardrail_violations)}")
                        
                        # Add guardrail feedback to context for AI Instructor
                        state_updates_from_ai['guardrail_feedback'] = {
                            'violations': guardrail_violations,
                            'recommendation': 'Continue teaching to address these issues before quiz',
                            'objectives_covered': objectives_covered,
                            'interactions_count': current_interactions,
                            'min_interactions_required': min_interactions
                        }
                        
                        # Respect AI Instructor's decision but provide feedback
                        logger.info(f"[{request_id}] 🤖 AI Instructor requested quiz transition - providing guardrail feedback")
                    else:
                        logger.info(f"[{request_id}] ✅ Quiz transition approved by guardrails")'''
        
        if old_quiz_blocking in content:
            content = content.replace(old_quiz_blocking, new_quiz_blocking)
            print("✅ Fixed phase transition synchronization - AI Instructor now controls with guardrails")
        else:
            print("⚠️ Quiz blocking pattern not found - manual review needed")
        
        return content
    
    return apply_phase_fix

def add_ai_instructor_state_extraction_enhancement():
    """
    Enhance the AI state extraction to better capture AI Instructor's assessments
    including objectives coverage and phase transition decisions.
    """
    
    def apply_extraction_fix(content):
        
        # Find the extract_state_updates_from_ai_response function and enhance it
        # to better capture AI Instructor's objectives assessment
        
        old_extraction_start = '''def extract_state_updates_from_ai_response(response_text: str, current_phase: str = None, request_id: str = None) -> Dict[str, Any]:
    """
    Logic-based state extraction with smart diagnostic completion detection.
    Text-based pattern matching removed for teaching/quiz phases, but diagnostic completion logic retained.
    CRITICAL FIX: Always ensure new_phase is set to prevent None values.
    """
    state_updates = {}
    
    # CRITICAL FIX: Always initialize new_phase to current_phase to prevent None
    if current_phase:
        state_updates['new_phase'] = current_phase'''
        
        new_extraction_start = '''def extract_state_updates_from_ai_response(response_text: str, current_phase: str = None, request_id: str = None) -> Dict[str, Any]:
    """
    Enhanced AI state extraction with objectives coverage and phase transition detection.
    AI Instructor is the authoritative source for lesson progression and assessment.
    CRITICAL FIX: Always ensure new_phase is set to prevent None values.
    """
    state_updates = {}
    
    # CRITICAL FIX: Always initialize new_phase to current_phase to prevent None
    if current_phase:
        state_updates['new_phase'] = current_phase
    
    # ENHANCED: Extract AI Instructor's objectives assessment
    try:
        response_lower = response_text.lower()
        
        # Look for AI Instructor's objectives coverage assessment
        objectives_patterns = [
            r'objectives?\s+(?:coverage|covered|completed):\s*(\d+)',
            r'(\d+)\s+(?:of|out\s+of|\/)?\s*\d+\s+objectives?\s+(?:covered|completed|achieved)',
            r'learning\s+objectives?\s+(?:coverage|status):\s*(\d+)',
            r'all\s+(\d+)\s+objectives?\s+(?:have\s+been\s+)?(?:covered|completed|achieved)',
            r'objectives?\s+(?:successfully\s+)?(?:covered|completed|achieved):\s*(\d+)'
        ]
        
        for pattern in objectives_patterns:
            import re
            match = re.search(pattern, response_lower)
            if match:
                objectives_count = int(match.group(1))
                state_updates['objectives_covered'] = objectives_count
                logger.info(f"[{request_id}] 🎯 AI Instructor objectives assessment: {objectives_count}")
                break
        
        # Look for percentage-based objectives coverage
        percentage_patterns = [
            r'objectives?\s+(?:coverage|completion):\s*(\d+)%',
            r'(\d+)%\s+(?:of\s+)?objectives?\s+(?:covered|completed)',
            r'learning\s+objectives?\s+(?:at\s+)?(\d+)%'
        ]
        
        for pattern in percentage_patterns:
            match = re.search(pattern, response_lower)
            if match:
                percentage = int(match.group(1))
                state_updates['objectives_coverage_percentage'] = percentage
                logger.info(f"[{request_id}] 📊 AI Instructor objectives percentage: {percentage}%")
                break
        
        # Look for AI Instructor's phase transition decisions
        phase_transition_patterns = [
            r'(?:ready\s+for|transitioning\s+to|moving\s+to|starting)\s+(?:the\s+)?quiz',
            r'quiz\s+(?:phase|time|initiation)',
            r'begin\s+(?:the\s+)?(?:quiz|assessment)',
            r'teaching\s+(?:phase\s+)?(?:complete|finished|done)',
            r'continue\s+(?:with\s+)?teaching',
            r'more\s+teaching\s+(?:is\s+)?(?:needed|required)'
        ]
        
        for pattern in phase_transition_patterns:
            match = re.search(pattern, response_lower)
            if match:
                matched_text = match.group(0)
                if any(word in matched_text for word in ['quiz', 'assessment']):
                    state_updates['ai_phase_request'] = 'quiz_initiate'
                    logger.info(f"[{request_id}] 🎯 AI Instructor requests quiz transition")
                elif any(word in matched_text for word in ['continue', 'more', 'teaching']):
                    state_updates['ai_phase_request'] = 'teaching'
                    logger.info(f"[{request_id}] 📚 AI Instructor requests continued teaching")
                break
        
        # If AI explicitly requests a phase, respect it (with guardrails)
        if 'ai_phase_request' in state_updates:
            state_updates['new_phase'] = state_updates['ai_phase_request']
            
    except Exception as e:
        logger.error(f"[{request_id}] Error in enhanced AI state extraction: {e}")'''
        
        if old_extraction_start in content:
            content = content.replace(old_extraction_start, new_extraction_start)
            print("✅ Enhanced AI state extraction for objectives and phase transitions")
        else:
            print("⚠️ State extraction function not found - manual review needed")
        
        return content
    
    return apply_extraction_fix

def apply_comprehensive_synchronization_fix():
    """
    Apply all synchronization fixes to resolve AI Instructor and backend mismatches.
    """
    
    print("🔧 Starting AI Instructor Backend Synchronization Fix...")
    print("=" * 60)
    
    try:
        # Step 1: Fix objectives coverage synchronization
        print("\n1. Fixing objectives coverage synchronization...")
        content = fix_objectives_coverage_synchronization()
        
        # Step 2: Fix phase transition synchronization
        print("\n2. Fixing phase transition synchronization...")
        phase_fix = fix_phase_transition_synchronization()
        content = phase_fix(content)
        
        # Step 3: Enhance AI state extraction
        print("\n3. Enhancing AI state extraction...")
        extraction_fix = add_ai_instructor_state_extraction_enhancement()
        content = extraction_fix(content)
        
        # Step 4: Write the updated content back to main.py
        main_py_path = 'backend/cloud_function/lesson_manager/main.py'
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("\n✅ Successfully applied all synchronization fixes!")
        print("\nSummary of changes:")
        print("- AI Instructor's objectives assessment is now authoritative")
        print("- AI Instructor controls phase transitions with intelligent guardrails")
        print("- Backend calculations are synchronized with AI assessments")
        print("- Enhanced state extraction captures AI decisions better")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error applying synchronization fixes: {e}")
        return False

if __name__ == "__main__":
    success = apply_comprehensive_synchronization_fix()
    if success:
        print("\n🎉 AI Instructor Backend Synchronization Fix completed successfully!")
        print("\nNext steps:")
        print("1. Restart the backend server")
        print("2. Test lesson flow to verify synchronization")
        print("3. Monitor logs for AI Instructor and backend alignment")
    else:
        print("\n💥 Fix failed - manual intervention required")
        sys.exit(1)