#!/usr/bin/env python3
"""
Teaching Quality Validator

Validates that sufficient teaching has occurred before transitioning to quiz phase.
"""

from typing import Dict, List, <PERSON><PERSON>

def validate_teaching_quality(lesson_context: Dict, request_id: str = "unknown") -> Tuple[bool, List[str], Dict]:
    """
    Validate that teaching phase has provided adequate instruction.
    
    Returns:
        Tuple of (is_ready_for_quiz, list_of_issues, quality_metrics)
    """
    issues = []
    quality_metrics = {}
    
    # Extract teaching metrics
    teaching_interactions = lesson_context.get('teaching_interactions', 0)
    substantive_interactions = lesson_context.get('substantive_teaching_interactions', 0)
    concepts_covered = len(lesson_context.get('key_concepts', []))
    concepts_covered_count = lesson_context.get('concepts_covered_count', 0)
    learning_objectives = len(lesson_context.get('learning_objectives', []))
    
    quality_metrics = {
        'teaching_interactions': teaching_interactions,
        'substantive_interactions': substantive_interactions,
        'concepts_available': concepts_covered,
        'concepts_covered': concepts_covered_count,
        'learning_objectives': learning_objectives,
        'interaction_quality_ratio': substantive_interactions / max(teaching_interactions, 1)
    }
    
    # Minimum requirements for quiz readiness
    min_teaching_interactions = 10
    min_substantive_interactions = 5
    min_concept_coverage_ratio = 0.6  # 60% of concepts should be covered
    
    # Check teaching interaction count
    if teaching_interactions < min_teaching_interactions:
        issues.append(f"Insufficient teaching interactions: {teaching_interactions} < {min_teaching_interactions}")
    
    # Check substantive interaction count
    if substantive_interactions < min_substantive_interactions:
        issues.append(f"Insufficient substantive interactions: {substantive_interactions} < {min_substantive_interactions}")
    
    # Check concept coverage
    if concepts_covered > 0:
        coverage_ratio = concepts_covered_count / concepts_covered
        if coverage_ratio < min_concept_coverage_ratio:
            issues.append(f"Insufficient concept coverage: {coverage_ratio:.1%} < {min_concept_coverage_ratio:.1%}")
    else:
        issues.append("No key concepts defined for lesson")
    
    # Check interaction quality
    if quality_metrics['interaction_quality_ratio'] < 0.4:
        issues.append(f"Low interaction quality ratio: {quality_metrics['interaction_quality_ratio']:.1%}")
    
    is_ready = len(issues) == 0
    
    return is_ready, issues, quality_metrics

def get_teaching_recommendations(quality_metrics: Dict) -> List[str]:
    """Get recommendations for improving teaching quality"""
    
    recommendations = []
    
    if quality_metrics.get('teaching_interactions', 0) < 10:
        recommendations.append("Engage in more interactive teaching exchanges")
    
    if quality_metrics.get('interaction_quality_ratio', 0) < 0.5:
        recommendations.append("Focus on more substantive, concept-focused discussions")
    
    if quality_metrics.get('concepts_covered', 0) < quality_metrics.get('concepts_available', 1) * 0.8:
        recommendations.append("Cover more of the key lesson concepts")
    
    if not recommendations:
        recommendations.append("Teaching quality is good - ready for assessment")
    
    return recommendations

def generate_teaching_summary(lesson_context: Dict) -> str:
    """Generate a summary of teaching quality for logging"""
    
    is_ready, issues, metrics = validate_teaching_quality(lesson_context)
    recommendations = get_teaching_recommendations(metrics)
    
    summary = f"""
Teaching Quality Assessment:
- Status: {'✅ Ready for Quiz' if is_ready else '⚠️ Needs More Teaching'}
- Teaching Interactions: {metrics.get('teaching_interactions', 0)}
- Substantive Interactions: {metrics.get('substantive_interactions', 0)}
- Concept Coverage: {metrics.get('concepts_covered', 0)}/{metrics.get('concepts_available', 0)}
- Quality Ratio: {metrics.get('interaction_quality_ratio', 0):.1%}

Issues: {len(issues)}
{chr(10).join(f"  - {issue}" for issue in issues)}

Recommendations:
{chr(10).join(f"  - {rec}" for rec in recommendations)}
"""
    
    return summary.strip()

if __name__ == "__main__":
    # Test the validator
    test_context = {
        'teaching_interactions': 12,
        'substantive_teaching_interactions': 8,
        'key_concepts': ['fractions', 'numerator', 'denominator', 'equivalent'],
        'concepts_covered_count': 3,
        'learning_objectives': ['understand fractions', 'add fractions', 'subtract fractions']
    }
    
    is_ready, issues, metrics = validate_teaching_quality(test_context)
    
    print("Teaching Quality Validation Test:")
    print(f"Ready for quiz: {is_ready}")
    print(f"Issues: {issues}")
    print(f"Metrics: {metrics}")
    print("\nSummary:")
    print(generate_teaching_summary(test_context))
