#!/usr/bin/env python3
"""
Comprehensive End-to-End Test for 9-Phase Lesson Flow
Validates frontend-backend synchronization and complete phase transitions
"""

import os
import sys
import time
import json
import asyncio
import logging
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensivePhaseFlowTester:
    """Comprehensive tester for 9-phase lesson flow with frontend-backend sync validation"""
    
    def __init__(self):
        self.test_results = {
            'test_start_time': datetime.now().isoformat(),
            'student_id': 'andrea_ugono_33305',
            'lesson_ref': 'P5-ENT-046',
            'phase_transitions': [],
            'frontend_backend_sync': [],
            'api_responses': [],
            'state_update_blocks': [],
            'performance_metrics': {},
            'validation_results': {
                'complete_9_phase_flow': False,
                'frontend_backend_sync': False,
                'api_response_structure': False,
                'state_update_blocks_generated': False,
                'lesson_data_persistence': False
            }
        }
        
        # Expected 9-phase sequence
        self.expected_phases = [
            'smart_diagnostic_start',
            'smart_diagnostic_q1',
            'smart_diagnostic_q2', 
            'smart_diagnostic_q3',
            'smart_diagnostic_q4',
            'smart_diagnostic_q5',
            'teaching_start_level_5',  # Will vary based on diagnostic results
            'teaching',
            'quiz_initiate',
            'quiz_questions',
            'quiz_results',
            'conclusion_summary',
            'final_assessment_pending',
            'completed'
        ]
        
        # Smart diagnostic responses for consistent testing
        self.diagnostic_responses = [
            "A computer is a machine that helps us work and play games. It has a screen and keyboard.",
            "Software is like programs that make the computer do things. Hardware is the physical parts you can touch.",
            "Input devices help us put information into the computer, like keyboard and mouse. Output devices show us results like monitor and speakers.",
            "The CPU is like the brain of the computer that processes information and makes calculations.",
            "Memory stores information temporarily while the computer is working, and storage keeps files permanently."
        ]
        
        # Teaching interaction responses
        self.teaching_responses = [
            "I understand that computers help us with many tasks.",
            "Can you show me more examples of how computers work?",
            "I'm ready to practice what I've learned about computers."
        ]
        
        # Quiz responses
        self.quiz_responses = [
            "The CPU processes information and controls the computer.",
            "Input devices include keyboard, mouse, and microphone."
        ]
    
    async def execute_lesson_interaction(self, user_query: str, context: Dict, interaction_num: int) -> Dict:
        """Execute a single lesson interaction and capture comprehensive response data"""
        try:
            logger.info(f"🔄 Interaction {interaction_num}: {user_query[:50]}...")
            
            # Import the main lesson function
            from main import enhance_lesson_content
            
            start_time = time.time()
            
            # Execute the lesson interaction
            response_text, state_updates, state_json = await enhance_lesson_content(
                user_query=user_query,
                chat_history=[],
                context=context,
                request_id=f"test_interaction_{interaction_num}"
            )
            
            response_time = time.time() - start_time
            
            # Extract AI state update blocks
            state_block_pattern = r'// AI_STATE_UPDATE_BLOCK_START (.*?) // AI_STATE_UPDATE_BLOCK_END'
            state_blocks = re.findall(state_block_pattern, response_text, re.DOTALL)
            
            # Parse state updates
            parsed_state_updates = {}
            if state_blocks:
                try:
                    parsed_state_updates = json.loads(state_blocks[0])
                except:
                    parsed_state_updates = {}
            
            # Create comprehensive response data
            response_data = {
                'interaction_num': interaction_num,
                'timestamp': datetime.now().isoformat(),
                'user_query': user_query,
                'response_text': response_text,
                'response_time_seconds': round(response_time, 3),
                'state_updates': state_updates,
                'state_json': state_json,
                'ai_state_blocks': state_blocks,
                'parsed_state_updates': parsed_state_updates,
                'current_phase_before': context.get('lesson_phase', 'unknown'),
                'current_phase_after': state_updates.get('new_phase', context.get('lesson_phase', 'unknown')),
                'phase_transition_occurred': False,
                'frontend_integration_fields': {
                    'new_phase': state_updates.get('new_phase'),
                    'phase_transition': state_updates.get('phase_transition'),
                    'trigger_completion_workflows': state_updates.get('trigger_completion_workflows'),
                    'lesson_data_ready_for_persistence': state_updates.get('lesson_data_ready_for_persistence')
                }
            }
            
            # Check for phase transition
            if response_data['current_phase_after'] != response_data['current_phase_before']:
                response_data['phase_transition_occurred'] = True
                logger.info(f"   🎯 Phase transition: {response_data['current_phase_before']} → {response_data['current_phase_after']}")
            
            # Validate AI state update block generation
            if state_blocks:
                logger.info(f"   ✅ AI state update block generated: {state_blocks[0][:100]}...")
                self.test_results['state_update_blocks'].append({
                    'interaction': interaction_num,
                    'block_content': state_blocks[0],
                    'parsed_successfully': bool(parsed_state_updates)
                })
            else:
                logger.warning(f"   ⚠️ No AI state update block found")
            
            # Record phase transition
            if response_data['phase_transition_occurred']:
                self.test_results['phase_transitions'].append({
                    'interaction': interaction_num,
                    'from_phase': response_data['current_phase_before'],
                    'to_phase': response_data['current_phase_after'],
                    'timestamp': response_data['timestamp']
                })
            
            # Record API response data
            self.test_results['api_responses'].append(response_data)
            
            return response_data
            
        except Exception as e:
            logger.error(f"❌ Interaction {interaction_num} failed: {e}")
            return {
                'interaction_num': interaction_num,
                'error': str(e),
                'current_phase_before': context.get('lesson_phase', 'unknown'),
                'current_phase_after': 'error'
            }
    
    def create_lesson_context(self, phase: str, interaction_count: int, additional_data: Dict = None) -> Dict:
        """Create comprehensive lesson context for testing"""
        context = {
            'student_info': {
                'first_name': 'Andrea',
                'student_id': 'andrea_ugono_33305',
                'email': '<EMAIL>'
            },
            'lesson_ref': 'P5-ENT-046',
            'country': 'Nigeria',
            'curriculum_name': 'National Curriculum',
            'grade': 'Primary 5',
            'level': 'P5',
            'subject': 'Entrepreneurship',
            'topic': 'Introduction to Entrepreneurship',
            'lesson_phase': phase,
            'current_phase': phase,
            'interaction_count': interaction_count,
            'session_id': f'test_session_{int(time.time())}',
            'module_name': 'Basic Entrepreneurship Concepts',
            'key_concepts_str': 'entrepreneurship, business ideas, innovation, problem solving',
            'learning_objectives': [
                'Understand what entrepreneurship means',
                'Identify business opportunities',
                'Develop problem-solving skills'
            ],
            # Smart diagnostic state
            'smart_diagnostic': {
                'current_question': 1,
                'questions_completed': 0,
                'diagnostic_complete': False,
                'grade': 'Primary 5'
            }
        }
        
        if additional_data:
            context.update(additional_data)
        
        return context
    
    async def run_smart_diagnostic_phase(self) -> Tuple[str, int]:
        """Execute complete smart diagnostic phase"""
        logger.info("🧪 PHASE 1-3: Smart Diagnostic Testing")
        logger.info("=" * 60)
        
        current_phase = 'smart_diagnostic_start'
        interaction_count = 0
        
        # Interaction 1: Start smart diagnostic
        interaction_count += 1
        context = self.create_lesson_context(current_phase, interaction_count)
        
        response_data = await self.execute_lesson_interaction(
            "Hi! I'm ready to start learning about entrepreneurship.",
            context,
            interaction_count
        )
        
        current_phase = response_data.get('current_phase_after', current_phase)
        
        # Interactions 2-6: Answer 5 smart diagnostic questions
        for q_num in range(5):
            if q_num < len(self.diagnostic_responses):
                interaction_count += 1
                
                expected_phase = f'smart_diagnostic_q{q_num + 1}'
                context = self.create_lesson_context(expected_phase, interaction_count)
                context['smart_diagnostic']['current_question'] = q_num + 1
                context['smart_diagnostic']['questions_completed'] = q_num
                
                response_data = await self.execute_lesson_interaction(
                    self.diagnostic_responses[q_num],
                    context,
                    interaction_count
                )
                
                current_phase = response_data.get('current_phase_after', current_phase)
                logger.info(f"   📝 Q{q_num + 1} completed, current phase: {current_phase}")
        
        # Determine assigned teaching level
        assigned_level = 5  # Default level based on diagnostic performance
        if 'teaching_start_level' in current_phase:
            level_match = re.search(r'level_(\d+)', current_phase)
            if level_match:
                assigned_level = int(level_match.group(1))
        
        logger.info(f"🎯 Smart Diagnostic Complete: Assigned Level {assigned_level}")
        return current_phase, assigned_level
    
    async def run_teaching_phase(self, start_phase: str, assigned_level: int) -> str:
        """Execute teaching phase"""
        logger.info("🧪 PHASE 4-5: Teaching Phase Testing")
        logger.info("=" * 60)
        
        current_phase = start_phase
        interaction_count = 7  # Continue from diagnostic interactions
        
        # Teaching interactions
        for i, response in enumerate(self.teaching_responses):
            interaction_count += 1
            
            if i == 0:
                expected_phase = f'teaching_start_level_{assigned_level}'
            elif i == 1:
                expected_phase = 'teaching'
            else:
                expected_phase = 'teaching'  # May transition to quiz_initiate
            
            context = self.create_lesson_context(expected_phase, interaction_count)
            context['assigned_level_for_teaching'] = assigned_level
            
            response_data = await self.execute_lesson_interaction(
                response,
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   📚 Teaching interaction {i+1}, current phase: {current_phase}")
            
            # Check if transitioned to quiz
            if 'quiz' in current_phase:
                break
        
        logger.info(f"🎯 Teaching Phase Complete: Final phase {current_phase}")
        return current_phase
    
    async def run_assessment_phase(self, start_phase: str) -> str:
        """Execute assessment phase"""
        logger.info("🧪 PHASE 6-8: Assessment Phase Testing")
        logger.info("=" * 60)
        
        current_phase = start_phase
        interaction_count = 11  # Continue from previous interactions
        
        # Quiz initiation
        if current_phase != 'quiz_initiate':
            interaction_count += 1
            context = self.create_lesson_context('quiz_initiate', interaction_count)
            
            response_data = await self.execute_lesson_interaction(
                "I'm ready for the quiz!",
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   📝 Quiz initiated, current phase: {current_phase}")
        
        # Quiz questions
        for i, response in enumerate(self.quiz_responses):
            interaction_count += 1
            
            expected_phase = 'quiz_questions' if i == 0 else 'quiz_questions'
            context = self.create_lesson_context(expected_phase, interaction_count)
            
            response_data = await self.execute_lesson_interaction(
                response,
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   📝 Quiz question {i+1} answered, current phase: {current_phase}")
        
        # Quiz results
        if current_phase != 'quiz_results':
            interaction_count += 1
            context = self.create_lesson_context('quiz_results', interaction_count)
            
            response_data = await self.execute_lesson_interaction(
                "How did I do on the quiz?",
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   📊 Quiz results processed, current phase: {current_phase}")
        
        logger.info(f"🎯 Assessment Phase Complete: Final phase {current_phase}")
        return current_phase
    
    async def run_completion_phase(self, start_phase: str) -> str:
        """Execute completion phase"""
        logger.info("🧪 PHASE 9: Completion Phase Testing")
        logger.info("=" * 60)
        
        current_phase = start_phase
        interaction_count = 15  # Continue from previous interactions
        
        # Conclusion summary
        if current_phase != 'conclusion_summary':
            interaction_count += 1
            context = self.create_lesson_context('conclusion_summary', interaction_count)
            
            response_data = await self.execute_lesson_interaction(
                "Can you give me a summary of what I learned?",
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   📋 Conclusion summary generated, current phase: {current_phase}")
        
        # Final assessment pending
        if current_phase == 'final_assessment_pending':
            interaction_count += 1
            context = self.create_lesson_context('final_assessment_pending', interaction_count)
            
            response_data = await self.execute_lesson_interaction(
                "I'm ready for the final assessment.",
                context,
                interaction_count
            )
            
            current_phase = response_data.get('current_phase_after', current_phase)
            logger.info(f"   🎯 Final assessment processed, current phase: {current_phase}")
        
        logger.info(f"🎯 Completion Phase Complete: Final phase {current_phase}")
        return current_phase
    
    def validate_frontend_backend_sync(self):
        """Validate frontend-backend synchronization"""
        logger.info("🔍 Validating Frontend-Backend Synchronization")
        logger.info("=" * 60)
        
        sync_issues = []
        
        for response in self.test_results['api_responses']:
            if 'error' in response:
                continue
            
            # Check if phase transitions are properly communicated
            if response.get('phase_transition_occurred'):
                frontend_fields = response.get('frontend_integration_fields', {})
                
                # Validate required frontend fields
                required_fields = ['new_phase']
                missing_fields = [field for field in required_fields if not frontend_fields.get(field)]
                
                if missing_fields:
                    sync_issues.append({
                        'interaction': response['interaction_num'],
                        'issue': 'Missing frontend integration fields',
                        'missing_fields': missing_fields,
                        'phase_transition': f"{response['current_phase_before']} → {response['current_phase_after']}"
                    })
        
        if sync_issues:
            logger.warning(f"⚠️ Frontend-Backend Sync Issues Found: {len(sync_issues)}")
            for issue in sync_issues:
                logger.warning(f"   - Interaction {issue['interaction']}: {issue['issue']}")
        else:
            logger.info("✅ Frontend-Backend Synchronization: PASSED")
            self.test_results['validation_results']['frontend_backend_sync'] = True
        
        return len(sync_issues) == 0

    def validate_api_response_structure(self):
        """Validate API response structure for frontend integration"""
        logger.info("🔍 Validating API Response Structure")
        logger.info("=" * 60)

        structure_issues = []

        for response in self.test_results['api_responses']:
            if 'error' in response:
                continue

            # Check for required API response fields
            required_api_fields = [
                'response_text',
                'state_updates',
                'current_phase_after'
            ]

            missing_api_fields = [field for field in required_api_fields if field not in response]

            if missing_api_fields:
                structure_issues.append({
                    'interaction': response['interaction_num'],
                    'issue': 'Missing API response fields',
                    'missing_fields': missing_api_fields
                })

        if structure_issues:
            logger.warning(f"⚠️ API Response Structure Issues: {len(structure_issues)}")
            for issue in structure_issues:
                logger.warning(f"   - Interaction {issue['interaction']}: {issue['issue']}")
        else:
            logger.info("✅ API Response Structure: PASSED")
            self.test_results['validation_results']['api_response_structure'] = True

        return len(structure_issues) == 0

    def validate_state_update_blocks(self):
        """Validate AI state update block generation"""
        logger.info("🔍 Validating AI State Update Blocks")
        logger.info("=" * 60)

        total_transitions = len(self.test_results['phase_transitions'])
        blocks_generated = len(self.test_results['state_update_blocks'])

        logger.info(f"📊 Phase transitions: {total_transitions}")
        logger.info(f"📊 State update blocks: {blocks_generated}")

        if blocks_generated >= total_transitions * 0.8:  # 80% threshold
            logger.info("✅ AI State Update Block Generation: PASSED")
            self.test_results['validation_results']['state_update_blocks_generated'] = True
            return True
        else:
            logger.warning(f"⚠️ AI State Update Block Generation: LOW ({blocks_generated}/{total_transitions})")
            return False

    def validate_complete_9_phase_flow(self):
        """Validate complete 9-phase flow execution"""
        logger.info("🔍 Validating Complete 9-Phase Flow")
        logger.info("=" * 60)

        phases_encountered = set()
        for transition in self.test_results['phase_transitions']:
            phases_encountered.add(transition['from_phase'])
            phases_encountered.add(transition['to_phase'])

        # Check for key phases
        key_phases = [
            'smart_diagnostic_start',
            'smart_diagnostic_q1',
            'teaching',
            'quiz_questions',
            'conclusion_summary',
            'completed'
        ]

        missing_phases = [phase for phase in key_phases if phase not in phases_encountered]

        logger.info(f"📊 Phases encountered: {len(phases_encountered)}")
        logger.info(f"📊 Key phases covered: {len(key_phases) - len(missing_phases)}/{len(key_phases)}")

        if len(missing_phases) == 0:
            logger.info("✅ Complete 9-Phase Flow: PASSED")
            self.test_results['validation_results']['complete_9_phase_flow'] = True
            return True
        else:
            logger.warning(f"⚠️ Missing key phases: {missing_phases}")
            return False

    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 COMPREHENSIVE 9-PHASE FLOW TEST REPORT")
        logger.info("=" * 80)

        # Test summary
        logger.info(f"🎯 Test Configuration:")
        logger.info(f"   Student: Andrea Ugono (andrea_ugono_33305)")
        logger.info(f"   Lesson: {self.test_results['lesson_ref']} (Primary 5 Entrepreneurship)")
        logger.info(f"   Test Start: {self.test_results['test_start_time']}")
        logger.info(f"   Total Interactions: {len(self.test_results['api_responses'])}")
        logger.info(f"   Phase Transitions: {len(self.test_results['phase_transitions'])}")

        # Phase transition summary
        logger.info(f"\n🔄 Phase Transition Summary:")
        for i, transition in enumerate(self.test_results['phase_transitions'], 1):
            logger.info(f"   {i:2d}. {transition['from_phase']} → {transition['to_phase']} (Interaction {transition['interaction']})")

        # Validation results
        logger.info(f"\n✅ Validation Results:")
        validation = self.test_results['validation_results']
        for criterion, passed in validation.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"   {status} {criterion.replace('_', ' ').title()}")

        # Performance metrics
        response_times = [r.get('response_time_seconds', 0) for r in self.test_results['api_responses'] if 'response_time_seconds' in r]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            logger.info(f"\n📊 Performance Metrics:")
            logger.info(f"   Average Response Time: {avg_response_time:.2f}s")
            logger.info(f"   Maximum Response Time: {max_response_time:.2f}s")

        # Final assessment
        all_passed = all(validation.values())
        logger.info(f"\n🎯 FINAL ASSESSMENT:")
        if all_passed:
            logger.info("✅ ALL VALIDATION CRITERIA PASSED")
            logger.info("✅ 9-Phase lesson flow is working correctly")
            logger.info("✅ Frontend-backend synchronization is functional")
            logger.info("✅ System ready for production deployment")
        else:
            failed_criteria = [k for k, v in validation.items() if not v]
            logger.info("❌ SOME VALIDATION CRITERIA FAILED")
            logger.info(f"⚠️ Failed criteria: {failed_criteria}")
            logger.info("🔧 Additional fixes required before production")

        return all_passed

    async def run_comprehensive_test(self):
        """Execute comprehensive 9-phase flow test"""
        try:
            logger.info("🚀 Starting Comprehensive 9-Phase Flow Test")
            logger.info("=" * 80)

            # Phase 1-3: Smart Diagnostic
            current_phase, assigned_level = await self.run_smart_diagnostic_phase()

            # Phase 4-5: Teaching
            current_phase = await self.run_teaching_phase(current_phase, assigned_level)

            # Phase 6-8: Assessment
            current_phase = await self.run_assessment_phase(current_phase)

            # Phase 9: Completion
            final_phase = await self.run_completion_phase(current_phase)

            # Validation
            logger.info("\n🔍 VALIDATION PHASE")
            logger.info("=" * 80)

            sync_valid = self.validate_frontend_backend_sync()
            api_valid = self.validate_api_response_structure()
            blocks_valid = self.validate_state_update_blocks()
            flow_valid = self.validate_complete_9_phase_flow()

            # Generate report
            overall_success = self.generate_comprehensive_report()

            return overall_success

        except Exception as e:
            logger.error(f"❌ Comprehensive test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """Main test execution"""
    tester = ComprehensivePhaseFlowTester()
    success = await tester.run_comprehensive_test()

    if success:
        print("\n🎉 COMPREHENSIVE 9-PHASE FLOW TEST: SUCCESS!")
        print("✅ All validation criteria passed")
        print("✅ Frontend-backend synchronization working")
        print("✅ Complete lesson flow functional")
    else:
        print("\n⚠️ COMPREHENSIVE 9-PHASE FLOW TEST: ISSUES DETECTED")
        print("❌ Some validation criteria failed")
        print("🔧 Review test report for specific issues")

    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
