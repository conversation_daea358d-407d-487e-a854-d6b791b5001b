2025-07-19 21:21:33,651 - INFO - [main.py:71] - ============================================================
2025-07-19 21:21:33,659 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-19 21:21:33,660 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:21:33,660 - INFO - [main.py:74] - Platform: win32
2025-07-19 21:21:33,660 - INFO - [main.py:75] - ============================================================
2025-07-19 21:21:37,782 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-19 21:21:38,142 - INFO - [main.py:148] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-19 21:21:38,142 - INFO - [main.py:468] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-19 21:21:38,144 - INFO - [main.py:626] - ================================================================================
2025-07-19 21:21:38,144 - INFO - [main.py:627] - LESSON MANAGER BACKEND STARTING UP
2025-07-19 21:21:38,144 - INFO - [main.py:628] - ================================================================================
2025-07-19 21:21:38,145 - INFO - [main.py:629] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:21:38,145 - INFO - [main.py:630] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-19 21:21:38,145 - INFO - [main.py:631] - Log level: DEBUG
2025-07-19 21:21:38,146 - INFO - [main.py:632] - ================================================================================
2025-07-19 21:21:38,146 - INFO - [main.py:634] - Logging configuration complete with immediate console output
2025-07-19 21:21:38,146 - INFO - [main.py:635] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-19 21:21:38,150 - INFO - [main.py:1332] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-19 21:21:38,150 - INFO - [main.py:1339] - [OK] Enhanced state and auth managers initialized successfully
2025-07-19 21:21:38,153 - INFO - [main.py:1571] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-19 21:21:38,170 - INFO - [main.py:1600] - Phase transition fixes imported successfully
2025-07-19 21:21:38,174 - INFO - [main.py:5362] - Successfully imported utils functions
2025-07-19 21:21:38,178 - INFO - [main.py:5370] - Successfully imported extract_ai_state functions
2025-07-19 21:21:38,186 - INFO - [main.py:5820] - FLASK: Using unified Firebase initialization approach...
2025-07-19 21:21:38,190 - WARNING - [unified_firebase_init.py:54] - Environment variable path not found: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-19 21:21:38,191 - INFO - [unified_firebase_init.py:62] - Using secure credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-19 21:21:38,194 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-19 21:21:38,195 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-19 21:21:38,195 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-19 21:21:38,196 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-19 21:21:38,285 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-19 21:21:38,286 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-19 21:21:40,018 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-19 21:21:42,520 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-19 21:21:43,132 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-19 21:21:43,133 - INFO - [main.py:5828] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-19 21:21:43,134 - INFO - [main.py:5918] - Gemini API will be initialized on first use (lazy loading).
2025-07-19 21:21:43,150 - INFO - [main.py:19273] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-19 21:21:43,151 - INFO - [main.py:19316] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-19 21:21:43,167 - INFO - [main.py:2059] - Successfully imported timetable_generator functions
2025-07-19 21:21:43,175 - INFO - [main.py:25343] - Starting Lesson Manager Service...
2025-07-19 21:21:43,179 - INFO - [main.py:25349] - ENHANCED: Debug mode enabled for better console logging
2025-07-19 21:21:43,180 - INFO - [main.py:25351] - Flask server starting on host 0.0.0.0, port 5000
2025-07-19 21:21:43,181 - INFO - [main.py:25352] - Debug mode: ON
2025-07-19 21:21:43,181 - INFO - [main.py:25355] - ================================================================================
2025-07-19 21:21:43,182 - INFO - [main.py:25356] - LESSON MANAGER BACKEND READY TO START
2025-07-19 21:21:43,182 - INFO - [main.py:25357] - ================================================================================
2025-07-19 21:21:43,183 - INFO - [main.py:25358] - Server: 0.0.0.0:5000
2025-07-19 21:21:43,183 - INFO - [main.py:25359] - Debug mode: ON
2025-07-19 21:21:43,184 - INFO - [main.py:25360] - Firebase initialized: False
2025-07-19 21:21:43,185 - INFO - [main.py:25361] - Database available: True
2025-07-19 21:21:43,185 - INFO - [main.py:25362] - Health check: http://localhost:5000/api/health
2025-07-19 21:21:43,186 - INFO - [main.py:25363] - Enhance content: http://localhost:5000/api/enhance-content
2025-07-19 21:21:43,187 - INFO - [main.py:25364] - ================================================================================
2025-07-19 21:21:43,187 - INFO - [main.py:25369] - CONSOLE: Starting Flask development server...
2025-07-19 21:21:43,277 - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-19 21:21:43,278 - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-07-19 21:39:31,771 - INFO - [main.py:71] - ============================================================
2025-07-19 21:39:31,772 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-19 21:39:31,773 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:39:31,773 - INFO - [main.py:74] - Platform: win32
2025-07-19 21:39:31,773 - INFO - [main.py:75] - ============================================================
2025-07-19 21:39:35,930 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-19 21:39:36,446 - INFO - [main.py:148] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend/cloud_function/lesson_manager\.env
2025-07-19 21:39:36,446 - INFO - [main.py:468] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-19 21:39:36,449 - INFO - [main.py:626] - ================================================================================
2025-07-19 21:39:36,449 - INFO - [main.py:627] - LESSON MANAGER BACKEND STARTING UP
2025-07-19 21:39:36,450 - INFO - [main.py:628] - ================================================================================
2025-07-19 21:39:36,451 - INFO - [main.py:629] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:39:36,452 - INFO - [main.py:630] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-19 21:39:36,452 - INFO - [main.py:631] - Log level: DEBUG
2025-07-19 21:39:36,453 - INFO - [main.py:632] - ================================================================================
2025-07-19 21:39:36,454 - INFO - [main.py:634] - Logging configuration complete with immediate console output
2025-07-19 21:39:36,455 - INFO - [main.py:635] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-19 21:39:36,459 - INFO - [main.py:1332] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-19 21:39:36,460 - INFO - [main.py:1339] - [OK] Enhanced state and auth managers initialized successfully
2025-07-19 21:39:36,466 - INFO - [main.py:1571] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-19 21:39:36,501 - INFO - [main.py:1600] - Phase transition fixes imported successfully
2025-07-19 21:39:36,509 - INFO - [main.py:5362] - Successfully imported utils functions
2025-07-19 21:39:36,515 - INFO - [main.py:5370] - Successfully imported extract_ai_state functions
2025-07-19 21:39:36,527 - INFO - [main.py:5820] - FLASK: Using unified Firebase initialization approach...
2025-07-19 21:39:36,531 - WARNING - [unified_firebase_init.py:54] - Environment variable path not found: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-19 21:39:36,532 - INFO - [unified_firebase_init.py:62] - Using secure credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend/cloud_function/lesson_manager\secure\firebase-credentials.json
2025-07-19 21:39:36,533 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend/cloud_function/lesson_manager\secure\firebase-credentials.json
2025-07-19 21:39:36,534 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-19 21:39:36,535 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-19 21:39:36,535 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-19 21:39:36,623 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-19 21:39:36,625 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-19 21:39:38,392 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-19 21:39:40,384 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-19 21:39:41,533 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-19 21:39:41,533 - INFO - [main.py:5828] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-19 21:39:41,534 - INFO - [main.py:5918] - Gemini API will be initialized on first use (lazy loading).
2025-07-19 21:39:41,546 - INFO - [main.py:19273] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-19 21:39:41,547 - INFO - [main.py:19316] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-19 21:39:41,557 - INFO - [main.py:2059] - Successfully imported timetable_generator functions
2025-07-19 21:39:41,564 - INFO - [main.py:25444] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend/cloud_function/lesson_manager\secure\firebase-credentials.json
2025-07-19 21:39:41,656 - INFO - [main.py:25447] - Google Cloud Storage client initialized successfully.
2025-07-19 21:39:41,657 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-19 21:39:41,660 - INFO - [test_teaching_rules_enforcement_comprehensive.py:344] - 🚀 Starting comprehensive teaching rules enforcement tests...
2025-07-19 21:39:41,660 - INFO - [test_teaching_rules_enforcement_comprehensive.py:31] - 🧪 Testing early teaching phase quiz request...
2025-07-19 21:39:41,661 - ERROR - [test_teaching_rules_enforcement_comprehensive.py:94] - Test failed with error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-19 21:39:41,702 - ERROR - [test_teaching_rules_enforcement_comprehensive.py:95] - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\test_teaching_rules_enforcement_comprehensive.py", line 60, in test_early_teaching_phase_quiz_request
    result = await enhance_content_api(request_data)
                   ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend/cloud_function/lesson_manager\auth_decorator.py", line 64, in decorated_function
    request_id = getattr(g, 'request_id', 'no-auth-req-id')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.

2025-07-19 21:39:42,712 - INFO - [test_teaching_rules_enforcement_comprehensive.py:99] - 🧪 Testing mid-teaching phase quiz request...
2025-07-19 21:39:42,714 - ERROR - [test_teaching_rules_enforcement_comprehensive.py:156] - Test failed with error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-19 21:39:43,725 - INFO - [test_teaching_rules_enforcement_comprehensive.py:160] - 🧪 Testing sufficient teaching phase quiz request...
2025-07-19 21:39:43,727 - ERROR - [test_teaching_rules_enforcement_comprehensive.py:219] - Test failed with error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-19 21:39:44,734 - INFO - [test_teaching_rules_enforcement_comprehensive.py:223] - 🧪 Testing AI-initiated quiz transition...
2025-07-19 21:39:44,735 - ERROR - [test_teaching_rules_enforcement_comprehensive.py:282] - Test failed with error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-19 21:39:45,746 - INFO - [test_teaching_rules_enforcement_comprehensive.py:286] - 
================================================================================
2025-07-19 21:39:45,748 - INFO - [test_teaching_rules_enforcement_comprehensive.py:287] - 📊 TEACHING RULES ENFORCEMENT TEST REPORT
2025-07-19 21:39:45,750 - INFO - [test_teaching_rules_enforcement_comprehensive.py:288] - ================================================================================
2025-07-19 21:39:45,751 - INFO - [test_teaching_rules_enforcement_comprehensive.py:308] - 
📈 SUMMARY:
2025-07-19 21:39:45,753 - INFO - [test_teaching_rules_enforcement_comprehensive.py:309] -    Total Tests: 0
2025-07-19 21:39:45,754 - INFO - [test_teaching_rules_enforcement_comprehensive.py:324] - 
✅ ALL TESTS PASSED: Teaching rules working correctly
2025-07-19 21:39:45,763 - INFO - [test_teaching_rules_enforcement_comprehensive.py:340] - 
📄 Detailed report saved to: teaching_rules_test_report_1752957585.json
2025-07-19 21:39:45,764 - INFO - [test_teaching_rules_enforcement_comprehensive.py:364] - 
🏁 Teaching rules enforcement tests completed!
