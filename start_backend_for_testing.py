#!/usr/bin/env python3
"""
Start Backend Server for Testing
Simple script to start the lesson manager backend for testing purposes.
"""

import os
import sys
import subprocess
import time
import requests
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def start_backend_server():
    """Start the backend server"""
    try:
        # Change to the backend directory
        backend_dir = os.path.join("backend", "cloud_function", "lesson_manager")
        
        if not os.path.exists(backend_dir):
            logger.error(f"❌ Backend directory not found: {backend_dir}")
            return False
        
        logger.info(f"🚀 Starting backend server in {backend_dir}")
        
        # Start the server
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Wait a moment for server to start
        logger.info("⏳ Waiting for server to start...")
        time.sleep(5)
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            if response.status_code == 200:
                logger.info("✅ Backend server started successfully!")
                logger.info("🌐 Server running at http://localhost:5000")
                return True
            else:
                logger.error(f"❌ Server health check failed: {response.status_code}")
                return False
        except requests.exceptions.RequestException:
            logger.error("❌ Server not responding to health check")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to start backend server: {e}")
        return False

def main():
    """Main function"""
    logger.info("🔧 Backend Server Starter")
    logger.info("=" * 50)
    
    if start_backend_server():
        logger.info("✅ Backend server is ready for testing!")
        logger.info("💡 You can now run the handoff verification test:")
        logger.info("   python test_ai_instructor_handoff_fix.py")
        logger.info("\n🛑 Press Ctrl+C to stop the server when done testing")
        
        try:
            # Keep the script running
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("\n🛑 Stopping server...")
            
    else:
        logger.error("❌ Failed to start backend server")
        sys.exit(1)

if __name__ == "__main__":
    main()