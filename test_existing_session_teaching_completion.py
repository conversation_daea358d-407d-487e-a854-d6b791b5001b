#!/usr/bin/env python3
"""
Test Existing Session Teaching Completion
========================================

This test simulates an existing session that's already in the teaching phase
to verify that the enhanced teaching completion system works correctly.
"""

import requests
import json
import time

def test_existing_session_teaching():
    """Test teaching completion with an existing session"""
    
    url = "http://localhost:5000/api/enhance-content"
    
    # Use a consistent session ID to simulate an existing session
    session_id = "existing_session_teaching_test"
    student_id = "existing_student_test"
    
    headers = {
        'Content-Type': 'application/json',
        'X-Testing-Mode': 'true',
        'X-Student-ID': student_id
    }
    
    print("🧪 TESTING: Existing Session Teaching Completion")
    print("=" * 70)
    
    # Step 1: First, make a request to establish the session in teaching phase
    print("📋 STEP 1: Establishing session in teaching phase")
    
    establish_data = {
        "session_id": session_id,
        "lesson_ref": "P5_AI_088",
        "subject": "artificial_intelligence",
        "grade": "primary_5",
        "student_input": "I want to continue learning about AI in transportation",
        "current_phase": "teaching",
        "phase_history": ["smart_diagnostic_start", "smart_diagnostic_q1", "smart_diagnostic_q2", "smart_diagnostic_q3", "smart_diagnostic_q4", "smart_diagnostic_q5", "teaching"],
        "teaching_interactions": 5,
        "objectives_covered": 3,
        "total_objectives": 5,
        "content_depth_score": 0.7,
        "teaching_level": 6,
        "diagnostic_complete": True,
        "lesson_start_time": time.time() - 1200,  # 20 minutes ago
        "teaching_start_time": time.time() - 600   # 10 minutes ago
    }
    
    try:
        response = requests.post(url, json=establish_data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            response_data = response.json()
            current_phase = response_data.get('data', {}).get('current_phase', '')
            
            print(f"   Status: {response.status_code} ✅")
            print(f"   Phase: {current_phase}")
            
            if 'teaching' in current_phase.lower():
                print("   ✅ Successfully established teaching phase!")
            else:
                print(f"   ⚠️ Phase is {current_phase}, not teaching")
        else:
            print(f"   Status: {response.status_code} ❌")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   Exception: {e}")
        return False
    
    # Step 2: Now test teaching completion with high metrics
    print(f"\n📋 STEP 2: Testing teaching completion with high metrics")
    
    completion_data = {
        "session_id": session_id,  # Same session
        "lesson_ref": "P5_AI_088",
        "subject": "artificial_intelligence", 
        "grade": "primary_5",
        "student_input": "I understand everything now! Can we do the quiz?",
        "current_phase": "teaching",
        "phase_history": ["smart_diagnostic_start", "smart_diagnostic_q1", "smart_diagnostic_q2", "smart_diagnostic_q3", "smart_diagnostic_q4", "smart_diagnostic_q5", "teaching"],
        "teaching_interactions": 12,  # High number
        "objectives_covered": 5,      # 100% coverage
        "total_objectives": 5,
        "content_depth_score": 0.95,  # Very high
        "teaching_level": 6,
        "diagnostic_complete": True,
        "lesson_start_time": time.time() - 1200,  # 20 minutes ago
        "teaching_start_time": time.time() - 600   # 10 minutes ago
    }
    
    try:
        response = requests.post(url, json=completion_data, headers=headers, timeout=30)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Extract key information
            ai_response = response_data.get('data', {}).get('enhanced_content', '')
            current_phase = response_data.get('data', {}).get('current_phase', '')
            new_phase = response_data.get('data', {}).get('state_updates', {}).get('new_phase', '')
            teaching_complete = response_data.get('data', {}).get('state_updates', {}).get('teaching_complete', False)
            
            print(f"   Current Phase: {current_phase}")
            print(f"   New Phase: {new_phase}")
            print(f"   Teaching Complete: {teaching_complete}")
            print(f"   AI Response: {ai_response[:150]}...")
            
            # Check for enhanced system indicators
            response_str = json.dumps(response_data, default=str).lower()
            
            enhanced_indicators = [
                'teaching complete',
                'quiz initiate', 
                'handoff',
                'primary success',
                '100% objectives',
                'adaptive requirements',
                'guardrails applied'
            ]
            
            found_indicators = [indicator for indicator in enhanced_indicators 
                              if indicator in response_str]
            
            print(f"\n🔍 ENHANCED SYSTEM INDICATORS: {len(found_indicators)}")
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            # Check if quiz transition occurred
            quiz_transition = (new_phase == 'quiz_initiate' or 
                             teaching_complete or 
                             'quiz' in ai_response.lower())
            
            if quiz_transition:
                print(f"\n🎉 SUCCESS: Teaching completion system is working!")
                print(f"   Quiz transition detected")
                return True
            elif len(found_indicators) >= 2:
                print(f"\n✅ PARTIAL SUCCESS: Enhanced system is active")
                print(f"   Multiple enhanced indicators found")
                return True
            else:
                print(f"\n❌ ISSUE: Enhanced system not clearly working")
                return False
                
        else:
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   Exception: {e}")
        return False

def main():
    """Main test execution"""
    print("🚀 STARTING: Existing Session Teaching Completion Test")
    
    success = test_existing_session_teaching()
    
    if success:
        print(f"\n🎉 RESULT: Enhanced teaching completion system is working!")
        print(f"   The fix successfully preserves existing sessions")
        print(f"   Teaching completion validation is operational")
    else:
        print(f"\n🔧 RESULT: Issue still exists")
        print(f"   May need additional fixes to session management")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)