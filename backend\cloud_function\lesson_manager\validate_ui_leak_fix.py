#!/usr/bin/env python3
"""
Validation script to ensure the UI leak fix is working correctly
"""

import os
import sys
import re

def validate_guardrails_fix():
    """Validate that the guardrails fix is properly implemented"""
    print("🔍 Validating Guardrails UI Leak Fix Implementation")
    print("=" * 60)
    
    # Check if the fix is properly implemented
    guardrails_file = os.path.join(os.path.dirname(__file__), 'intelligent_guardrails.py')
    
    if not os.path.exists(guardrails_file):
        print("❌ intelligent_guardrails.py not found")
        return False
    
    with open(guardrails_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the fix implementation
    fix_indicators = [
        "BACKEND INSTRUCTION (INTERNAL ONLY)",
        "_generate_student_friendly_continuation_message",
        "student_friendly_message",
        "Do NOT add backend instruction messages to student-facing response"
    ]
    
    found_indicators = []
    for indicator in fix_indicators:
        if indicator in content:
            found_indicators.append(indicator)
            print(f"✅ Found: {indicator}")
        else:
            print(f"❌ Missing: {indicator}")
    
    if len(found_indicators) >= 3:
        print(f"\n✅ GUARDRAILS FIX PROPERLY IMPLEMENTED")
        print(f"   Found {len(found_indicators)}/{len(fix_indicators)} key indicators")
    else:
        print(f"\n❌ GUARDRAILS FIX INCOMPLETE")
        print(f"   Only found {len(found_indicators)}/{len(fix_indicators)} key indicators")
        return False
    
    # Check that the problematic code is removed (unprotected guidance additions)
    # Look for lines that add guidance without proper filtering
    lines = content.split('\n')
    problematic_lines = []

    for i, line in enumerate(lines, 1):
        # Look for enhanced_response += guidance patterns
        if re.search(r'enhanced_response\s*\+=.*guidance', line):
            # Check if this line is protected by filtering logic
            # Look at the previous few lines for filtering conditions
            context_lines = lines[max(0, i-5):i]
            has_protection = any(
                'if not any(' in context_line or
                'backend_phrase' in context_line or
                'continue teaching' in context_line
                for context_line in context_lines
            )

            if not has_protection:
                problematic_lines.append((i, line.strip()))

    if problematic_lines:
        print(f"\n⚠️ UNPROTECTED GUIDANCE ADDITIONS FOUND:")
        for line_num, line_content in problematic_lines:
            print(f"   - Line {line_num}: {line_content}")
        print("   These lines might leak backend instructions without filtering")
    else:
        print(f"\n✅ ALL GUIDANCE ADDITIONS ARE PROTECTED")
        print("   Backend instruction filtering is properly implemented")
    
    return len(found_indicators) >= 3 and len(problematic_lines) == 0

def validate_student_friendly_messages():
    """Validate that student-friendly messages are properly implemented"""
    print("\n🎯 Validating Student-Friendly Message Generation")
    print("=" * 60)
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from intelligent_guardrails import IntelligentGuardrailsManager
        
        guardrails = IntelligentGuardrailsManager()
        
        # Test context
        lesson_context = {
            'student_info': {'first_name': 'Andrea'},
            'topic': 'Mathematics'
        }
        
        # Generate multiple messages to test variety
        messages = []
        for i in range(5):
            request_id = f"validation_test_{i}"
            message = guardrails._generate_student_friendly_continuation_message(
                lesson_context, request_id
            )
            messages.append(message)
        
        print(f"✅ Generated {len(messages)} student-friendly messages:")
        for i, message in enumerate(messages, 1):
            print(f"   {i}. {message}")
        
        # Validate message quality
        quality_checks = {
            'contains_student_name': sum(1 for msg in messages if 'Andrea' in msg),
            'contains_topic': sum(1 for msg in messages if 'Mathematics' in msg or 'topic' in msg),
            'encouraging_tone': sum(1 for msg in messages if any(word in msg.lower() for word in ['great', 'excellent', 'good', 'well'])),
            'no_backend_terms': sum(1 for msg in messages if not any(term in msg.lower() for term in ['completion criteria', 'backend', 'teaching complete']))
        }
        
        print(f"\n📊 Message Quality Analysis:")
        for check, count in quality_checks.items():
            percentage = (count / len(messages)) * 100
            status = "✅" if percentage >= 60 else "⚠️" if percentage >= 40 else "❌"
            print(f"   {status} {check}: {count}/{len(messages)} ({percentage:.1f}%)")
        
        # Overall quality assessment
        avg_quality = sum(quality_checks.values()) / (len(quality_checks) * len(messages)) * 100
        
        if avg_quality >= 80:
            print(f"\n✅ EXCELLENT MESSAGE QUALITY: {avg_quality:.1f}%")
            return True
        elif avg_quality >= 60:
            print(f"\n✅ GOOD MESSAGE QUALITY: {avg_quality:.1f}%")
            return True
        else:
            print(f"\n⚠️ MESSAGE QUALITY NEEDS IMPROVEMENT: {avg_quality:.1f}%")
            return False
            
    except Exception as e:
        print(f"❌ Error testing student-friendly messages: {e}")
        return False

def validate_no_ui_leaks():
    """Validate that no UI leaks exist in the codebase"""
    print("\n🔒 Validating No UI Leaks in Codebase")
    print("=" * 60)
    
    # Files to check for potential UI leaks
    files_to_check = [
        'intelligent_guardrails.py',
        'teaching_rules.py'
    ]
    
    # Patterns that should NOT appear in student-facing responses
    leak_patterns = [
        r'Continue teaching until all completion criteria are met',
        r'teaching.*completion.*criteria.*met',
        r'backend.*instruction',
        r'completion.*criteria.*teaching'
    ]
    
    total_leaks = 0
    
    for filename in files_to_check:
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        if not os.path.exists(filepath):
            print(f"⚠️ File not found: {filename}")
            continue
        
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_leaks = 0
        print(f"\n📁 Checking {filename}:")
        
        for pattern in leak_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                # Check if these are in comments or logging (acceptable)
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if re.search(pattern, line, re.IGNORECASE):
                        # Check if it's in a comment, log statement, internal processing, or rule definition
                        internal_indicators = [
                            '#', 'logger.', 'print(', 'INTERNAL ONLY', 'backend',
                            'suggestion=', 'GuardrailViolation', 'rule_id=', 'message=',
                            'context={', 'violations.append'
                        ]
                        if any(indicator in line for indicator in internal_indicators):
                            print(f"   ✅ Line {line_num}: Internal use only - {line.strip()[:80]}...")
                        else:
                            print(f"   ❌ Line {line_num}: Potential UI leak - {line.strip()[:80]}...")
                            file_leaks += 1
        
        if file_leaks == 0:
            print(f"   ✅ No UI leaks found in {filename}")
        else:
            print(f"   ❌ {file_leaks} potential UI leaks found in {filename}")
        
        total_leaks += file_leaks
    
    if total_leaks == 0:
        print(f"\n✅ NO UI LEAKS DETECTED")
        print("   All backend instructions are properly contained")
        return True
    else:
        print(f"\n❌ {total_leaks} POTENTIAL UI LEAKS DETECTED")
        print("   Review the flagged lines above")
        return False

def main():
    """Run all validation tests"""
    print("🚀 UI Leak Fix Validation Suite")
    print("=" * 80)
    
    test1 = validate_guardrails_fix()
    test2 = validate_student_friendly_messages()
    test3 = validate_no_ui_leaks()
    
    print("\n" + "=" * 80)
    print("📊 VALIDATION RESULTS SUMMARY")
    print("=" * 80)
    
    results = [
        ("Guardrails Fix Implementation", test1),
        ("Student-Friendly Messages", test2),
        ("No UI Leaks", test3)
    ]
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ UI leak fix is properly implemented")
        print("✅ Students will see appropriate messages")
        print("✅ Backend instructions are contained internally")
        print("\n🚀 READY FOR PRODUCTION DEPLOYMENT")
    else:
        print("\n⚠️ SOME VALIDATIONS FAILED")
        print("❌ Additional fixes may be needed")
        print("📋 Review the failed tests above")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
