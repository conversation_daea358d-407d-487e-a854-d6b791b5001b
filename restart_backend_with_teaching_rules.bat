@echo off
echo ========================================
echo RESTARTING BACKEND WITH TEACHING RULES
echo ========================================

echo.
echo [1/4] Stopping any running backend processes...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

echo.
echo [2/4] Navigating to backend directory...
cd /d "%~dp0backend\cloud_function\lesson_manager"

echo.
echo [3/4] Clearing Python cache...
if exist __pycache__ rmdir /s /q __pycache__
if exist *.pyc del /q *.pyc

echo.
echo [4/4] Starting backend server with teaching rules...
echo Backend will start on http://localhost:5000
echo.
echo ========================================
echo TEACHING RULES SYSTEM ACTIVE
echo ========================================
echo - Minimum 10+ teaching interactions
echo - 85%+ objective coverage required  
echo - Content depth score 0.75+ required
echo - 15+ minutes teaching time required
echo - Phase/content consistency enforced
echo ========================================
echo.

python main.py

pause