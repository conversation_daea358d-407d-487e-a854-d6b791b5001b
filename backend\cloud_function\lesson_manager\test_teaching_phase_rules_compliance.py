#!/usr/bin/env python3
"""
Focused test to verify teaching phase rules compliance and prevent premature quiz triggering.
"""

import sys
import requests
import json
import time

def test_teaching_phase_rules():
    """
    Test that teaching phase follows comprehensive rules:
    1. Minimum 8 interactions before quiz
    2. Objectives coverage requirements
    3. Content depth requirements
    4. Time-based requirements (37.5 min trigger)
    5. No premature quiz triggering
    """
    
    print("🎓 TEACHING PHASE RULES COMPLIANCE TEST")
    print("=" * 50)
    
    backend_url = "http://localhost:5000"
    test_session_id = f"teaching-rules-test-{int(time.time())}"
    
    test_data = {
        "session_id": test_session_id,
        "student_id": "test_teaching_rules",
        "lesson_ref": "P5_MAT_180",
        "grade": "Primary 5",
        "subject": "Mathematics"
    }
    
    print(f"📋 Test Session: {test_session_id}")
    
    try:
        # Start lesson
        print("\n🚀 Starting lesson...")
        start_response = requests.post(f"{backend_url}/start_lesson", json=test_data, timeout=30)
        
        if start_response.status_code != 200:
            print(f"❌ Failed to start lesson: {start_response.status_code}")
            return False
        
        # Progress through diagnostic quickly
        print("🎯 Completing diagnostic phase...")
        diagnostic_queries = [
            "I understand basic math concepts",
            "I can solve equations",
            "I know fractions and decimals", 
            "I can work with percentages",
            "I'm ready for advanced topics"
        ]
        
        current_phase = "diagnostic"
        for i, query in enumerate(diagnostic_queries):
            if 'teaching' in current_phase:
                break
                
            response = requests.post(
                f"{backend_url}/lesson_interaction",
                json={**test_data, "user_query": query},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                current_phase = result.get('data', {}).get('current_phase', current_phase)
                print(f"   Q{i+1}: {current_phase}")
            else:
                print(f"   ❌ Diagnostic Q{i+1} failed")
        
        # Now test teaching phase rules
        print(f"\n🎓 Testing teaching phase rules...")
        print(f"Current phase: {current_phase}")
        
        if 'teaching' not in current_phase:
            print("⚠️ Not in teaching phase, forcing progression...")
        
        # Test teaching interactions with premature quiz detection
        teaching_queries = [
            "Can you explain this concept in more detail?",
            "I understand. What's the next important point?",
            "This makes sense. Can you show me examples?",
            "I'm following. How does this apply practically?",
            "That's clear. What else should I know?",
            "I see the connection. Can you elaborate further?",
            "This is helpful. What are the key principles?",
            "I understand this part. What's next?"
        ]
        
        teaching_interactions = 0
        premature_quiz_detected = False
        objectives_progress = []
        content_depth_progress = []
        
        print(f"\n📊 Teaching Interaction Analysis:")
        print(f"{'#':<3} {'Phase':<20} {'Objectives':<12} {'Depth':<8} {'Status'}")
        print("-" * 60)
        
        for i, query in enumerate(teaching_queries):
            response = requests.post(
                f"{backend_url}/lesson_interaction", 
                json={**test_data, "user_query": query},
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ Teaching interaction {i+1} failed")
                continue
            
            result = response.json()
            data = result.get('data', {})
            current_phase = data.get('current_phase', 'unknown')
            
            # Extract teaching metrics
            objectives_covered = data.get('objectives_covered', 0)
            coverage_percentage = data.get('coverage_percentage', 0)
            content_depth = data.get('teaching_depth_score', 0)
            teaching_complete = data.get('teaching_complete', False)
            
            teaching_interactions += 1
            objectives_progress.append(objectives_covered)
            content_depth_progress.append(content_depth)
            
            # Check for premature quiz
            if 'quiz' in current_phase.lower() and teaching_interactions < 8:
                premature_quiz_detected = True
                status = "❌ PREMATURE QUIZ!"
            elif 'quiz' in current_phase.lower():
                status = "✅ Quiz Ready"
            elif teaching_complete:
                status = "✅ Teaching Complete"
            else:
                status = "🔄 Continuing"
            
            print(f"{i+1:<3} {current_phase:<20} {objectives_covered:<12} {content_depth:<8.2f} {status}")
            
            # Stop if quiz triggered
            if 'quiz' in current_phase.lower():
                break
        
        # Analyze results
        print(f"\n📋 TEACHING PHASE ANALYSIS:")
        print(f"   Teaching Interactions: {teaching_interactions}")
        print(f"   Minimum Required: 8")
        print(f"   Premature Quiz Detected: {'❌ YES' if premature_quiz_detected else '✅ NO'}")
        
        if objectives_progress:
            final_objectives = objectives_progress[-1]
            print(f"   Final Objectives Covered: {final_objectives}")
        
        if content_depth_progress:
            final_depth = content_depth_progress[-1]
            print(f"   Final Content Depth: {final_depth:.2f}")
        
        # Test specific teaching rules
        print(f"\n🔍 TEACHING RULES COMPLIANCE:")
        
        rules_passed = 0
        total_rules = 4
        
        # Rule 1: Minimum interactions
        min_interactions_met = teaching_interactions >= 10
        print(f"   1. Minimum 8 interactions: {'✅ PASS' if min_interactions_met else '❌ FAIL'} ({teaching_interactions})")
        if min_interactions_met:
            rules_passed += 1
        
        # Rule 2: No premature quiz
        no_premature_quiz = not premature_quiz_detected
        print(f"   2. No premature quiz: {'✅ PASS' if no_premature_quiz else '❌ FAIL'}")
        if no_premature_quiz:
            rules_passed += 1
        
        # Rule 3: Objectives progress
        objectives_progressing = len(set(objectives_progress)) > 1 if objectives_progress else False
        print(f"   3. Objectives progressing: {'✅ PASS' if objectives_progressing else '❌ FAIL'}")
        if objectives_progressing:
            rules_passed += 1
        
        # Rule 4: Content depth progress
        depth_progressing = len(set([round(d, 1) for d in content_depth_progress])) > 1 if content_depth_progress else False
        print(f"   4. Content depth progressing: {'✅ PASS' if depth_progressing else '❌ FAIL'}")
        if depth_progressing:
            rules_passed += 1
        
        # Final assessment
        print(f"\n🎯 FINAL ASSESSMENT:")
        print(f"   Rules Passed: {rules_passed}/{total_rules}")
        print(f"   Overall Status: {'✅ COMPLIANT' if rules_passed >= 3 else '❌ NON-COMPLIANT'}")
        
        return rules_passed >= 3
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def check_backend_status():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("🔧 TEACHING PHASE RULES COMPLIANCE TEST")
    print("Testing: Minimum interactions, objectives, depth, no premature quiz")
    print("=" * 70)
    
    # Check backend status
    if not check_backend_status():
        print("❌ Backend is not running on localhost:5000")
        print("Please start the backend server first")
        sys.exit(1)
    
    # Run the test
    success = test_teaching_phase_rules()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 TEACHING RULES TEST PASSED!")
        print("✅ Teaching phase follows comprehensive rules")
        print("✅ No premature quiz triggering detected")
        print("✅ Proper progression and metrics tracking")
    else:
        print("💥 TEACHING RULES TEST FAILED!")
        print("❌ Teaching phase rules not properly followed")
        sys.exit(1)