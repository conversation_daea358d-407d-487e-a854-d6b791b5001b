#!/usr/bin/env python3
"""
Debug Diagnostic Progression
===========================

This script examines the diagnostic phase responses to understand
how the progression from q1 → q2 → q3 → q4 → q5 works.
"""

import requests
import json
import time

def debug_diagnostic_progression():
    """Debug the diagnostic progression mechanism"""
    
    url = "http://localhost:5000/api/enhance-content"
    session_id = f"debug_diagnostic_{int(time.time())}"
    student_id = f"debug_student_{int(time.time())}"
    
    headers = {
        'Content-Type': 'application/json',
        'X-Testing-Mode': 'true',
        'X-Student-ID': student_id
    }
    
    print("🔍 DEBUGGING: Diagnostic Phase Progression")
    print("=" * 70)
    
    # Start with the first diagnostic question
    current_phase = "smart_diagnostic_start"
    
    # Try different types of answers to see what triggers progression
    test_answers = [
        # Question 1 - Try different answer types
        "I know basic math",
        "I understand addition and subtraction well",
        "I can solve simple problems",
        "I'm good at mathematics",
        "Level 3"  # Maybe it expects a level response?
    ]
    
    for i, answer in enumerate(test_answers, 1):
        print(f"\n📝 ATTEMPT {i}: Testing answer type")
        print(f"Current Phase: {current_phase}")
        print(f"Answer: {answer}")
        print("-" * 50)
        
        data = {
            "session_id": session_id,
            "lesson_ref": "P5_MAT_180",
            "subject": "mathematics",
            "grade": "primary_5",
            "student_input": answer,
            "current_phase": current_phase
        }
        
        try:
            response = requests.post(url, json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Extract key information
                new_phase = response_data.get('data', {}).get('current_phase')
                next_phase = response_data.get('data', {}).get('state_updates', {}).get('new_phase')
                diagnostic_complete = response_data.get('data', {}).get('diagnostic_complete', False)
                ai_response = response_data.get('data', {}).get('enhanced_content', '')
                
                print(f"Status: 200 ✅")
                print(f"Current Phase: {new_phase}")
                print(f"Next Phase: {next_phase}")
                print(f"Diagnostic Complete: {diagnostic_complete}")
                print(f"AI Response: {ai_response[:200]}...")
                
                # Check for progression indicators
                if next_phase and next_phase != current_phase:
                    print(f"🎉 PROGRESSION DETECTED: {current_phase} → {next_phase}")
                    current_phase = next_phase
                    
                    # If we progressed, try to continue
                    if 'q2' in next_phase:
                        print("✅ Successfully progressed to Q2!")
                        break
                elif new_phase and new_phase != current_phase:
                    print(f"🎉 PHASE CHANGE: {current_phase} → {new_phase}")
                    current_phase = new_phase
                else:
                    print("❌ No progression detected")
                
                # Look for clues in the AI response about what it expects
                if 'level' in ai_response.lower():
                    print("💡 HINT: Response mentions 'level' - might need level-based answer")
                if 'question' in ai_response.lower():
                    print("💡 HINT: Response contains question - might need specific answer format")
                
                # Print full response for analysis
                print(f"\nFULL RESPONSE DATA:")
                print(json.dumps(response_data, indent=2, default=str))
                
            else:
                print(f"Status: {response.status_code} ❌")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"Exception: {e}")
        
        print("\n" + "=" * 70)
        time.sleep(2)  # Delay between attempts
    
    print(f"\n🎯 FINAL PHASE: {current_phase}")
    
    if 'q2' in current_phase:
        print("✅ SUCCESS: Found way to progress to Q2!")
    else:
        print("❌ FAILED: Could not progress past Q1")

if __name__ == "__main__":
    debug_diagnostic_progression()