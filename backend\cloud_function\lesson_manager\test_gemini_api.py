#!/usr/bin/env python3
"""
Test Gemini API connectivity
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_gemini_api():
    """Test Gemini API connectivity"""
    try:
        logger.info("🚀 Testing Gemini API connectivity...")
        
        # Load environment variables
        load_dotenv()
        
        # Check if API key is available
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            logger.error("❌ GEMINI_API_KEY not found in environment")
            return False
        
        logger.info(f"✅ GEMINI_API_KEY found: {api_key[:10]}...")
        
        # Try to import and initialize Gemini
        import google.generativeai as genai
        
        genai.configure(api_key=api_key)
        
        # Create model with safety settings disabled
        model = genai.GenerativeModel(
            model_name='gemini-2.5-flash-preview-05-20',
            safety_settings=None  # Remove all safety filters
        )
        
        logger.info("✅ Gemini model initialized successfully")
        
        # Test simple generation
        logger.info("📝 Testing simple text generation...")
        
        response = model.generate_content(
            "Say 'Hello, this is a test of the Gemini API!'",
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,
                max_output_tokens=100
            )
        )
        
        logger.info(f"✅ Response received: {response.text}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Gemini API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    success = await test_gemini_api()
    
    if success:
        print("\n🎉 GEMINI API TEST: SUCCESS!")
        print("✅ Gemini API is working correctly")
    else:
        print("\n⚠️  GEMINI API TEST: FAILED")
        print("❌ Gemini API has connectivity issues")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
