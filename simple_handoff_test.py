#!/usr/bin/env python3
"""
Simple AI Instructor Handoff Test
Tests the exact format from the working lesson log.
"""

import requests
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_exact_format():
    """Test using the exact format from the working lesson log"""
    
    # This is the exact format from the working lesson log
    test_payload = {
        "student_id": "andrea_ugono_33305",
        "lesson_ref": "P5-COM-088",
        "content_to_enhance": "I understand all the concepts now. I'm ready for the quiz.",
        "country": "Nigeria",
        "curriculum": "National Curriculum",
        "grade": "primary-5",
        "subject": "Computing",
        "session_id": "fallback-handoff-test-123",
        "chat_history": []
    }
    
    logger.info("🧪 Testing with exact format from working lesson...")
    logger.info(f"📝 Payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        response = requests.post(
            'http://localhost:5000/api/enhance-content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        logger.info(f"📊 Response Status: {response.status_code}")
        logger.info(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ SUCCESS! Request processed successfully")
            logger.info(f"📝 Response keys: {list(result.keys())}")
            
            if 'data' in result:
                data = result['data']
                logger.info(f"📝 Data keys: {list(data.keys())}")
                
                # Check for handoff indicators
                state_updates = data.get('state_updates', {})
                current_phase = data.get('current_phase', '')
                
                logger.info(f"📝 Current Phase: {current_phase}")
                logger.info(f"📝 State Updates: {json.dumps(state_updates, indent=2)}")
                
                # Look for handoff indicators
                handoff_indicators = [
                    'quiz' in current_phase.lower(),
                    state_updates.get('teaching_complete'),
                    state_updates.get('ai_instructor_handoff'),
                    state_updates.get('teaching_phase_complete')
                ]
                
                logger.info(f"📊 Handoff Indicators: {handoff_indicators}")
                
                if any(handoff_indicators):
                    logger.info("🎉 HANDOFF DETECTED!")
                else:
                    logger.info("⚠️ No handoff detected")
            
            return True
            
        else:
            logger.error(f"❌ Request failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return False

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get('http://localhost:5000/health-check', timeout=10)
        logger.info(f"📊 Health Check Status: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ Health check passed")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Simple AI Instructor Handoff Test")
    logger.info("=" * 50)
    
    # Test health check first
    logger.info("\n🧪 Testing health check...")
    health_ok = test_health_check()
    
    # Test the API
    logger.info("\n🧪 Testing API with exact format...")
    api_ok = test_exact_format()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Summary:")
    logger.info(f"  Health Check: {'✅ PASS' if health_ok else '❌ FAIL'}")
    logger.info(f"  API Test: {'✅ PASS' if api_ok else '❌ FAIL'}")
    
    if health_ok and api_ok:
        logger.info("🎉 All tests passed!")
    else:
        logger.warning("⚠️ Some tests failed")