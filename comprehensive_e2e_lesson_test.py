#!/usr/bin/env python3
"""
Comprehensive End-to-End Lesson Test with Enhanced Teaching Completion System
============================================================================

This test follows the complete lesson flow from diagnostic through teaching
to verify that the enhanced teaching completion system is working correctly.
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveE2ETest:
    def __init__(self):
        self.base_url = "http://localhost:5000/api"
        self.session_id = f"comprehensive_test_{int(time.time())}"
        self.student_id = f"test_student_{int(time.time())}"
        self.lesson_ref = "P5_MAT_180"
        self.current_phase = "smart_diagnostic_start"
        self.enhanced_features_detected = []
        
    def make_request(self, endpoint, data):
        """Make HTTP request with proper headers"""
        url = f"{self.base_url}/{endpoint}"
        headers = {
            'Content-Type': 'application/json',
            'X-Testing-Mode': 'true',
            'X-Student-ID': self.student_id
        }
        
        try:
            response = requests.post(url, json=data, headers=headers, timeout=30)
            logger.info(f"🌐 REQUEST: {endpoint} - Status: {response.status_code}")
            
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.error(f"   Error: {response.text}")
                return False, None
                
        except Exception as e:
            logger.error(f"   Exception: {e}")
            return False, None
    
    def complete_diagnostic_phase(self):
        """Complete the diagnostic phase to reach teaching"""
        logger.info("\n🔍 PHASE 1: Completing Diagnostic Phase")
        
        # Answer diagnostic questions to progress q1 → q2 → q3 → q4 → q5 → teaching
        diagnostic_answers = [
            "I know basic addition and subtraction",
            "I can work with simple fractions", 
            "I understand place value",
            "I can solve basic word problems",
            "I'm ready to learn more advanced concepts"
        ]
        
        # Expected diagnostic phase progression
        expected_phases = [
            "smart_diagnostic_q1",
            "smart_diagnostic_q2", 
            "smart_diagnostic_q3",
            "smart_diagnostic_q4",
            "smart_diagnostic_q5"
        ]
        
        for i, answer in enumerate(diagnostic_answers):
            current_question = i + 1
            logger.info(f"   📝 Diagnostic Question {current_question}")
            logger.info(f"      Current Phase: {self.current_phase}")
            
            data = {
                "session_id": self.session_id,
                "lesson_ref": self.lesson_ref,
                "subject": "mathematics",
                "grade": "primary_5",
                "student_input": answer,
                "current_phase": self.current_phase
            }
            
            success, response = self.make_request("enhance-content", data)
            
            if success and response:
                # Update phase from response
                new_phase = response.get('data', {}).get('current_phase')
                next_phase = response.get('data', {}).get('state_updates', {}).get('new_phase')
                diagnostic_complete = response.get('data', {}).get('diagnostic_complete', False)
                
                # Use the new_phase if available, otherwise current_phase
                if next_phase:
                    self.current_phase = next_phase
                elif new_phase:
                    self.current_phase = new_phase
                
                logger.info(f"      New Phase: {self.current_phase}")
                logger.info(f"      Diagnostic Complete: {diagnostic_complete}")
                
                # Check if we've moved to teaching phase
                if diagnostic_complete or 'teaching' in self.current_phase.lower():
                    logger.info("✅ Diagnostic phase completed - moved to teaching!")
                    return True
                
                # Check if we're progressing through diagnostic questions correctly
                if current_question < 5:
                    expected_next = f"smart_diagnostic_q{current_question + 1}"
                    if expected_next in self.current_phase:
                        logger.info(f"      ✅ Progressed to {expected_next}")
                    else:
                        logger.warning(f"      ⚠️ Expected {expected_next}, got {self.current_phase}")
            else:
                logger.error(f"      ❌ Failed to get response for question {current_question}")
                return False
            
            time.sleep(1)  # Small delay between questions
        
        # After all 5 questions, check if we transitioned to teaching
        if 'teaching' in self.current_phase.lower():
            logger.info("✅ Diagnostic phase completed - moved to teaching!")
            return True
        else:
            logger.warning(f"⚠️ Diagnostic phase not completed. Final phase: {self.current_phase}")
            return False
    
    def test_enhanced_teaching_system(self):
        """Test the enhanced teaching completion system"""
        logger.info("\n🎓 PHASE 2: Testing Enhanced Teaching Completion System")
        
        # Test incomplete teaching scenario (should continue teaching)
        logger.info("\n   📋 TEST 2A: Incomplete Teaching Scenario")
        
        incomplete_data = {
            "session_id": self.session_id,
            "lesson_ref": self.lesson_ref,
            "subject": "mathematics",
            "student_input": "I think I understand. Can we do the quiz now?",
            "current_phase": self.current_phase,
            "teaching_interactions": 3,  # Below minimum
            "objectives_covered": 2,     # Below 100%
            "total_objectives": 5,
            "content_depth_score": 0.6,  # Below threshold
            "grade": "primary_5",
            "teaching_level": 5
        }
        
        success, response = self.test_teaching_scenario(incomplete_data, "incomplete")
        
        if not success:
            return False
        
        # Test complete teaching scenario (should allow quiz)
        logger.info("\n   📋 TEST 2B: Complete Teaching Scenario")
        
        complete_data = {
            "session_id": self.session_id,
            "lesson_ref": self.lesson_ref,
            "subject": "mathematics",
            "student_input": "I've mastered all the concepts. I'm ready for the quiz!",
            "current_phase": self.current_phase,
            "teaching_interactions": 12,  # Above minimum
            "objectives_covered": 5,      # 100% coverage
            "total_objectives": 5,
            "content_depth_score": 0.9,   # Above threshold
            "grade": "primary_5",
            "teaching_level": 5
        }
        
        success, response = self.test_teaching_scenario(complete_data, "complete")
        
        return success
    
    def test_teaching_scenario(self, data, scenario_type):
        """Test a specific teaching scenario"""
        success, response = self.make_request("enhance-content", data)
        
        if not success:
            logger.error(f"❌ {scenario_type.title()} teaching test failed - no response")
            return False
        
        # Analyze response for enhanced system indicators
        response_str = json.dumps(response, default=str).lower()
        
        enhanced_indicators = {
            'adaptive_requirements': 'adaptive requirements' in response_str,
            'teaching_validation': any(term in response_str for term in [
                'teaching validation', 'validate_teaching_completion', 'teaching complete'
            ]),
            'primary_driver': any(term in response_str for term in [
                'primary driver', '100% objectives', 'optimal objective'
            ]),
            'guardrails': any(term in response_str for term in [
                'guardrails applied', 'guardrail', 'intelligent guardrails'
            ]),
            'completion_logic': any(term in response_str for term in [
                'completion_reason', 'teaching_truly_complete', 'handoff'
            ])
        }
        
        # Check AI response content
        ai_response = response.get('data', {}).get('enhanced_content', '')
        
        # Analyze teaching behavior
        if scenario_type == "incomplete":
            # Should continue teaching, not allow quiz
            teaching_continues = any(phrase in ai_response.lower() for phrase in [
                'let\'s continue', 'more practice', 'before we move', 'need to cover',
                'let me explain', 'understand better'
            ])
            
            quiz_blocked = any(phrase in ai_response.lower() for phrase in [
                'not ready', 'need more', 'continue learning', 'more preparation'
            ])
            
            expected_behavior = teaching_continues or quiz_blocked
            behavior_description = "Teaching continuation or quiz blocking"
            
        else:  # complete scenario
            # Should allow quiz transition
            quiz_allowed = any(phrase in ai_response.lower() for phrase in [
                'ready for quiz', 'let\'s test', 'time for assessment', 'quiz time',
                'demonstrate what you\'ve learned'
            ])
            
            expected_behavior = quiz_allowed
            behavior_description = "Quiz transition allowed"
        
        # Log results
        found_indicators = [name for name, found in enhanced_indicators.items() if found]
        
        logger.info(f"      Enhanced Indicators: {len(found_indicators)}/5")
        for indicator in found_indicators:
            logger.info(f"         ✅ {indicator}")
            if indicator not in self.enhanced_features_detected:
                self.enhanced_features_detected.append(indicator)
        
        logger.info(f"      Expected Behavior: {behavior_description}")
        logger.info(f"      Behavior Detected: {'✅ YES' if expected_behavior else '❌ NO'}")
        logger.info(f"      AI Response: {ai_response[:100]}...")
        
        # Determine success
        indicators_found = len(found_indicators) >= 2
        behavior_correct = expected_behavior
        
        if indicators_found and behavior_correct:
            logger.info(f"✅ {scenario_type.title()} teaching test: PASSED")
            return True
        elif indicators_found:
            logger.info(f"⚠️ {scenario_type.title()} teaching test: PARTIAL (indicators found, behavior unclear)")
            return True
        elif behavior_correct:
            logger.info(f"⚠️ {scenario_type.title()} teaching test: PARTIAL (behavior correct, few indicators)")
            return True
        else:
            logger.info(f"❌ {scenario_type.title()} teaching test: FAILED")
            return False
    
    def run_comprehensive_test(self):
        """Run the complete test suite"""
        logger.info("🚀 STARTING: Comprehensive E2E Enhanced Teaching System Test")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        # Phase 1: Complete diagnostic
        if not self.complete_diagnostic_phase():
            logger.error("❌ Failed to complete diagnostic phase")
            return False
        
        # Phase 2: Test enhanced teaching system
        if not self.test_enhanced_teaching_system():
            logger.error("❌ Enhanced teaching system tests failed")
            return False
        
        # Generate final report
        self.generate_final_report(time.time() - start_time)
        
        return True
    
    def generate_final_report(self, duration):
        """Generate comprehensive final report"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 COMPREHENSIVE E2E TEST RESULTS")
        logger.info("=" * 80)
        
        logger.info(f"🎯 ENHANCED FEATURES DETECTED: {len(self.enhanced_features_detected)}")
        for feature in self.enhanced_features_detected:
            logger.info(f"   ✅ {feature}")
        
        logger.info(f"\n📈 TEST STATISTICS:")
        logger.info(f"   Duration: {duration:.2f} seconds")
        logger.info(f"   Session ID: {self.session_id}")
        logger.info(f"   Final Phase: {self.current_phase}")
        
        # Overall assessment
        if len(self.enhanced_features_detected) >= 3:
            logger.info("\n🎉 OVERALL RESULT: ✅ SUCCESS")
            logger.info("   Enhanced teaching completion system is WORKING!")
            logger.info("   Multiple enhanced features detected and functioning")
        elif len(self.enhanced_features_detected) >= 1:
            logger.info("\n⚠️ OVERALL RESULT: 🔶 PARTIAL SUCCESS")
            logger.info("   Some enhanced features detected")
            logger.info("   System is partially operational")
        else:
            logger.info("\n❌ OVERALL RESULT: ❌ NEEDS ATTENTION")
            logger.info("   Enhanced features not clearly detected")
            logger.info("   System may need investigation")

def main():
    """Main test execution"""
    test_runner = ComprehensiveE2ETest()
    
    try:
        success = test_runner.run_comprehensive_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)