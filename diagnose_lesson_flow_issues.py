#!/usr/bin/env python3
"""
Diagnose Lesson Flow Issues

This script identifies why the teaching rules changes aren't being reflected
in the actual lesson behavior.
"""

import os
import sys
import re
import requests
import time
from datetime import datetime

def check_server_status():
    """Check if the backend server is running and responsive"""
    print("🔍 CHECKING SERVER STATUS")
    print("-" * 40)
    
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running and responsive")
            return True
        else:
            print(f"❌ Backend server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking server status: {e}")
        return False

def check_main_py_changes():
    """Check if our changes are present in main.py"""
    print("\n📝 CHECKING MAIN.PY CHANGES")
    print("-" * 40)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    main_py_path = os.path.join(backend_path, 'main.py')
    
    if not os.path.exists(main_py_path):
        print(f"❌ main.py not found at {main_py_path}")
        return False
    
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for our changes
    changes_to_check = [
        (r'min_interactions = 10', "Minimum interactions set to 10"),
        (r'INCLUDES teaching_start phase', "Teaching start phase comment"),
        (r'fallback_objectives_threshold = 85\.0', "Objective threshold 85%"),
        (r'min_content_depth = 0\.75', "Content depth 0.75"),
        (r'min_teaching_time = 15\.0', "Teaching time 15 minutes")
    ]
    
    changes_found = 0
    for pattern, description in changes_to_check:
        if re.search(pattern, content):
            print(f"✅ {description}")
            changes_found += 1
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\nChanges found: {changes_found}/{len(changes_to_check)}")
    return changes_found == len(changes_to_check)

def check_server_restart_needed():
    """Check if the server needs to be restarted"""
    print("\n🔄 CHECKING SERVER RESTART STATUS")
    print("-" * 40)
    
    # Check main.py modification time
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    main_py_path = os.path.join(backend_path, 'main.py')
    
    if os.path.exists(main_py_path):
        main_py_mtime = os.path.getmtime(main_py_path)
        main_py_time = datetime.fromtimestamp(main_py_mtime)
        print(f"📝 main.py last modified: {main_py_time}")
        
        # Check if modification was recent (within last hour)
        current_time = datetime.now()
        time_diff = current_time - main_py_time
        
        if time_diff.total_seconds() < 3600:  # Less than 1 hour
            print("⚠️ main.py was modified recently - server restart likely needed")
            return True
        else:
            print("✅ main.py modifications are older - server may have restarted")
            return False
    
    return False

def test_teaching_rules_endpoint():
    """Test if the teaching rules are being applied by making a test request"""
    print("\n🧪 TESTING TEACHING RULES ENDPOINT")
    print("-" * 40)
    
    # Test payload
    test_payload = {
        "student_id": "test_student_rules",
        "lesson_ref": "P5-MAT-005",
        "content_to_enhance": "Start diagnostic assessment",
        "country": "Nigeria",
        "curriculum": "National Curriculum",
        "grade": "Primary 5",
        "subject": "Mathematics",
        "session_id": f"test-rules-{int(time.time())}",
        "chat_history": []
    }
    
    try:
        response = requests.post(
            "http://localhost:5000/api/enhance-content",
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API request successful")
            
            # Check response for teaching rules indicators
            enhanced_content = result.get('enhanced_content', '')
            current_phase = result.get('current_phase', 'unknown')
            
            print(f"📊 Current phase: {current_phase}")
            print(f"📝 Content length: {len(enhanced_content)} characters")
            
            # Look for diagnostic start indicators
            if 'diagnostic' in current_phase.lower():
                print("✅ Lesson started in diagnostic phase (expected)")
                return True
            else:
                print(f"⚠️ Unexpected starting phase: {current_phase}")
                return False
                
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def check_log_files():
    """Check for recent log files that might show server activity"""
    print("\n📋 CHECKING LOG FILES")
    print("-" * 40)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    
    # Check for log files
    log_patterns = ['*.log', 'logs/*.log', 'app.log']
    
    for pattern in log_patterns:
        log_path = os.path.join(backend_path, pattern.replace('*', 'app'))
        if os.path.exists(log_path):
            mtime = os.path.getmtime(log_path)
            log_time = datetime.fromtimestamp(mtime)
            print(f"📄 Found log: {pattern} (modified: {log_time})")
        else:
            print(f"❌ No log found: {pattern}")
    
    # Check for lesson test files
    lesson_files = [f for f in os.listdir('.') if f.endswith('_lesson.txt')]
    if lesson_files:
        print(f"\n📚 Found {len(lesson_files)} lesson test files:")
        for file in lesson_files[-3:]:  # Show last 3
            mtime = os.path.getmtime(file)
            file_time = datetime.fromtimestamp(mtime)
            print(f"   {file} (modified: {file_time})")
    
    return True

def provide_recommendations():
    """Provide recommendations to fix the issues"""
    print("\n💡 RECOMMENDATIONS")
    print("=" * 60)
    
    print("Based on the analysis, here are the likely issues and solutions:")
    print()
    
    print("🔄 ISSUE 1: Server Not Restarted")
    print("   Problem: Changes to main.py require server restart")
    print("   Solution: Restart the backend server")
    print("   Commands:")
    print("     1. Stop current server (Ctrl+C)")
    print("     2. Restart: python main.py")
    print("     3. Or use: python -m flask run --host=0.0.0.0 --port=5000")
    print()
    
    print("🐍 ISSUE 2: Python Module Caching")
    print("   Problem: Python may be using cached bytecode")
    print("   Solution: Clear Python cache")
    print("   Commands:")
    print("     1. Delete __pycache__ folders")
    print("     2. Or restart with: python -B main.py")
    print()
    
    print("🔧 ISSUE 3: Environment Issues")
    print("   Problem: Wrong environment or dependencies")
    print("   Solution: Verify environment")
    print("   Commands:")
    print("     1. Check Python version: python --version")
    print("     2. Check working directory")
    print("     3. Verify dependencies are installed")
    print()
    
    print("📝 ISSUE 4: Code Not Loaded")
    print("   Problem: Changes not in the running code")
    print("   Solution: Verify and reload")
    print("   Commands:")
    print("     1. Check file timestamps")
    print("     2. Restart server completely")
    print("     3. Test with fresh session")

def main():
    """Main diagnostic function"""
    print("🔍 LESSON FLOW ISSUES DIAGNOSTIC")
    print("=" * 60)
    print("Diagnosing why teaching rules changes aren't reflected in lesson behavior")
    print("=" * 60)
    
    # Run diagnostics
    server_ok = check_server_status()
    changes_ok = check_main_py_changes()
    restart_needed = check_server_restart_needed()
    
    if server_ok:
        endpoint_ok = test_teaching_rules_endpoint()
    else:
        endpoint_ok = False
    
    check_log_files()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    issues = []
    
    if not server_ok:
        issues.append("❌ Backend server not running")
    else:
        print("✅ Backend server is running")
    
    if not changes_ok:
        issues.append("❌ Changes not found in main.py")
    else:
        print("✅ Changes are present in main.py")
    
    if restart_needed:
        issues.append("⚠️ Server restart needed (recent changes)")
    else:
        print("✅ No immediate restart needed")
    
    if not endpoint_ok and server_ok:
        issues.append("❌ API endpoint not responding correctly")
    elif endpoint_ok:
        print("✅ API endpoint responding")
    
    if issues:
        print(f"\n🚨 ISSUES FOUND ({len(issues)}):")
        for issue in issues:
            print(f"   {issue}")
        
        print("\n🔧 MOST LIKELY CAUSE:")
        if not server_ok:
            print("   The backend server is not running")
        elif restart_needed:
            print("   The server needs to be restarted to pick up changes")
        else:
            print("   Code changes may not be loaded properly")
    else:
        print("\n✅ NO OBVIOUS ISSUES FOUND")
        print("   The changes should be working. Check lesson flow manually.")
    
    provide_recommendations()
    
    return len(issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)