#!/usr/bin/env python3
"""
Test AI Instructor Backend Synchronization

This test verifies that the synchronization fixes between AI Instructor and backend are working:
1. AI Instructor's objectives assessment is authoritative
2. AI Instructor controls phase transitions with intelligent guardrails
3. Backend calculations are synchronized with AI assessments
"""

import sys
import os
import json
import time
import requests
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test configuration
BACKEND_URL = "http://localhost:5000"
TEST_SESSION_ID = f"sync_test_{int(time.time())}"

def test_objectives_coverage_synchronization():
    """
    Test that AI Instructor's objectives assessment is used as authoritative source
    """
    logger.info("🧪 Testing objectives coverage synchronization...")
    
    # Start a lesson with known objectives
    start_payload = {
        "session_id": TEST_SESSION_ID,
        "lesson_ref": "P5_MAT_180",
        "student_name": "Test Student",
        "student_id": "sync_test_student"
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/start_lesson", json=start_payload, timeout=30)
        if response.status_code != 200:
            logger.error(f"Failed to start lesson: {response.status_code} - {response.text}")
            return False
        
        logger.info("✅ Lesson started successfully")
        
        # Send a teaching interaction that should trigger objectives assessment
        teaching_payload = {
            "session_id": TEST_SESSION_ID,
            "user_input": "I understand fractions now. I can add 1/2 + 1/4 = 3/4 and I know how to find common denominators.",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 5
            }
        }
        
        # Add AI state update that includes objectives assessment
        ai_state_update = {
            "new_phase": "teaching",
            "objectives_covered": 3,  # AI Instructor says 3 objectives covered
            "objectives_coverage_percentage": 75,
            "teaching_complete": False
        }
        
        # Simulate AI response with state update
        teaching_payload["ai_state_update"] = ai_state_update
        
        response = requests.post(f"{BACKEND_URL}/enhance_content", json=teaching_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if AI Instructor's objectives assessment was used
            if "🤖 Using AI Instructor objectives assessment: 3" in str(result):
                logger.info("✅ AI Instructor objectives assessment is being used as authoritative")
                return True
            else:
                logger.warning("⚠️ AI Instructor objectives assessment may not be prioritized")
                return False
        else:
            logger.error(f"Teaching interaction failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error in objectives synchronization test: {e}")
        return False

def test_phase_transition_control():
    """
    Test that AI Instructor controls phase transitions with intelligent guardrails
    """
    logger.info("🧪 Testing phase transition control...")
    
    try:
        # Send a teaching interaction where AI requests quiz transition
        quiz_request_payload = {
            "session_id": TEST_SESSION_ID,
            "user_input": "I'm ready for the quiz now. I understand all the concepts we've covered.",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 12,
                "objectives_covered": 4
            }
        }
        
        # AI Instructor requests quiz transition
        ai_state_update = {
            "new_phase": "quiz_initiate",
            "ai_phase_request": "quiz_initiate",
            "objectives_covered": 4,
            "teaching_complete": True
        }
        
        quiz_request_payload["ai_state_update"] = ai_state_update
        
        response = requests.post(f"{BACKEND_URL}/enhance_content", json=quiz_request_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            # Check if AI Instructor's phase request is being respected
            if "🤖 AI Instructor requested quiz transition" in response_text:
                logger.info("✅ AI Instructor phase transition request is being processed")
                
                # Check if guardrails are providing feedback without blocking
                if "guardrail_feedback" in response_text or "Quiz transition approved by guardrails" in response_text:
                    logger.info("✅ Intelligent guardrails are working correctly")
                    return True
                else:
                    logger.warning("⚠️ Guardrails may not be providing proper feedback")
                    return False
            else:
                logger.warning("⚠️ AI Instructor phase transition request may not be respected")
                return False
        else:
            logger.error(f"Quiz transition test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error in phase transition test: {e}")
        return False

def test_guardrail_violations():
    """
    Test that guardrails provide feedback when violations occur but still respect AI decisions
    """
    logger.info("🧪 Testing guardrail violations handling...")
    
    try:
        # Send a request where AI wants quiz but guardrails should flag violations
        violation_payload = {
            "session_id": TEST_SESSION_ID,
            "user_input": "Let's do the quiz now!",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 2,  # Too few interactions
                "objectives_covered": 0  # No objectives covered
            }
        }
        
        # AI Instructor still requests quiz despite violations
        ai_state_update = {
            "new_phase": "quiz_initiate",
            "ai_phase_request": "quiz_initiate",
            "objectives_covered": 0,  # AI also reports no objectives covered
            "teaching_complete": False
        }
        
        violation_payload["ai_state_update"] = ai_state_update
        
        response = requests.post(f"{BACKEND_URL}/enhance_content", json=violation_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            # Check if guardrail violations are detected
            if "GUARDRAIL VIOLATIONS" in response_text:
                logger.info("✅ Guardrail violations are being detected")
                
                # Check if feedback is provided to AI Instructor
                if "guardrail_feedback" in response_text:
                    logger.info("✅ Guardrail feedback is being provided to AI Instructor")
                    return True
                else:
                    logger.warning("⚠️ Guardrail feedback may not be properly structured")
                    return False
            else:
                logger.warning("⚠️ Guardrail violations may not be detected")
                return False
        else:
            logger.error(f"Guardrail violation test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error in guardrail violations test: {e}")
        return False

def test_backend_synchronization():
    """
    Test that backend calculations are synchronized with AI assessments
    """
    logger.info("🧪 Testing backend synchronization...")
    
    try:
        # Send interaction with AI assessment that should sync backend
        sync_payload = {
            "session_id": TEST_SESSION_ID,
            "user_input": "I've mastered all the learning objectives for this lesson.",
            "context": {
                "current_phase": "teaching",
                "teaching_interactions": 15
            }
        }
        
        # AI provides comprehensive assessment
        ai_state_update = {
            "new_phase": "teaching",
            "objectives_covered": 5,  # AI says all 5 objectives covered
            "objectives_coverage_percentage": 100,
            "content_depth_score": 0.85,
            "teaching_complete": True
        }
        
        sync_payload["ai_state_update"] = ai_state_update
        
        response = requests.post(f"{BACKEND_URL}/enhance_content", json=sync_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = str(result)
            
            # Check if backend is synchronized with AI assessment
            if "🔄 Synchronized backend calculation with AI assessment" in response_text:
                logger.info("✅ Backend calculations are being synchronized with AI assessments")
                return True
            else:
                logger.warning("⚠️ Backend synchronization may not be working")
                return False
        else:
            logger.error(f"Backend synchronization test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error in backend synchronization test: {e}")
        return False

def run_comprehensive_synchronization_test():
    """
    Run all synchronization tests and provide comprehensive report
    """
    logger.info("🚀 Starting AI Instructor Backend Synchronization Test Suite")
    logger.info("=" * 70)
    
    test_results = {}
    
    # Test 1: Objectives Coverage Synchronization
    test_results["objectives_sync"] = test_objectives_coverage_synchronization()
    
    # Test 2: Phase Transition Control
    test_results["phase_control"] = test_phase_transition_control()
    
    # Test 3: Guardrail Violations
    test_results["guardrail_violations"] = test_guardrail_violations()
    
    # Test 4: Backend Synchronization
    test_results["backend_sync"] = test_backend_synchronization()
    
    # Generate report
    logger.info("\n" + "=" * 70)
    logger.info("📊 SYNCHRONIZATION TEST RESULTS")
    logger.info("=" * 70)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    logger.info(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All synchronization tests PASSED!")
        logger.info("AI Instructor and backend are properly synchronized.")
        return True
    else:
        logger.warning("⚠️ Some synchronization tests FAILED!")
        logger.warning("Manual review and fixes may be required.")
        return False

def check_backend_availability():
    """
    Check if the backend server is running and accessible
    """
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Backend server is running and accessible")
            return True
        else:
            logger.error(f"Backend server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Cannot connect to backend server: {e}")
        logger.error("Please ensure the backend server is running on http://localhost:5000")
        return False

if __name__ == "__main__":
    logger.info("🔧 AI Instructor Backend Synchronization Test")
    logger.info("This test verifies the fixes for synchronization issues")
    
    # Check backend availability first
    if not check_backend_availability():
        logger.error("❌ Backend server is not available. Please start the server first.")
        sys.exit(1)
    
    # Run comprehensive test
    success = run_comprehensive_synchronization_test()
    
    if success:
        logger.info("\n✅ Synchronization fixes are working correctly!")
        sys.exit(0)
    else:
        logger.error("\n❌ Synchronization issues detected!")
        sys.exit(1)