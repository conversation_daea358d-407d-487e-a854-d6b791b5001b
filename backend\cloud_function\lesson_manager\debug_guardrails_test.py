#!/usr/bin/env python3
"""
Debug test to understand what's happening with the guardrails validation
"""

import asyncio
import sys
import os
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_guardrails import IntelligentGuardrailsManager, GuardrailViolation, GuardrailSeverity

async def debug_guardrails_validation():
    """Debug the guardrails validation process step by step"""
    print("🔍 DEBUG: Testing Intelligent Guardrails System")
    print("=" * 60)
    
    try:
        # Create guardrails manager
        guardrails = IntelligentGuardrailsManager()
        print("✅ GuardrailsManager created successfully")
        print(f"🔍 Active rules: {list(guardrails.active_rules.keys())}")
        
        # Create test data with correct structure
        lesson_context = {
            'subject': 'Mathematics',
            'grade_level': '8th Grade',
            'topic': 'Linear Equations',
            'learning_objectives': [
                'Understand linear equations',
                'Solve linear equations',
                'Graph linear equations',
                'Apply to real problems',
                'Identify slope and intercept'
            ]
        }
        
        session_data = {
            'lesson_context': lesson_context,
            'current_phase': 'teaching_start',
            'objectives_tracking': {
                'total_objectives': 5,
                'covered_objectives': 2,  # Only 40% covered
                'completion_percentage': 40.0,
                'high_confidence_count': 1
            },
            'interaction_count': 5,
            'elapsed_minutes': 8,
            'content_depth_score': 0.6
        }
        
        # Test AI response that tries to transition to quiz prematurely
        ai_response = "Great! Now let's test your knowledge with a quiz."
        
        print(f"📊 Test scenario: {session_data['objectives_tracking']['completion_percentage']}% objectives covered")
        print(f"🤖 AI Response: {ai_response}")
        print(f"⏰ Elapsed time: {session_data['elapsed_minutes']} minutes")
        
        # Debug: Check if quiz transition is detected
        quiz_keywords = ["let's test", "quiz time", "time for a quiz", "ready for the quiz", "test your knowledge", "with a quiz"]
        detected_keywords = [keyword for keyword in quiz_keywords if keyword in ai_response.lower()]
        print(f"🔍 Quiz keywords detected: {detected_keywords}")
        
        # Validate with guardrails
        print("\n🛡️ Running guardrails validation...")
        is_valid, violations, enhanced_response = await guardrails.validate_ai_response(
            ai_response=ai_response,
            lesson_context=lesson_context,
            session_data=session_data,
            request_id="debug_test_001"
        )
        
        print(f"\n📋 Validation Results:")
        print(f"   Valid: {is_valid}")
        print(f"   Total Violations: {len(violations)}")
        print(f"   Enhanced Response Length: {len(enhanced_response)} chars")
        print(f"   Response Changed: {enhanced_response != ai_response}")
        
        # Analyze violations by severity
        violation_counts = {}
        for violation in violations:
            severity = violation.severity.value
            violation_counts[severity] = violation_counts.get(severity, 0) + 1
            print(f"   🚨 {violation.severity.value.upper()}: {violation.rule_id} - {violation.message}")
            if violation.suggestion:
                print(f"      💡 Suggestion: {violation.suggestion}")
        
        print(f"\n📊 Violation Summary: {violation_counts}")
        
        # Check for blocking violations specifically
        blocking_violations = [v for v in violations if v.severity == GuardrailSeverity.BLOCKING]
        print(f"🛑 Blocking violations: {len(blocking_violations)}")
        
        if len(blocking_violations) > 0:
            print("✅ SUCCESS: Guardrails correctly blocked premature quiz transition")
            print(f"📝 Enhanced response preview: {enhanced_response[:100]}...")
            return True
        else:
            print("❌ FAILURE: No blocking violations found - guardrails may not be working")
            print("🔍 This suggests the objective coverage validation is not working as expected")
            return False
            
    except Exception as e:
        print(f"❌ ERROR during validation: {str(e)}")
        print("🔍 Full traceback:")
        traceback.print_exc()
        return False

async def test_individual_validation_functions():
    """Test individual validation functions to isolate issues"""
    print("\n" + "="*60)
    print("🔬 TESTING INDIVIDUAL VALIDATION FUNCTIONS")
    print("="*60)
    
    try:
        guardrails = IntelligentGuardrailsManager()
        
        lesson_context = {
            'subject': 'Mathematics',
            'grade_level': '8th Grade',
            'topic': 'Linear Equations'
        }
        
        session_data = {
            'objectives_tracking': {
                'total_objectives': 5,
                'covered_objectives': 2,
                'completion_percentage': 40.0,
                'high_confidence_count': 1
            },
            'elapsed_minutes': 8
        }
        
        ai_response = "Great! Now let's test your knowledge with a quiz."
        
        # Test objective coverage validation specifically
        print("🎯 Testing objective coverage validation...")
        coverage_violation = guardrails._validate_objective_coverage(
            ai_response, lesson_context, session_data, "test_coverage"
        )
        
        if coverage_violation:
            print(f"✅ Coverage validation detected violation: {coverage_violation.message}")
            print(f"   Severity: {coverage_violation.severity.value}")
            return True
        else:
            print("❌ Coverage validation did not detect violation")
            return False
            
    except Exception as e:
        print(f"❌ ERROR in individual function test: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting comprehensive guardrails debug test\n")
    
    # Run main validation test
    success1 = asyncio.run(debug_guardrails_validation())
    
    # Run individual function test
    success2 = asyncio.run(test_individual_validation_functions())
    
    overall_success = success1 or success2
    print(f"\n🎯 Overall Test Result: {'PASSED' if overall_success else 'FAILED'}")
    
    if not overall_success:
        print("\n🔍 DEBUGGING SUGGESTIONS:")
        print("1. Check if quiz transition keywords are being detected correctly")
        print("2. Verify objectives_tracking data structure matches expectations")
        print("3. Ensure validation functions are being called properly")
        print("4. Check if GuardrailSeverity.BLOCKING is being set correctly")
    
    sys.exit(0 if overall_success else 1)
