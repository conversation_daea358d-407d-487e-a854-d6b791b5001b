#!/usr/bin/env python3
"""
Fix Teaching Rules Enforcement - Clear Chat Session to Force Re-initialization
This script clears the existing chat session that contains old quiz transition instructions
and forces re-initialization with the updated BASE_INSTRUCTOR_RULES template.
"""

import sys
import os
import logging

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

def fix_teaching_rules_enforcement():
    """Clear the problematic chat session to force re-initialization with updated instructions."""
    
    print("🔧 FIXING TEACHING RULES ENFORCEMENT")
    print("=" * 60)
    
    try:
        # Import the main module to access the chat session manager
        import main
        
        # The problematic session ID from the log
        problematic_session_id = "fallback-ba2e2454-3009-470f-bec8-76994014b39c"
        
        print(f"📋 Target Session ID: {problematic_session_id}")
        print(f"🎯 Issue: Chat session contains old quiz transition instructions")
        print(f"🔧 Solution: Clear session to force re-initialization with updated rules")
        print()
        
        # Clear the problematic session
        print("🧹 Clearing chat session...")
        success = main.chat_session_manager.clear_session(problematic_session_id, "fix_script")
        
        if success:
            print("✅ SUCCESS: Chat session cleared successfully")
            print("📝 Next interaction will re-initialize with updated BASE_INSTRUCTOR_RULES")
            print()
            print("🎯 EXPECTED BEHAVIOR AFTER FIX:")
            print("  ✅ AI will focus on comprehensive teaching")
            print("  ✅ No premature quiz transition suggestions")
            print("  ✅ Backend teaching rules will control quiz transitions")
            print("  ✅ 100% objective coverage required before quiz")
            print("  ❌ No more 'I think you're ready for quiz' messages")
            
        else:
            print("❌ FAILED: Could not clear chat session")
            print("💡 Manual solution: Restart the backend server to clear all sessions")
            
    except ImportError as e:
        print(f"❌ IMPORT ERROR: Could not import main module: {e}")
        print("💡 Make sure you're running this from the correct directory")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("💡 Try restarting the backend server to clear all chat sessions")
    
    print()
    print("=" * 60)
    print("🔧 TEACHING RULES ENFORCEMENT FIX COMPLETE")

if __name__ == "__main__":
    fix_teaching_rules_enforcement()