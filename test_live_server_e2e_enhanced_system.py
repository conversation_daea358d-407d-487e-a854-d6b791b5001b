#!/usr/bin/env python3
"""
Live Server End-to-End Enhanced Teaching Completion System Test
==============================================================

This test validates the enhanced teaching completion system against the live server
to ensure all components are working together perfectly in production.
"""

import requests
import json
import time
import logging
from datetime import datetime
import uuid
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveServerE2ETest:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.session_id = None
        self.student_id = "test_student_enhanced_" + str(int(time.time()))
        self.lesson_id = "P5_MAT_180"  # Primary 5 Mathematics lesson
        self.request_count = 0
        self.test_results = {
            'server_health': False,
            'lesson_initialization': False,
            'teaching_phase_progression': False,
            'enhanced_completion_validation': False,
            'intelligent_guardrails': False,
            'ai_instructor_handoff': False,
            'quiz_transition': False,
            'overall_success': False
        }
        
    def log_test_step(self, step_name, status, details=""):
        """Log test step with consistent formatting"""
        status_icon = "✅" if status else "❌"
        logger.info(f"{status_icon} {step_name}: {details}")
        
    def make_request(self, endpoint, data=None, method="POST"):
        """Make HTTP request with error handling"""
        try:
            self.request_count += 1
            url = f"{self.base_url}/{endpoint}"
            
            if method == "GET":
                response = requests.get(url, timeout=30)
            else:
                headers = {'Content-Type': 'application/json'}
                response = requests.post(url, json=data, headers=headers, timeout=30)
            
            logger.info(f"🌐 REQUEST #{self.request_count}: {method} {endpoint}")
            logger.info(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.error(f"   Error: {response.text}")
                return False, None
                
        except Exception as e:
            logger.error(f"   Exception: {e}")
            return False, None
    
    def test_server_health(self):
        """Test 1: Verify server is running and healthy"""
        logger.info("\n🏥 TEST 1: Server Health Check")
        
        success, response = self.make_request("api/health", method="GET")
        
        if success and response:
            self.log_test_step("Server Health", True, f"Server responding: {response.get('message', 'healthy')}")
            self.test_results['server_health'] = True
            return True
        else:
            self.log_test_step("Server Health", False, "Server not responding")
            return False
    
    def test_lesson_initialization(self):
        """Test 2: Initialize lesson and verify enhanced system is active"""
        logger.info("\n📚 TEST 2: Lesson Initialization with Enhanced System")
        
        init_data = {
            "lesson_id": self.lesson_id,
            "student_input": "I want to start learning mathematics",
            "interaction_type": "lesson_start"
        }
        
        success, response = self.make_request("lesson-content", init_data)
        
        if success and response:
            self.session_id = response.get('session_id')
            lesson_phase = response.get('lesson_phase', 'unknown')
            
            # Check for enhanced system indicators
            enhanced_indicators = [
                'teaching_rules_active',
                'adaptive_requirements',
                'intelligent_guardrails_enabled'
            ]
            
            enhanced_active = any(indicator in str(response) for indicator in enhanced_indicators)
            
            self.log_test_step("Lesson Initialization", True, 
                             f"Session: {self.session_id}, Phase: {lesson_phase}")
            self.log_test_step("Enhanced System Active", enhanced_active, 
                             "Enhanced teaching completion system detected" if enhanced_active else "Standard system")
            
            self.test_results['lesson_initialization'] = True
            return True
        else:
            self.log_test_step("Lesson Initialization", False, "Failed to start lesson")
            return False
    
    def test_teaching_phase_progression(self):
        """Test 3: Progress through teaching phase with enhanced validation"""
        logger.info("\n🎓 TEST 3: Teaching Phase Progression with Enhanced Validation")
        
        if not self.session_id:
            self.log_test_step("Teaching Phase", False, "No session ID available")
            return False
        
        teaching_interactions = 0
        max_teaching_attempts = 15  # Allow for adaptive requirements
        
        for attempt in range(max_teaching_attempts):
            logger.info(f"\n   📝 Teaching Interaction #{attempt + 1}")
            
            # Send teaching request
            teaching_data = {
                "session_id": self.session_id,
                "student_input": f"I want to learn more about this topic. Can you explain it further? (Interaction {attempt + 1})",
                "interaction_type": "teaching_request"
            }
            
            success, response = self.make_request("enhance-content", teaching_data)
            
            if not success:
                self.log_test_step(f"Teaching Interaction {attempt + 1}", False, "Request failed")
                continue
            
            teaching_interactions += 1
            
            # Extract key information
            ai_response = response.get('ai_response', '')
            lesson_phase = response.get('lesson_phase', 'unknown')
            teaching_complete = response.get('teaching_complete', False)
            objectives_covered = response.get('objectives_covered', 0)
            total_objectives = response.get('total_objectives', 1)
            
            # Check for enhanced system indicators
            enhanced_logs = response.get('debug_info', {}).get('logs', [])
            adaptive_requirements_found = any('ADAPTIVE REQUIREMENTS' in str(log) for log in enhanced_logs)
            completion_validation_found = any('TEACHING VALIDATION RESULTS' in str(log) for log in enhanced_logs)
            guardrails_applied = any('GUARDRAILS APPLIED' in str(log) for log in enhanced_logs)
            
            logger.info(f"      Phase: {lesson_phase}")
            logger.info(f"      Teaching Complete: {teaching_complete}")
            logger.info(f"      Objectives: {objectives_covered}/{total_objectives}")
            logger.info(f"      Enhanced Validation: {'✅' if completion_validation_found else '❌'}")
            logger.info(f"      Adaptive Requirements: {'✅' if adaptive_requirements_found else '❌'}")
            logger.info(f"      Guardrails Applied: {'✅' if guardrails_applied else '❌'}")
            
            # Check if teaching is complete
            if teaching_complete or lesson_phase == 'quiz_initiate':
                self.log_test_step("Teaching Phase Progression", True, 
                                 f"Completed after {teaching_interactions} interactions")
                self.test_results['teaching_phase_progression'] = True
                
                # Verify enhanced validation was used
                if completion_validation_found:
                    self.log_test_step("Enhanced Completion Validation", True, 
                                     "Enhanced teaching validation system active")
                    self.test_results['enhanced_completion_validation'] = True
                
                return True
            
            # Check for quiz content being blocked (guardrails working)
            quiz_indicators = ['question 1:', 'quiz', 'test', 'choose the correct answer']
            quiz_content_detected = any(indicator in ai_response.lower() for indicator in quiz_indicators)
            
            if quiz_content_detected and not teaching_complete:
                self.log_test_step("Intelligent Guardrails", True, 
                                 "Quiz content blocked during incomplete teaching")
                self.test_results['intelligent_guardrails'] = True
            
            # Small delay between interactions
            time.sleep(1)
        
        self.log_test_step("Teaching Phase Progression", False, 
                         f"Teaching did not complete after {max_teaching_attempts} attempts")
        return False
    
    def test_ai_instructor_handoff(self):
        """Test 4: Verify AI instructor handoff to quiz system"""
        logger.info("\n🔄 TEST 4: AI Instructor Handoff Verification")
        
        if not self.session_id:
            self.log_test_step("AI Handoff", False, "No session ID available")
            return False
        
        # Request quiz initiation to test handoff
        quiz_request_data = {
            "session_id": self.session_id,
            "student_input": "I'm ready for the quiz now",
            "interaction_type": "quiz_request"
        }
        
        success, response = self.make_request("enhance-content", quiz_request_data)
        
        if success and response:
            lesson_phase = response.get('lesson_phase', 'unknown')
            ai_response = response.get('ai_response', '')
            
            # Check for handoff indicators
            handoff_indicators = [
                'ai_instructor_handoff',
                'teaching_phase_complete',
                'quiz_initiate'
            ]
            
            handoff_detected = any(indicator in str(response) for indicator in handoff_indicators)
            
            # Check debug logs for handoff messages
            debug_logs = response.get('debug_info', {}).get('logs', [])
            handoff_logs = any('HANDOFF: AI Instructor → Existing Quiz System' in str(log) for log in debug_logs)
            
            if handoff_detected or handoff_logs or lesson_phase == 'quiz_initiate':
                self.log_test_step("AI Instructor Handoff", True, 
                                 f"Handoff successful, Phase: {lesson_phase}")
                self.test_results['ai_instructor_handoff'] = True
                return True
            else:
                self.log_test_step("AI Instructor Handoff", False, 
                                 "Handoff indicators not found")
                return False
        else:
            self.log_test_step("AI Instructor Handoff", False, "Request failed")
            return False
    
    def test_quiz_transition(self):
        """Test 5: Verify smooth transition to quiz system"""
        logger.info("\n🧪 TEST 5: Quiz Transition Verification")
        
        if not self.session_id:
            self.log_test_step("Quiz Transition", False, "No session ID available")
            return False
        
        # Request quiz content
        quiz_data = {
            "session_id": self.session_id,
            "action": "get_quiz"
        }
        
        success, response = self.make_request("quiz", quiz_data)
        
        if success and response:
            quiz_questions = response.get('questions', [])
            quiz_phase = response.get('phase', 'unknown')
            
            if quiz_questions and len(quiz_questions) > 0:
                self.log_test_step("Quiz Transition", True, 
                                 f"Quiz loaded with {len(quiz_questions)} questions")
                self.test_results['quiz_transition'] = True
                return True
            else:
                self.log_test_step("Quiz Transition", False, "No quiz questions received")
                return False
        else:
            self.log_test_step("Quiz Transition", False, "Quiz request failed")
            return False
    
    def run_comprehensive_test(self):
        """Run the complete end-to-end test suite"""
        logger.info("🚀 STARTING: Live Server E2E Enhanced Teaching Completion System Test")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        # Run all tests in sequence
        test_methods = [
            self.test_server_health,
            self.test_lesson_initialization,
            self.test_teaching_phase_progression,
            self.test_ai_instructor_handoff,
            self.test_quiz_transition
        ]
        
        for test_method in test_methods:
            try:
                success = test_method()
                if not success and test_method.__name__ in ['test_server_health', 'test_lesson_initialization']:
                    # Critical failures - stop testing
                    logger.error(f"❌ Critical test failure in {test_method.__name__}")
                    break
            except Exception as e:
                logger.error(f"❌ Exception in {test_method.__name__}: {e}")
        
        # Calculate overall success
        critical_tests = ['server_health', 'lesson_initialization', 'teaching_phase_progression']
        critical_passed = all(self.test_results[test] for test in critical_tests)
        
        enhanced_tests = ['enhanced_completion_validation', 'intelligent_guardrails', 'ai_instructor_handoff']
        enhanced_passed = any(self.test_results[test] for test in enhanced_tests)
        
        self.test_results['overall_success'] = critical_passed and enhanced_passed
        
        # Generate comprehensive report
        self.generate_test_report(time.time() - start_time)
        
        return self.test_results['overall_success']
    
    def generate_test_report(self, duration):
        """Generate comprehensive test report"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 LIVE SERVER E2E TEST RESULTS")
        logger.info("=" * 80)
        
        # Test results summary
        for test_name, result in self.test_results.items():
            if test_name == 'overall_success':
                continue
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        # Overall result
        overall_status = "✅ SUCCESS" if self.test_results['overall_success'] else "❌ FAILURE"
        logger.info(f"\n🎯 OVERALL RESULT: {overall_status}")
        
        # Statistics
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results) - 1  # Exclude overall_success
        
        logger.info(f"📈 STATISTICS:")
        logger.info(f"   Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info(f"   Duration: {duration:.2f} seconds")
        logger.info(f"   Requests Made: {self.request_count}")
        logger.info(f"   Session ID: {self.session_id}")
        
        # Enhanced system verification
        enhanced_features = [
            'enhanced_completion_validation',
            'intelligent_guardrails', 
            'ai_instructor_handoff'
        ]
        
        enhanced_working = sum(1 for feature in enhanced_features if self.test_results[feature])
        
        logger.info(f"\n🔧 ENHANCED SYSTEM STATUS:")
        logger.info(f"   Enhanced Features Working: {enhanced_working}/{len(enhanced_features)}")
        
        if enhanced_working >= 2:
            logger.info("   🎉 Enhanced teaching completion system is ACTIVE and WORKING!")
        elif enhanced_working >= 1:
            logger.info("   ⚠️ Enhanced system partially active - some features working")
        else:
            logger.info("   ❌ Enhanced system not detected - may be using standard system")
        
        # Recommendations
        logger.info(f"\n💡 RECOMMENDATIONS:")
        
        if self.test_results['overall_success']:
            logger.info("   ✅ System is working perfectly - ready for production use")
            logger.info("   ✅ Enhanced teaching completion system is active")
            logger.info("   ✅ All critical components functioning correctly")
        else:
            failed_tests = [name for name, result in self.test_results.items() 
                          if not result and name != 'overall_success']
            logger.info(f"   ❌ Failed tests need attention: {', '.join(failed_tests)}")
            
            if not self.test_results['server_health']:
                logger.info("   🔧 Check server status and restart if necessary")
            
            if not self.test_results['enhanced_completion_validation']:
                logger.info("   🔧 Verify enhanced teaching rules are deployed")
            
            if not self.test_results['intelligent_guardrails']:
                logger.info("   🔧 Check intelligent guardrails implementation")
        
        # Save detailed report
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'test_results': self.test_results,
            'statistics': {
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'success_rate': (passed_tests/total_tests)*100,
                'duration_seconds': duration,
                'requests_made': self.request_count
            },
            'session_info': {
                'session_id': self.session_id,
                'student_id': self.student_id,
                'lesson_id': self.lesson_id
            },
            'enhanced_system_status': {
                'features_working': enhanced_working,
                'total_features': len(enhanced_features),
                'is_active': enhanced_working >= 2
            }
        }
        
        report_filename = f"live_server_e2e_test_report_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: {report_filename}")

def main():
    """Main test execution"""
    test_runner = LiveServerE2ETest()
    
    try:
        success = test_runner.run_comprehensive_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)