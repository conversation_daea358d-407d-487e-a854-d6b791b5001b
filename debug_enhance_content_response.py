#!/usr/bin/env python3
"""
Debug Enhanced Content Response
==============================

This script examines the actual response from the /api/enhance-content endpoint
to see what's being returned and whether the enhanced system is active.
"""

import requests
import json
import time

def test_enhance_content():
    """Test the enhance-content endpoint and examine the response"""
    
    url = "http://localhost:5000/api/enhance-content"
    
    # Test data with all required parameters
    test_data = {
        "session_id": f"debug_session_{int(time.time())}",
        "lesson_ref": "P5_MAT_180",
        "subject": "mathematics",
        "student_input": "I want to learn about mathematics. Can you explain fractions?",
        "current_phase": "teaching",
        "teaching_interactions": 2,  # Below minimum to trigger enhanced validation
        "objectives_covered": 1,     # Below 100%
        "total_objectives": 5,
        "content_depth_score": 0.5,  # Below threshold
        "grade": "primary_5",
        "teaching_level": 5
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-Testing-Mode': 'true',  # Bypass auth
        'X-Student-ID': f"debug_student_{int(time.time())}"
    }
    
    print("🔍 DEBUGGING: Enhanced Content Response")
    print("=" * 60)
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print("=" * 60)
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("=" * 60)
        
        if response.status_code == 200:
            response_data = response.json()
            print("RESPONSE DATA:")
            print(json.dumps(response_data, indent=2, default=str))
            
            # Look for enhanced system indicators
            response_str = json.dumps(response_data, default=str).lower()
            
            enhanced_indicators = [
                'adaptive requirements',
                'teaching validation results',
                'primary driver',
                '100% objectives',
                'teaching incomplete',
                'guardrails applied',
                'enhanced',
                'intelligent',
                'completion',
                'validation'
            ]
            
            print("\n" + "=" * 60)
            print("ENHANCED SYSTEM INDICATORS SEARCH:")
            
            found_indicators = []
            for indicator in enhanced_indicators:
                if indicator in response_str:
                    found_indicators.append(indicator)
                    print(f"✅ FOUND: '{indicator}'")
                else:
                    print(f"❌ NOT FOUND: '{indicator}'")
            
            print(f"\nTOTAL INDICATORS FOUND: {len(found_indicators)}")
            
            if found_indicators:
                print("🎉 ENHANCED SYSTEM DETECTED!")
            else:
                print("❌ ENHANCED SYSTEM NOT DETECTED")
                
        else:
            print("ERROR RESPONSE:")
            print(response.text)
            
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    test_enhance_content()