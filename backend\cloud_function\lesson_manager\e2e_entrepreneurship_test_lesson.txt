2025-07-20 13:44:36,677 - INFO - [main.py:7105] - Incoming request: {"request_id": "55bfa088-848a-48be-b12e-afcab8f54516", "timestamp": "2025-07-20T12:44:36.667164+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": []}}
2025-07-20 13:44:36,687 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:44:36,693 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '295', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:44:36,698 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 55bfa088-848a-48be-b12e-afcab8f54516
2025-07-20 13:44:36,700 - INFO - [auth_decorator.py:74] - [55bfa088-848a-48be-b12e-afcab8f54516][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:44:36,701 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:44:36,703 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:44:36,704 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:44:36,704 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:44:36,706 - INFO - [auth_decorator.py:95] - [55bfa088-848a-48be-b12e-afcab8f54516][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:44:36,707 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:44:36,708 - INFO - [auth_decorator.py:121] - [55bfa088-848a-48be-b12e-afcab8f54516][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:44:46,273 - INFO - [main.py:71] - ============================================================
2025-07-20 13:44:46,273 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-20 13:44:46,274 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-20 13:44:46,274 - INFO - [main.py:74] - Platform: win32
2025-07-20 13:44:46,274 - INFO - [main.py:75] - ============================================================
2025-07-20 13:44:46,294 - INFO - [main.py:154] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-20 13:44:46,295 - INFO - [main.py:474] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-20 13:44:46,300 - INFO - [main.py:632] - ================================================================================
2025-07-20 13:44:46,301 - INFO - [main.py:633] - LESSON MANAGER BACKEND STARTING UP
2025-07-20 13:44:46,301 - INFO - [main.py:634] - ================================================================================
2025-07-20 13:44:46,301 - INFO - [main.py:635] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-20 13:44:46,302 - INFO - [main.py:636] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
2025-07-20 13:44:46,302 - INFO - [main.py:637] - Log level: DEBUG
2025-07-20 13:44:46,302 - INFO - [main.py:638] - ================================================================================
2025-07-20 13:44:46,302 - INFO - [main.py:640] - Logging configuration complete with immediate console output
2025-07-20 13:44:46,303 - INFO - [main.py:641] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-20 13:44:46,319 - INFO - [main.py:1338] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-20 13:44:46,319 - INFO - [main.py:1345] - [OK] Enhanced state and auth managers initialized successfully
2025-07-20 13:44:46,322 - INFO - [main.py:1577] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-20 13:44:46,322 - INFO - [main.py:1606] - Phase transition fixes imported successfully
2025-07-20 13:44:46,327 - INFO - [main.py:5029] - Successfully imported utils functions
2025-07-20 13:44:46,327 - INFO - [main.py:5037] - Successfully imported extract_ai_state functions
2025-07-20 13:44:46,330 - INFO - [main.py:5487] - FLASK: Using unified Firebase initialization approach...
2025-07-20 13:44:46,330 - INFO - [unified_firebase_init.py:42] - Firebase already initialized
2025-07-20 13:44:46,330 - INFO - [main.py:5495] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-20 13:44:46,331 - INFO - [main.py:5585] - Gemini API will be initialized on first use (lazy loading).
2025-07-20 13:44:46,347 - INFO - [main.py:19253] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-20 13:44:46,349 - INFO - [main.py:19296] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-20 13:44:46,357 - INFO - [main.py:2065] - Successfully imported timetable_generator functions
2025-07-20 13:44:46,367 - INFO - [main.py:25424] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-20 13:44:46,519 - INFO - [main.py:25427] - Google Cloud Storage client initialized successfully.
2025-07-20 13:44:47,862 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:44:47,862 - INFO - [auth_decorator.py:164] - [55bfa088-848a-48be-b12e-afcab8f54516][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:44:47,874 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:44:47,881 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:44:47,882 - WARNING - [main.py:7283] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:44:47,882 - WARNING - [main.py:7284] - [55bfa088-848a-48be-b12e-afcab8f54516] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:44:47,882 - INFO - [main.py:7289] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Start diagnostic assessment","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","subject":"Entrepreneurship","session_id":"fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d","chat_history":[]}...
2025-07-20 13:44:47,882 - INFO - [main.py:7291] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': []}
2025-07-20 13:44:47,884 - INFO - [main.py:7293] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:47,884 - INFO - [main.py:7294] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:44:47,884 - INFO - [main.py:7295] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:44:47,885 - DEBUG - [main.py:7331] - [55bfa088-848a-48be-b12e-afcab8f54516] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:44:47,885 - INFO - [main.py:7332] - [55bfa088-848a-48be-b12e-afcab8f54516] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:44:47,886 - INFO - [main.py:7372] - [55bfa088-848a-48be-b12e-afcab8f54516] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:44:48,394 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-20 13:44:48,954 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-20 13:44:49,755 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:44:49,756 - INFO - [main.py:7389] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:44:49,756 - INFO - [main.py:7390] - [55bfa088-848a-48be-b12e-afcab8f54516] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:44:49,759 - INFO - [main.py:2455] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 PARAMETER MAPPING:
2025-07-20 13:44:49,759 - INFO - [main.py:2456] - [55bfa088-848a-48be-b12e-afcab8f54516] 📥 Original: country=Nigeria, curriculum=National Curriculum, subject=Entrepreneurship, level=5
2025-07-20 13:44:49,760 - INFO - [main.py:2457] - [55bfa088-848a-48be-b12e-afcab8f54516] 📤 Mapped: country=Nigeria, curriculum=National Curriculum, subject=Entrepreneurship Education, level=P5
2025-07-20 13:44:49,762 - INFO - [main.py:2462] - [55bfa088-848a-48be-b12e-afcab8f54516] 🎓 Grade mapping: Primary 5 → Primary 5
2025-07-20 13:44:49,763 - INFO - [main.py:2464] - [55bfa088-848a-48be-b12e-afcab8f54516] fetch_lesson_data: Fetching lesson with parameters:
2025-07-20 13:44:49,763 - INFO - [main.py:2465] -   • Country (original): Nigeria
2025-07-20 13:44:49,764 - INFO - [main.py:2466] -   • Country (for Firestore): Nigeria
2025-07-20 13:44:49,764 - INFO - [main.py:2467] -   • Curriculum (original): National Curriculum
2025-07-20 13:44:49,764 - INFO - [main.py:2468] -   • Curriculum (for Firestore): National Curriculum
2025-07-20 13:44:49,764 - INFO - [main.py:2469] -   • Grade (original): Primary 5
2025-07-20 13:44:49,764 - INFO - [main.py:2470] -   • Grade (normalized for Firestore): Primary 5
2025-07-20 13:44:49,765 - INFO - [main.py:2471] -   • Level (original): 5
2025-07-20 13:44:49,765 - INFO - [main.py:2472] -   • Level (for Firestore): P5
2025-07-20 13:44:49,765 - INFO - [main.py:2473] -   • Subject (original): Entrepreneurship
2025-07-20 13:44:49,765 - INFO - [main.py:2474] -   • Subject (for Firestore): Entrepreneurship Education
2025-07-20 13:44:49,766 - INFO - [main.py:2475] -   • Lesson ID: P5-ENT-001
2025-07-20 13:44:49,766 - INFO - [main.py:2484] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔧 PATH CONSTRUCTION:
2025-07-20 13:44:49,767 - INFO - [main.py:2485] - [55bfa088-848a-48be-b12e-afcab8f54516]    └─ countries/Nigeria
2025-07-20 13:44:49,767 - INFO - [main.py:2486] - [55bfa088-848a-48be-b12e-afcab8f54516]       └─ curriculums/National Curriculum
2025-07-20 13:44:49,767 - INFO - [main.py:2487] - [55bfa088-848a-48be-b12e-afcab8f54516]          └─ grades/Primary 5
2025-07-20 13:44:49,767 - INFO - [main.py:2488] - [55bfa088-848a-48be-b12e-afcab8f54516]             └─ levels/P5
2025-07-20 13:44:49,768 - INFO - [main.py:2489] - [55bfa088-848a-48be-b12e-afcab8f54516]                └─ subjects/Entrepreneurship Education
2025-07-20 13:44:49,768 - INFO - [main.py:2490] - [55bfa088-848a-48be-b12e-afcab8f54516]                   └─ lessonRef/P5-ENT-001
2025-07-20 13:44:49,768 - INFO - [main.py:2491] - [55bfa088-848a-48be-b12e-afcab8f54516] 📍 FULL PATH: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Entrepreneurship Education/lessonRef/P5-ENT-001
2025-07-20 13:44:49,768 - INFO - [main.py:2492] - [55bfa088-848a-48be-b12e-afcab8f54516] 🆔 LESSON REF: P5-ENT-001
2025-07-20 13:44:49,769 - INFO - [main.py:2505] - [55bfa088-848a-48be-b12e-afcab8f54516] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Entrepreneurship Education/lessonRef/P5-ENT-001
2025-07-20 13:44:50,056 - WARNING - [main.py:2511] - [55bfa088-848a-48be-b12e-afcab8f54516] Primary path failed, trying alternative: root_lessonRef_collection (lessonRef/P5-ENT-001)
2025-07-20 13:44:50,543 - WARNING - [main.py:2511] - [55bfa088-848a-48be-b12e-afcab8f54516] Primary path failed, trying alternative: legacy_lessons_collection (lessons/P5-ENT-001)
2025-07-20 13:44:50,930 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,931 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,931 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,931 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,932 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,932 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,932 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,932 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,933 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,933 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,934 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,934 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,935 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,936 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,936 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,937 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,938 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,938 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,938 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,939 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,939 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,940 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,940 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,940 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,941 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,941 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,941 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:50,941 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,942 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,942 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,942 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,942 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:50,943 - INFO - [main.py:2574] - [55bfa088-848a-48be-b12e-afcab8f54516] Successfully retrieved and sanitized document with keys: ['additionalNotes', 'interactiveElements', 'metadata', 'lessonRef', 'lessonTitle', 'instructionalSteps', 'learningObjectives', 'extensionActivities', 'adaptiveStrategies', 'conclusion', 'quizzes', 'introduction']
2025-07-20 13:44:50,945 - INFO - [main.py:16039] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔧 Enhancing lesson data compatibility
2025-07-20 13:44:50,946 - INFO - [main.py:16065] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Inferred subject: General Studies
2025-07-20 13:44:50,949 - INFO - [main.py:16084] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Set grade: Primary 5
2025-07-20 13:44:50,949 - INFO - [main.py:16094] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Set topic: Introduction to Marketing
2025-07-20 13:44:50,949 - INFO - [main.py:16152] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Lesson data enhancement complete
2025-07-20 13:44:50,950 - INFO - [main.py:2663] - [55bfa088-848a-48be-b12e-afcab8f54516] 🗺️ Starting robust field mapping for lesson P5-ENT-001
2025-07-20 13:44:50,953 - INFO - [main.py:2691] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Core fields mapped: subject=General Studies, topic=Introduction to Marketing, grade=Primary 5
2025-07-20 13:44:50,954 - INFO - [main.py:2861] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 Extracting key concepts from lesson data
2025-07-20 13:44:50,954 - INFO - [main.py:2868] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Found 0 existing key concepts
2025-07-20 13:44:50,955 - INFO - [main.py:2884] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Extracted concepts from 2 learning objectives
2025-07-20 13:44:50,956 - INFO - [main.py:2910] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Extracted concepts from instructional steps
2025-07-20 13:44:50,956 - INFO - [main.py:2928] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Added topic and subject-based concepts
2025-07-20 13:44:50,957 - INFO - [main.py:2966] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Key concepts extraction complete: 10 unique concepts
2025-07-20 13:44:50,957 - INFO - [main.py:3140] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Universal content extraction: 548 characters from 3 steps
2025-07-20 13:44:50,958 - INFO - [main.py:3248] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Universal conversion: 3 steps → 3 sections
2025-07-20 13:44:50,958 - INFO - [main.py:2803] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Field mapping completed successfully:
2025-07-20 13:44:50,958 - INFO - [main.py:2804] -   - Subject: General Studies
2025-07-20 13:44:50,959 - INFO - [main.py:2805] -   - Topic: Introduction to Marketing
2025-07-20 13:44:50,959 - INFO - [main.py:2806] -   - Grade: Primary 5
2025-07-20 13:44:50,959 - INFO - [main.py:2807] -   - Key Concepts: 10 extracted
2025-07-20 13:44:50,959 - INFO - [main.py:2808] -   - Instructional Steps: 3
2025-07-20 13:44:50,960 - INFO - [main.py:2809] -   - Total Fields: 33
2025-07-20 13:44:50,960 - INFO - [main.py:3295] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Universal content structure recognized: instructionalSteps (3 steps)
2025-07-20 13:44:50,961 - INFO - [main.py:3316] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ All required fields present after universal mapping
2025-07-20 13:44:50,961 - INFO - [main.py:3325] - [55bfa088-848a-48be-b12e-afcab8f54516] 📊 Lesson validation complete: 33 fields available for processing
2025-07-20 13:44:50,961 - INFO - [main.py:2589] - [55bfa088-848a-48be-b12e-afcab8f54516] Successfully mapped lesson fields for AI inference
2025-07-20 13:44:50,961 - DEBUG - [main.py:680] - Cached result for fetch_lesson_data
2025-07-20 13:44:51,369 - INFO - [main.py:7483] - [55bfa088-848a-48be-b12e-afcab8f54516] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:44:51,370 - INFO - [main.py:7608] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:44:51,370 - INFO - [main.py:7619] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:44:51,371 - INFO - [main.py:7658] - [55bfa088-848a-48be-b12e-afcab8f54516] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:44:51,372 - INFO - [main.py:4239] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:44:51,394 - INFO - [main.py:5576] - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
2025-07-20 13:44:51,759 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:44:51,759 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:44:51,759 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:44:51,759 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:44:51,760 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:44:51,760 - INFO - [main.py:4305] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:44:51,761 - INFO - [main.py:4374] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:44:51,762 - DEBUG - [main.py:4388] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:44:51,762 - DEBUG - [main.py:4391] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:44:51,763 - DEBUG - [main.py:4392] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:44:51,763 - DEBUG - [main.py:4393] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:44:51,764 - INFO - [main.py:4397] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:44:52,777 - INFO - [main.py:4407] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Gemini API call completed in 1.01s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:44:52,778 - DEBUG - [main.py:4429] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:44:52,778 - INFO - [main.py:4434] - [55bfa088-848a-48be-b12e-afcab8f54516] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:44:52,778 - INFO - [main.py:7692] - [55bfa088-848a-48be-b12e-afcab8f54516] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:44:52,780 - INFO - [main.py:4483] - [55bfa088-848a-48be-b12e-afcab8f54516] CACHE MISS or fetch: Getting GS levels for subject 'entrepreneurship', module 'marketing_and_customer_engagement'.
2025-07-20 13:44:53,153 - INFO - [main.py:4506] - [55bfa088-848a-48be-b12e-afcab8f54516] Fetched metadata for module: 'Marketing & Customer Engagement'
2025-07-20 13:44:53,576 - INFO - [main.py:4538] - [55bfa088-848a-48be-b12e-afcab8f54516] Successfully fetched 10 levels for module 'marketing_and_customer_engagement'.
2025-07-20 13:44:53,579 - INFO - [main.py:7729] - [55bfa088-848a-48be-b12e-afcab8f54516] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:44:53,982 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:44:54,355 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:44:54,356 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:54,357 - DEBUG - [main.py:7784] - 🔍   - Document Exists: False
2025-07-20 13:44:54,358 - DEBUG - [main.py:7785] - 🔍   - Current Phase: Not found
2025-07-20 13:44:54,358 - DEBUG - [main.py:7786] - 🔍   - Probing Level: Not found
2025-07-20 13:44:54,359 - DEBUG - [main.py:7787] - 🔍   - Question Index: Not found
2025-07-20 13:44:54,359 - WARNING - [main.py:7793] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 SESSION STATE DEBUG:
2025-07-20 13:44:54,360 - WARNING - [main.py:7794] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   - Session exists: False
2025-07-20 13:44:54,361 - WARNING - [main.py:7795] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   - Current phase: None
2025-07-20 13:44:54,362 - WARNING - [main.py:7796] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   - State data keys: []
2025-07-20 13:44:54,363 - DEBUG - [main.py:7814] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:44:54,364 - DEBUG - [main.py:7815] - [55bfa088-848a-48be-b12e-afcab8f54516]   Retrieved Phase: 'None'
2025-07-20 13:44:54,365 - DEBUG - [main.py:7816] - [55bfa088-848a-48be-b12e-afcab8f54516]   Diagnostic Completed: False
2025-07-20 13:44:54,365 - DEBUG - [main.py:7817] - [55bfa088-848a-48be-b12e-afcab8f54516]   Assigned Level: None
2025-07-20 13:44:54,365 - WARNING - [main.py:7818] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔒 STATE PROTECTION: phase='None', diagnostic_done=False, level=None
2025-07-20 13:44:54,366 - INFO - [main.py:7850] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:44:54,366 - INFO - [main.py:7851] - [55bfa088-848a-48be-b12e-afcab8f54516] State protection not triggered
2025-07-20 13:44:54,367 - INFO - [main.py:7899] - [55bfa088-848a-48be-b12e-afcab8f54516]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:44:54,367 - INFO - [main.py:7900] - [55bfa088-848a-48be-b12e-afcab8f54516]   assigned_level_for_teaching (session): None
2025-07-20 13:44:54,367 - INFO - [main.py:7901] - [55bfa088-848a-48be-b12e-afcab8f54516]   latest_assessed_level (profile): None
2025-07-20 13:44:54,367 - INFO - [main.py:7902] - [55bfa088-848a-48be-b12e-afcab8f54516]   teaching_level_for_returning_student: None
2025-07-20 13:44:54,368 - INFO - [main.py:7903] - [55bfa088-848a-48be-b12e-afcab8f54516]   has_completed_diagnostic_before: False
2025-07-20 13:44:54,368 - INFO - [main.py:7904] - [55bfa088-848a-48be-b12e-afcab8f54516]   is_first_encounter_for_module: True
2025-07-20 13:44:54,369 - WARNING - [main.py:7909] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:44:54,369 - INFO - [main.py:7915] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 PHASE INVESTIGATION:
2025-07-20 13:44:54,369 - INFO - [main.py:7916] - [55bfa088-848a-48be-b12e-afcab8f54516]   Retrieved from Firestore: 'None'
2025-07-20 13:44:54,369 - INFO - [main.py:7917] - [55bfa088-848a-48be-b12e-afcab8f54516]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:44:54,370 - INFO - [main.py:7918] - [55bfa088-848a-48be-b12e-afcab8f54516]   Is first encounter: True
2025-07-20 13:44:54,370 - INFO - [main.py:7919] - [55bfa088-848a-48be-b12e-afcab8f54516]   Diagnostic completed: False
2025-07-20 13:44:54,370 - INFO - [main.py:7932] - [55bfa088-848a-48be-b12e-afcab8f54516]   No stored phase found, starting with: 'smart_diagnostic_start'
2025-07-20 13:44:54,370 - INFO - [main.py:7939] - [55bfa088-848a-48be-b12e-afcab8f54516] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:44:54,371 - INFO - [main.py:7941] - [55bfa088-848a-48be-b12e-afcab8f54516] Final phase for AI logic: smart_diagnostic_start
2025-07-20 13:44:54,371 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:44:54,371 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:44:54,372 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:44:54,373 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:44:54,373 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:44:54,374 - WARNING - [main.py:3933] - [55bfa088-848a-48be-b12e-afcab8f54516] State key 'current_probing_level_number' had non-numeric value 'None', using default 2.
2025-07-20 13:44:54,375 - INFO - [main.py:7961] - [55bfa088-848a-48be-b12e-afcab8f54516] NEW SESSION: Forcing question_index to 0 (was: N/A)
2025-07-20 13:44:54,376 - INFO - [main.py:5858] - [55bfa088-848a-48be-b12e-afcab8f54516] Diagnostic context validation passed
2025-07-20 13:44:54,377 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_start'
2025-07-20 13:44:54,378 - WARNING - [main.py:8101] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_start' for first encounter
2025-07-20 13:44:54,378 - INFO - [main.py:8124] - [55bfa088-848a-48be-b12e-afcab8f54516] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_start
2025-07-20 13:44:54,379 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_start'
2025-07-20 13:44:54,379 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_start'
2025-07-20 13:44:54,380 - INFO - [main.py:8136] - [55bfa088-848a-48be-b12e-afcab8f54516] Robust context prepared successfully. Phase: smart_diagnostic_start
2025-07-20 13:44:54,380 - DEBUG - [main.py:8137] - [55bfa088-848a-48be-b12e-afcab8f54516] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:44:54,381 - WARNING - [main.py:8360] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖 AI PROMPT GENERATION:
2025-07-20 13:44:54,382 - WARNING - [main.py:8361] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - Current phase: smart_diagnostic_start
2025-07-20 13:44:54,382 - WARNING - [main.py:8362] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - Student query: Start diagnostic assessment...
2025-07-20 13:44:54,382 - WARNING - [main.py:8363] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:44:54,383 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:44:54,383 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_start
2025-07-20 13:44:54,383 - DEBUG - [main.py:8368] - 🤖   Query: Start diagnostic assessment...
2025-07-20 13:44:54,384 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:44:54,390 - INFO - [main.py:10008] - [55bfa088-848a-48be-b12e-afcab8f54516] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:54,390 - INFO - [main.py:10012] - [55bfa088-848a-48be-b12e-afcab8f54516] 🚀 First interaction - initializing chat session
2025-07-20 13:44:54,391 - INFO - [main.py:5635] - [55bfa088-848a-48be-b12e-afcab8f54516] 🚀 Initializing chat session for lesson: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:54,392 - INFO - [main.py:5719] - [55bfa088-848a-48be-b12e-afcab8f54516] Creating system prompt for Andrea, Grade Primary 5, Topic: Introduction to Marketing
2025-07-20 13:44:54,393 - INFO - [main.py:5650] - [55bfa088-848a-48be-b12e-afcab8f54516] 💰 Making SINGLE API call to initialize lesson session
2025-07-20 13:44:54,901 - INFO - [main.py:5658] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ Lesson session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d initialized successfully
2025-07-20 13:44:54,902 - INFO - [main.py:5659] - [55bfa088-848a-48be-b12e-afcab8f54516] 💰 COST OPTIMIZATION: All subsequent interactions will use NO additional API calls
2025-07-20 13:44:54,905 - WARNING - [main.py:8391] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:44:54,906 - WARNING - [main.py:8392] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - Content length: 298 chars
2025-07-20 13:44:54,906 - WARNING - [main.py:8393] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q1'}
2025-07-20 13:44:54,907 - WARNING - [main.py:8394] - [55bfa088-848a-48be-b12e-afcab8f54516] 🤖   - Raw state block: None...
2025-07-20 13:44:54,908 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:44:54,908 - DEBUG - [main.py:8790] - 🤖   Content: Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few qu...
2025-07-20 13:44:54,909 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'smart_diagnostic_q1'}
2025-07-20 13:44:54,909 - INFO - [main.py:8817] - [55bfa088-848a-48be-b12e-afcab8f54516] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:44:54,910 - INFO - [main.py:8818] - [55bfa088-848a-48be-b12e-afcab8f54516] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q1, Session=smart_diagnostic_start, Final=smart_diagnostic_q1
2025-07-20 13:44:55,182 - WARNING - [main.py:8907] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_start', new_phase='smart_diagnostic_q1'
2025-07-20 13:44:55,182 - INFO - [main.py:6099] - [55bfa088-848a-48be-b12e-afcab8f54516] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,183 - WARNING - [main.py:8916] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:44:55,183 - WARNING - [main.py:8935] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔄 PHASE TRANSITION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,183 - INFO - [main.py:8946] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:44:55,184 - INFO - [phase_transition_integrity.py:328] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,188 - INFO - [phase_transition_integrity.py:291] - [55bfa088-848a-48be-b12e-afcab8f54516] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_start
2025-07-20 13:44:55,190 - INFO - [phase_transition_integrity.py:153] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,192 - INFO - [phase_transition_integrity.py:233] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ TRANSITION VALIDATED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,192 - INFO - [phase_transition_integrity.py:358] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ TRANSITION APPLIED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,193 - DEBUG - [phase_transition_integrity.py:841] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:44:55,193 - DEBUG - [phase_transition_integrity.py:874] - [55bfa088-848a-48be-b12e-afcab8f54516] 📝 TRANSITION RECORDED: smart_diagnostic_start → smart_diagnostic_q1 (valid)
2025-07-20 13:44:55,193 - INFO - [phase_transition_integrity.py:404] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q1
2025-07-20 13:44:55,193 - INFO - [main.py:8993] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ TRANSITION VALIDATED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,194 - WARNING - [main.py:8998] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:44:55,194 - WARNING - [main.py:8999] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   1. Input phase: 'smart_diagnostic_start'
2025-07-20 13:44:55,194 - WARNING - [main.py:9000] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:44:55,195 - WARNING - [main.py:9001] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   3. Proposed phase: 'smart_diagnostic_q1'
2025-07-20 13:44:55,195 - WARNING - [main.py:9002] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:44:55,195 - WARNING - [main.py:9003] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍   5. Final phase to save: 'smart_diagnostic_q1'
2025-07-20 13:44:55,195 - WARNING - [main.py:9006] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾 FINAL STATE APPLICATION:
2025-07-20 13:44:55,196 - WARNING - [main.py:9007] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾   - Current phase input: 'smart_diagnostic_start'
2025-07-20 13:44:55,196 - WARNING - [main.py:9008] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾   - Validated state updates: 24 fields
2025-07-20 13:44:55,196 - WARNING - [main.py:9009] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾   - Final phase to save: 'smart_diagnostic_q1'
2025-07-20 13:44:55,197 - WARNING - [main.py:9010] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾   - Phase change: True
2025-07-20 13:44:55,198 - WARNING - [main.py:9011] - [55bfa088-848a-48be-b12e-afcab8f54516] 💾   - Integrity applied: True
2025-07-20 13:44:55,198 - INFO - [main.py:6131] - [55bfa088-848a-48be-b12e-afcab8f54516] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:44:55,198 - INFO - [main.py:6132] - [55bfa088-848a-48be-b12e-afcab8f54516]   Phase transition: smart_diagnostic_start -> smart_diagnostic_q1
2025-07-20 13:44:55,199 - INFO - [main.py:6133] - [55bfa088-848a-48be-b12e-afcab8f54516]   Current level: 2
2025-07-20 13:44:55,199 - INFO - [main.py:6134] - [55bfa088-848a-48be-b12e-afcab8f54516]   Question index: 0
2025-07-20 13:44:55,199 - INFO - [main.py:6135] - [55bfa088-848a-48be-b12e-afcab8f54516]   First encounter: True
2025-07-20 13:44:55,199 - INFO - [main.py:6140] - [55bfa088-848a-48be-b12e-afcab8f54516]   Answers collected: 0
2025-07-20 13:44:55,199 - INFO - [main.py:6141] - [55bfa088-848a-48be-b12e-afcab8f54516]   Levels failed: 0
2025-07-20 13:44:55,199 - INFO - [main.py:6099] - [55bfa088-848a-48be-b12e-afcab8f54516] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:55,200 - INFO - [main.py:6145] - [55bfa088-848a-48be-b12e-afcab8f54516]   State update valid: True
2025-07-20 13:44:55,200 - INFO - [main.py:6152] - [55bfa088-848a-48be-b12e-afcab8f54516]   Diagnostic complete: False
2025-07-20 13:44:55,200 - WARNING - [main.py:9029] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:44:55,200 - INFO - [main.py:9037] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:44:55,201 - INFO - [main.py:9038] - [55bfa088-848a-48be-b12e-afcab8f54516] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:44:55,713 - WARNING - [main.py:9083] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:44:55,713 - WARNING - [main.py:9084] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Phase: smart_diagnostic_q1
2025-07-20 13:44:55,714 - WARNING - [main.py:9085] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Probing Level: 2
2025-07-20 13:44:55,714 - WARNING - [main.py:9086] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Question Index: 0
2025-07-20 13:44:55,714 - WARNING - [main.py:9087] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Diagnostic Complete: False
2025-07-20 13:44:55,715 - WARNING - [main.py:9094] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Quiz Questions Saved: 0
2025-07-20 13:44:55,715 - WARNING - [main.py:9095] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Quiz Answers Saved: 0
2025-07-20 13:44:55,716 - WARNING - [main.py:9096] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅   - Quiz Started: False
2025-07-20 13:44:55,716 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q1
2025-07-20 13:44:55,717 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:44:56,516 - INFO - [main.py:9190] - [55bfa088-848a-48be-b12e-afcab8f54516] Created new session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:56,518 - INFO - [main.py:16959] - [55bfa088-848a-48be-b12e-afcab8f54516] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:44:56,519 - DEBUG - [main.py:4726] - [55bfa088-848a-48be-b12e-afcab8f54516] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:44:56,519 - DEBUG - [main.py:9298] - [55bfa088-848a-48be-b12e-afcab8f54516] No final assessment data found in AI response
2025-07-20 13:44:56,521 - DEBUG - [main.py:9321] - [55bfa088-848a-48be-b12e-afcab8f54516] No lesson completion detected (Phase: smart_diagnostic_q1, Complete: False)
2025-07-20 13:44:56,522 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:44:56,523 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_start
2025-07-20 13:44:56,524 - DEBUG - [main.py:9347] - 🔒   Final Phase: smart_diagnostic_q1
2025-07-20 13:44:56,525 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: False
2025-07-20 13:44:56,525 - DEBUG - [main.py:9349] - 🔒   Assigned Level: None
2025-07-20 13:44:56,526 - INFO - [main.py:9422] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:44:56,527 - INFO - [main.py:9456] - [55bfa088-848a-48be-b12e-afcab8f54516] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:44:56,527 - INFO - [main.py:9464] - [55bfa088-848a-48be-b12e-afcab8f54516] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:44:56,527 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:44:56,528 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:44:56,529 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_start → smart_diagnostic_q1
2025-07-20 13:44:56,530 - DEBUG - [main.py:9504] - 🎯   Content: Hello Andrea! Welcome to your lesson on Introducti...
2025-07-20 13:44:56,530 - DEBUG - [main.py:9505] - 🎯   Request ID: 55bfa088-848a-48be-b12e-afcab8f54516
2025-07-20 13:44:56,531 - INFO - [main.py:9511] - [55bfa088-848a-48be-b12e-afcab8f54516] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:44:56,532 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,532 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,532 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,532 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:56,533 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:56,533 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:56,533 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:56,534 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,534 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:44:56,534 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,534 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,535 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,535 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:44:56,550 - WARNING - [main.py:703] - High response time detected: 8.69s for enhance_content_api
2025-07-20 13:45:49,541 - INFO - [main.py:7105] - Incoming request: {"request_id": "3d647bd0-3b34-4065-ac83-5fd140606c0d", "timestamp": "2025-07-20T12:45:49.535856+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-20T12:44:56.588Z"}]}}
2025-07-20 13:45:49,545 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:45:49,548 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '816', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:45:49,549 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 3d647bd0-3b34-4065-ac83-5fd140606c0d
2025-07-20 13:45:49,549 - INFO - [auth_decorator.py:74] - [3d647bd0-3b34-4065-ac83-5fd140606c0d][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:45:49,549 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:45:49,550 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:45:49,550 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:45:49,551 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:45:49,552 - INFO - [auth_decorator.py:95] - [3d647bd0-3b34-4065-ac83-5fd140606c0d][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:45:49,552 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:45:49,553 - INFO - [auth_decorator.py:121] - [3d647bd0-3b34-4065-ac83-5fd140606c0d][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:45:50,090 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:45:50,090 - INFO - [auth_decorator.py:164] - [3d647bd0-3b34-4065-ac83-5fd140606c0d][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:45:50,092 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:45:50,100 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:45:50,101 - WARNING - [main.py:7283] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:45:50,101 - WARNING - [main.py:7284] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:45:50,101 - INFO - [main.py:7289] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","subject":"Entrepreneurship","session_id":"fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d","chat_history":[{"role":"assistant","content":"Hello Andrea! Welcome to yo...
2025-07-20 13:45:50,102 - INFO - [main.py:7291] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-20T12:44:56.588Z'}]}
2025-07-20 13:45:50,102 - INFO - [main.py:7293] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:45:50,103 - INFO - [main.py:7294] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:45:50,103 - INFO - [main.py:7295] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:45:50,103 - DEBUG - [main.py:7331] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:45:50,103 - INFO - [main.py:7332] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:45:50,104 - INFO - [main.py:7372] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:45:50,409 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:45:50,409 - INFO - [main.py:7389] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:45:50,410 - INFO - [main.py:7390] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:45:50,410 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:45:50,881 - INFO - [main.py:7420] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:45:50,881 - INFO - [main.py:7483] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:45:50,881 - INFO - [main.py:7608] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:45:50,882 - INFO - [main.py:7619] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:45:50,882 - INFO - [main.py:7658] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:45:50,882 - INFO - [main.py:4239] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:45:51,228 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:45:51,229 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:45:51,230 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:45:51,231 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:45:51,231 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:45:51,231 - INFO - [main.py:4305] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:45:51,232 - INFO - [main.py:4374] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:45:51,232 - DEBUG - [main.py:4388] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:45:51,232 - DEBUG - [main.py:4391] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:45:51,233 - DEBUG - [main.py:4392] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:45:51,233 - DEBUG - [main.py:4393] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:45:51,234 - INFO - [main.py:4397] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:45:51,675 - INFO - [main.py:4407] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Gemini API call completed in 0.44s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:45:51,675 - DEBUG - [main.py:4429] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:45:51,675 - INFO - [main.py:4434] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:45:51,676 - INFO - [main.py:7692] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:45:51,676 - INFO - [main.py:7729] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:45:51,983 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:45:52,476 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:45:52,477 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:45:52,477 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:45:52,478 - DEBUG - [main.py:7785] - 🔍   - Current Phase: smart_diagnostic_q1
2025-07-20 13:45:52,478 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 2
2025-07-20 13:45:52,480 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:45:52,481 - WARNING - [main.py:7793] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 SESSION STATE DEBUG:
2025-07-20 13:45:52,481 - WARNING - [main.py:7794] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   - Session exists: True
2025-07-20 13:45:52,482 - WARNING - [main.py:7795] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   - Current phase: smart_diagnostic_q1
2025-07-20 13:45:52,483 - WARNING - [main.py:7796] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_phase', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_probing_level_number', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:45:52,483 - DEBUG - [main.py:7814] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:45:52,484 - DEBUG - [main.py:7815] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Retrieved Phase: 'smart_diagnostic_q1'
2025-07-20 13:45:52,485 - DEBUG - [main.py:7816] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Diagnostic Completed: False
2025-07-20 13:45:52,485 - DEBUG - [main.py:7817] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Assigned Level: None
2025-07-20 13:45:52,486 - WARNING - [main.py:7818] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔒 STATE PROTECTION: phase='smart_diagnostic_q1', diagnostic_done=False, level=None
2025-07-20 13:45:52,487 - INFO - [main.py:7850] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:45:52,488 - INFO - [main.py:7851] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] State protection not triggered
2025-07-20 13:45:52,488 - INFO - [main.py:7899] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:45:52,488 - INFO - [main.py:7900] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   assigned_level_for_teaching (session): None
2025-07-20 13:45:52,489 - INFO - [main.py:7901] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   latest_assessed_level (profile): None
2025-07-20 13:45:52,489 - INFO - [main.py:7902] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   teaching_level_for_returning_student: None
2025-07-20 13:45:52,489 - INFO - [main.py:7903] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   has_completed_diagnostic_before: False
2025-07-20 13:45:52,489 - INFO - [main.py:7904] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   is_first_encounter_for_module: True
2025-07-20 13:45:52,490 - WARNING - [main.py:7909] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:45:52,490 - INFO - [main.py:7915] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 PHASE INVESTIGATION:
2025-07-20 13:45:52,490 - INFO - [main.py:7916] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Retrieved from Firestore: 'smart_diagnostic_q1'
2025-07-20 13:45:52,490 - INFO - [main.py:7917] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:45:52,491 - INFO - [main.py:7918] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Is first encounter: True
2025-07-20 13:45:52,491 - INFO - [main.py:7919] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Diagnostic completed: False
2025-07-20 13:45:52,491 - INFO - [main.py:7925] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ Using stored phase from Firestore: 'smart_diagnostic_q1'
2025-07-20 13:45:52,491 - INFO - [main.py:7939] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:45:52,492 - INFO - [main.py:7941] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Final phase for AI logic: smart_diagnostic_q1
2025-07-20 13:45:52,492 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:45:52,492 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:45:52,492 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:45:52,493 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:45:52,493 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:45:52,493 - INFO - [main.py:7961] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:45:52,493 - INFO - [main.py:5858] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Diagnostic context validation passed
2025-07-20 13:45:52,494 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_q1'
2025-07-20 13:45:52,494 - WARNING - [main.py:8101] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q1' for first encounter
2025-07-20 13:45:52,494 - INFO - [main.py:8124] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q1
2025-07-20 13:45:52,495 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q1'
2025-07-20 13:45:52,495 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q1'
2025-07-20 13:45:52,495 - INFO - [main.py:8136] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Robust context prepared successfully. Phase: smart_diagnostic_q1
2025-07-20 13:45:52,497 - DEBUG - [main.py:8137] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:45:52,498 - WARNING - [main.py:8360] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖 AI PROMPT GENERATION:
2025-07-20 13:45:52,498 - WARNING - [main.py:8361] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - Current phase: smart_diagnostic_q1
2025-07-20 13:45:52,499 - WARNING - [main.py:8362] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - Student query: Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's...
2025-07-20 13:45:52,499 - WARNING - [main.py:8363] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:45:52,499 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:45:52,500 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_q1
2025-07-20 13:45:52,501 - DEBUG - [main.py:8368] - 🤖   Query: Hello!  Hmm, marketing is like telling people abou...
2025-07-20 13:45:52,502 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:45:52,503 - INFO - [main.py:10008] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:45:52,503 - INFO - [main.py:10041] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📤 Sending message to existing session: Hello!  Hmm, marketing is like telling people abou...
2025-07-20 13:45:52,503 - INFO - [main.py:5679] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:45:53,234 - INFO - [main.py:5684] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:45:53,234 - INFO - [main.py:10048] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📥 Received response from session: That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about wha...
2025-07-20 13:45:53,235 - INFO - [main.py:10073] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q1
2025-07-20 13:45:53,235 - INFO - [main.py:10131] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📝 Diagnostic in progress, checking for question progression
2025-07-20 13:45:53,235 - INFO - [main.py:10162] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,236 - WARNING - [main.py:8391] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:45:53,236 - WARNING - [main.py:8392] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - Content length: 332 chars
2025-07-20 13:45:53,236 - WARNING - [main.py:8393] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q2'}
2025-07-20 13:45:53,237 - WARNING - [main.py:8394] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🤖   - Raw state block: None...
2025-07-20 13:45:53,237 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:45:53,237 - DEBUG - [main.py:8790] - 🤖   Content: That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about wha...
2025-07-20 13:45:53,237 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'smart_diagnostic_q2'}
2025-07-20 13:45:53,238 - INFO - [main.py:8817] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:45:53,238 - INFO - [main.py:8818] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q2, Session=smart_diagnostic_q1, Final=smart_diagnostic_q2
2025-07-20 13:45:53,507 - WARNING - [main.py:8907] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q1', new_phase='smart_diagnostic_q2'
2025-07-20 13:45:53,507 - INFO - [main.py:6099] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,508 - WARNING - [main.py:8916] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:45:53,508 - WARNING - [main.py:8935] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔄 PHASE TRANSITION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,508 - INFO - [main.py:8946] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:45:53,508 - INFO - [phase_transition_integrity.py:328] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,509 - INFO - [phase_transition_integrity.py:291] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_q1
2025-07-20 13:45:53,509 - INFO - [phase_transition_integrity.py:153] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,509 - INFO - [phase_transition_integrity.py:233] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ TRANSITION VALIDATED: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,509 - INFO - [phase_transition_integrity.py:358] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ TRANSITION APPLIED: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,510 - DEBUG - [phase_transition_integrity.py:841] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:45:53,510 - DEBUG - [phase_transition_integrity.py:874] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 📝 TRANSITION RECORDED: smart_diagnostic_q1 → smart_diagnostic_q2 (valid)
2025-07-20 13:45:53,510 - INFO - [phase_transition_integrity.py:404] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q2
2025-07-20 13:45:53,511 - INFO - [main.py:8993] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ TRANSITION VALIDATED: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,511 - WARNING - [main.py:8998] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:45:53,512 - WARNING - [main.py:8999] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   1. Input phase: 'smart_diagnostic_q1'
2025-07-20 13:45:53,514 - WARNING - [main.py:9000] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:45:53,514 - WARNING - [main.py:9001] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   3. Proposed phase: 'smart_diagnostic_q2'
2025-07-20 13:45:53,516 - WARNING - [main.py:9002] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:45:53,517 - WARNING - [main.py:9003] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍   5. Final phase to save: 'smart_diagnostic_q2'
2025-07-20 13:45:53,518 - WARNING - [main.py:9006] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾 FINAL STATE APPLICATION:
2025-07-20 13:45:53,518 - WARNING - [main.py:9007] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾   - Current phase input: 'smart_diagnostic_q1'
2025-07-20 13:45:53,518 - WARNING - [main.py:9008] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾   - Validated state updates: 24 fields
2025-07-20 13:45:53,519 - WARNING - [main.py:9009] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾   - Final phase to save: 'smart_diagnostic_q2'
2025-07-20 13:45:53,519 - WARNING - [main.py:9010] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾   - Phase change: True
2025-07-20 13:45:53,519 - WARNING - [main.py:9011] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 💾   - Integrity applied: True
2025-07-20 13:45:53,520 - INFO - [main.py:6131] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:45:53,520 - INFO - [main.py:6132] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Phase transition: smart_diagnostic_q1 -> smart_diagnostic_q2
2025-07-20 13:45:53,521 - INFO - [main.py:6133] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Current level: 2
2025-07-20 13:45:53,521 - INFO - [main.py:6134] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Question index: 0
2025-07-20 13:45:53,521 - INFO - [main.py:6135] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   First encounter: True
2025-07-20 13:45:53,521 - INFO - [main.py:6140] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Answers collected: 0
2025-07-20 13:45:53,521 - INFO - [main.py:6141] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Levels failed: 0
2025-07-20 13:45:53,522 - INFO - [main.py:6099] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:53,522 - INFO - [main.py:6145] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   State update valid: True
2025-07-20 13:45:53,522 - INFO - [main.py:6152] - [3d647bd0-3b34-4065-ac83-5fd140606c0d]   Diagnostic complete: False
2025-07-20 13:45:53,522 - WARNING - [main.py:9029] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:45:53,522 - INFO - [main.py:9037] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:45:53,523 - INFO - [main.py:9038] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:45:54,023 - WARNING - [main.py:9083] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:45:54,024 - WARNING - [main.py:9084] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Phase: smart_diagnostic_q2
2025-07-20 13:45:54,024 - WARNING - [main.py:9085] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Probing Level: 2
2025-07-20 13:45:54,025 - WARNING - [main.py:9086] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Question Index: 0
2025-07-20 13:45:54,025 - WARNING - [main.py:9087] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Diagnostic Complete: False
2025-07-20 13:45:54,026 - WARNING - [main.py:9094] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Quiz Questions Saved: 0
2025-07-20 13:45:54,026 - WARNING - [main.py:9095] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Quiz Answers Saved: 0
2025-07-20 13:45:54,027 - WARNING - [main.py:9096] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅   - Quiz Started: False
2025-07-20 13:45:54,027 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q2
2025-07-20 13:45:54,028 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:45:54,900 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q2
2025-07-20 13:45:54,900 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:54,901 - INFO - [main.py:9211] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:45:54,901 - INFO - [main.py:16959] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:45:54,901 - DEBUG - [main.py:4726] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:45:54,902 - DEBUG - [main.py:9298] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] No final assessment data found in AI response
2025-07-20 13:45:54,902 - DEBUG - [main.py:9321] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] No lesson completion detected (Phase: smart_diagnostic_q2, Complete: False)
2025-07-20 13:45:54,902 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:45:54,902 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_q1
2025-07-20 13:45:54,903 - DEBUG - [main.py:9347] - 🔒   Final Phase: smart_diagnostic_q2
2025-07-20 13:45:54,903 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: False
2025-07-20 13:45:54,903 - DEBUG - [main.py:9349] - 🔒   Assigned Level: None
2025-07-20 13:45:54,904 - INFO - [main.py:9422] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:45:54,905 - INFO - [main.py:9456] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:45:54,905 - INFO - [main.py:9464] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:45:54,906 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:45:54,906 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:45:54,907 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-20 13:45:54,908 - DEBUG - [main.py:9504] - 🎯   Content: That's a fantastic start, Andrea! You're absolutel...
2025-07-20 13:45:54,909 - DEBUG - [main.py:9505] - 🎯   Request ID: 3d647bd0-3b34-4065-ac83-5fd140606c0d
2025-07-20 13:45:54,909 - INFO - [main.py:9511] - [3d647bd0-3b34-4065-ac83-5fd140606c0d] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:45:54,909 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,910 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,910 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,910 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:45:54,910 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:45:54,911 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:45:54,911 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:45:54,911 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,911 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:45:54,912 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,912 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,913 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,914 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:45:54,915 - WARNING - [main.py:703] - High response time detected: 4.82s for enhance_content_api
2025-07-20 13:46:16,868 - INFO - [main.py:7105] - Incoming request: {"request_id": "d62b5c1a-b49b-4f68-b9b1-f8ca938dc824", "timestamp": "2025-07-20T12:46:16.867208+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-20T12:44:56.588Z"}, {"role": "user", "content": "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", "timestamp": "2025-07-20T12:45:49.393Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You're absolutely right \u2013 it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", "timestamp": "2025-07-20T12:45:54.932Z"}]}}
2025-07-20 13:46:16,868 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:46:16,869 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '1516', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:46:16,869 - INFO - [auth_decorator.py:70] - 🔒 Request ID: d62b5c1a-b49b-4f68-b9b1-f8ca938dc824
2025-07-20 13:46:16,869 - INFO - [auth_decorator.py:74] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:95] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:46:16,870 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:46:16,871 - INFO - [auth_decorator.py:121] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:46:17,146 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:46:17,147 - INFO - [auth_decorator.py:164] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:46:17,148 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:46:17,149 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:46:17,150 - WARNING - [main.py:7283] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:46:17,150 - WARNING - [main.py:7284] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:46:17,150 - INFO - [main.py:7289] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","subject":"Entrepreneurship","session_id":"fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d","chat_history":[{"role":"assi...
2025-07-20 13:46:17,151 - INFO - [main.py:7291] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-20T12:44:56.588Z'}, {'role': 'user', 'content': "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", 'timestamp': '2025-07-20T12:45:49.393Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", 'timestamp': '2025-07-20T12:45:54.932Z'}]}
2025-07-20 13:46:17,152 - INFO - [main.py:7293] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:17,152 - INFO - [main.py:7294] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:46:17,153 - INFO - [main.py:7295] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:46:17,153 - DEBUG - [main.py:7331] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:46:17,153 - INFO - [main.py:7332] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:46:17,153 - INFO - [main.py:7372] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:46:17,453 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:46:17,453 - INFO - [main.py:7389] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:46:17,453 - INFO - [main.py:7390] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:46:17,453 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:46:17,932 - INFO - [main.py:7420] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:46:17,933 - INFO - [main.py:7483] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:46:17,933 - INFO - [main.py:7608] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:46:17,934 - INFO - [main.py:7619] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:46:17,934 - INFO - [main.py:7658] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:46:17,934 - INFO - [main.py:4239] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:46:18,240 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:46:18,240 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:46:18,240 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:46:18,241 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:46:18,241 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:46:18,241 - INFO - [main.py:4305] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:46:18,241 - INFO - [main.py:4374] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:46:18,242 - DEBUG - [main.py:4388] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:46:18,242 - DEBUG - [main.py:4391] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:46:18,242 - DEBUG - [main.py:4392] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:46:18,243 - DEBUG - [main.py:4393] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:46:18,243 - INFO - [main.py:4397] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:46:18,699 - INFO - [main.py:4407] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Gemini API call completed in 0.46s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:46:18,700 - DEBUG - [main.py:4429] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:46:18,700 - INFO - [main.py:4434] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:46:18,700 - INFO - [main.py:7692] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:46:18,701 - INFO - [main.py:7729] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:46:18,986 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:46:19,487 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:46:19,487 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:19,488 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:46:19,488 - DEBUG - [main.py:7785] - 🔍   - Current Phase: smart_diagnostic_q2
2025-07-20 13:46:19,488 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 2
2025-07-20 13:46:19,488 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:46:19,489 - WARNING - [main.py:7793] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 SESSION STATE DEBUG:
2025-07-20 13:46:19,489 - WARNING - [main.py:7794] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   - Session exists: True
2025-07-20 13:46:19,489 - WARNING - [main.py:7795] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   - Current phase: smart_diagnostic_q2
2025-07-20 13:46:19,489 - WARNING - [main.py:7796] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_phase', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:46:19,490 - DEBUG - [main.py:7814] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:46:19,490 - DEBUG - [main.py:7815] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Retrieved Phase: 'smart_diagnostic_q2'
2025-07-20 13:46:19,490 - DEBUG - [main.py:7816] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Diagnostic Completed: False
2025-07-20 13:46:19,490 - DEBUG - [main.py:7817] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Assigned Level: None
2025-07-20 13:46:19,490 - WARNING - [main.py:7818] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔒 STATE PROTECTION: phase='smart_diagnostic_q2', diagnostic_done=False, level=None
2025-07-20 13:46:19,491 - INFO - [main.py:7850] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:46:19,491 - INFO - [main.py:7851] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] State protection not triggered
2025-07-20 13:46:19,492 - INFO - [main.py:7899] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:46:19,492 - INFO - [main.py:7900] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   assigned_level_for_teaching (session): None
2025-07-20 13:46:19,493 - INFO - [main.py:7901] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   latest_assessed_level (profile): None
2025-07-20 13:46:19,493 - INFO - [main.py:7902] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   teaching_level_for_returning_student: None
2025-07-20 13:46:19,494 - INFO - [main.py:7903] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   has_completed_diagnostic_before: False
2025-07-20 13:46:19,494 - INFO - [main.py:7904] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   is_first_encounter_for_module: True
2025-07-20 13:46:19,495 - WARNING - [main.py:7909] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:46:19,495 - INFO - [main.py:7915] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 PHASE INVESTIGATION:
2025-07-20 13:46:19,496 - INFO - [main.py:7916] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Retrieved from Firestore: 'smart_diagnostic_q2'
2025-07-20 13:46:19,496 - INFO - [main.py:7917] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:46:19,497 - INFO - [main.py:7918] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Is first encounter: True
2025-07-20 13:46:19,497 - INFO - [main.py:7919] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Diagnostic completed: False
2025-07-20 13:46:19,498 - INFO - [main.py:7925] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ Using stored phase from Firestore: 'smart_diagnostic_q2'
2025-07-20 13:46:19,499 - INFO - [main.py:7939] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:46:19,499 - INFO - [main.py:7941] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Final phase for AI logic: smart_diagnostic_q2
2025-07-20 13:46:19,499 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:46:19,500 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:46:19,500 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:46:19,500 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:46:19,501 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:46:19,501 - INFO - [main.py:7961] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:46:19,501 - INFO - [main.py:5858] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Diagnostic context validation passed
2025-07-20 13:46:19,501 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_q2'
2025-07-20 13:46:19,501 - WARNING - [main.py:8101] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q2' for first encounter
2025-07-20 13:46:19,502 - INFO - [main.py:8124] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q2
2025-07-20 13:46:19,502 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q2'
2025-07-20 13:46:19,502 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q2'
2025-07-20 13:46:19,502 - INFO - [main.py:8136] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Robust context prepared successfully. Phase: smart_diagnostic_q2
2025-07-20 13:46:19,502 - DEBUG - [main.py:8137] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:46:19,503 - WARNING - [main.py:8360] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖 AI PROMPT GENERATION:
2025-07-20 13:46:19,503 - WARNING - [main.py:8361] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - Current phase: smart_diagnostic_q2
2025-07-20 13:46:19,503 - WARNING - [main.py:8362] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - Student query: Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a ...
2025-07-20 13:46:19,503 - WARNING - [main.py:8363] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:46:19,504 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:46:19,504 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_q2
2025-07-20 13:46:19,504 - DEBUG - [main.py:8368] - 🤖   Query: Oh yes! I see it all the time.  Like **TV commerci...
2025-07-20 13:46:19,504 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:46:19,505 - INFO - [main.py:10008] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:19,505 - INFO - [main.py:10041] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📤 Sending message to existing session: Oh yes! I see it all the time.  Like **TV commerci...
2025-07-20 13:46:19,505 - INFO - [main.py:5679] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:46:20,219 - INFO - [main.py:5684] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:46:20,219 - INFO - [main.py:10048] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📥 Received response from session: That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They...
2025-07-20 13:46:20,220 - INFO - [main.py:10073] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q2
2025-07-20 13:46:20,220 - INFO - [main.py:10131] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📝 Diagnostic in progress, checking for question progression
2025-07-20 13:46:20,220 - INFO - [main.py:10162] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,221 - WARNING - [main.py:8391] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:46:20,221 - WARNING - [main.py:8392] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - Content length: 383 chars
2025-07-20 13:46:20,221 - WARNING - [main.py:8393] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q3'}
2025-07-20 13:46:20,222 - WARNING - [main.py:8394] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🤖   - Raw state block: None...
2025-07-20 13:46:20,222 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:46:20,223 - DEBUG - [main.py:8790] - 🤖   Content: That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They...
2025-07-20 13:46:20,223 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'smart_diagnostic_q3'}
2025-07-20 13:46:20,224 - INFO - [main.py:8817] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:46:20,225 - INFO - [main.py:8818] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q3, Session=smart_diagnostic_q2, Final=smart_diagnostic_q3
2025-07-20 13:46:20,511 - WARNING - [main.py:8907] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q2', new_phase='smart_diagnostic_q3'
2025-07-20 13:46:20,511 - INFO - [main.py:6099] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,512 - WARNING - [main.py:8916] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:46:20,514 - WARNING - [main.py:8935] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔄 PHASE TRANSITION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,515 - INFO - [main.py:8946] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:46:20,515 - INFO - [phase_transition_integrity.py:328] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,516 - INFO - [phase_transition_integrity.py:291] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_q2
2025-07-20 13:46:20,516 - INFO - [phase_transition_integrity.py:153] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,517 - INFO - [phase_transition_integrity.py:233] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ TRANSITION VALIDATED: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,518 - INFO - [phase_transition_integrity.py:358] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ TRANSITION APPLIED: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,518 - DEBUG - [phase_transition_integrity.py:841] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:46:20,519 - DEBUG - [phase_transition_integrity.py:874] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 📝 TRANSITION RECORDED: smart_diagnostic_q2 → smart_diagnostic_q3 (valid)
2025-07-20 13:46:20,519 - INFO - [phase_transition_integrity.py:404] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q3
2025-07-20 13:46:20,520 - INFO - [main.py:8993] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ TRANSITION VALIDATED: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,520 - WARNING - [main.py:8998] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:46:20,521 - WARNING - [main.py:8999] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   1. Input phase: 'smart_diagnostic_q2'
2025-07-20 13:46:20,521 - WARNING - [main.py:9000] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:46:20,522 - WARNING - [main.py:9001] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   3. Proposed phase: 'smart_diagnostic_q3'
2025-07-20 13:46:20,522 - WARNING - [main.py:9002] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:46:20,523 - WARNING - [main.py:9003] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍   5. Final phase to save: 'smart_diagnostic_q3'
2025-07-20 13:46:20,523 - WARNING - [main.py:9006] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾 FINAL STATE APPLICATION:
2025-07-20 13:46:20,524 - WARNING - [main.py:9007] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾   - Current phase input: 'smart_diagnostic_q2'
2025-07-20 13:46:20,524 - WARNING - [main.py:9008] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾   - Validated state updates: 24 fields
2025-07-20 13:46:20,524 - WARNING - [main.py:9009] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾   - Final phase to save: 'smart_diagnostic_q3'
2025-07-20 13:46:20,525 - WARNING - [main.py:9010] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾   - Phase change: True
2025-07-20 13:46:20,525 - WARNING - [main.py:9011] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 💾   - Integrity applied: True
2025-07-20 13:46:20,525 - INFO - [main.py:6131] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:46:20,526 - INFO - [main.py:6132] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Phase transition: smart_diagnostic_q2 -> smart_diagnostic_q3
2025-07-20 13:46:20,526 - INFO - [main.py:6133] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Current level: 2
2025-07-20 13:46:20,526 - INFO - [main.py:6134] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Question index: 0
2025-07-20 13:46:20,526 - INFO - [main.py:6135] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   First encounter: True
2025-07-20 13:46:20,527 - INFO - [main.py:6140] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Answers collected: 0
2025-07-20 13:46:20,527 - INFO - [main.py:6141] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Levels failed: 0
2025-07-20 13:46:20,527 - INFO - [main.py:6099] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:20,528 - INFO - [main.py:6145] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   State update valid: True
2025-07-20 13:46:20,528 - INFO - [main.py:6152] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824]   Diagnostic complete: False
2025-07-20 13:46:20,528 - WARNING - [main.py:9029] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:46:20,529 - INFO - [main.py:9037] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:46:20,529 - INFO - [main.py:9038] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:46:21,041 - WARNING - [main.py:9083] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:46:21,042 - WARNING - [main.py:9084] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Phase: smart_diagnostic_q3
2025-07-20 13:46:21,042 - WARNING - [main.py:9085] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Probing Level: 2
2025-07-20 13:46:21,042 - WARNING - [main.py:9086] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Question Index: 0
2025-07-20 13:46:21,043 - WARNING - [main.py:9087] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Diagnostic Complete: False
2025-07-20 13:46:21,043 - WARNING - [main.py:9094] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Quiz Questions Saved: 0
2025-07-20 13:46:21,043 - WARNING - [main.py:9095] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Quiz Answers Saved: 0
2025-07-20 13:46:21,044 - WARNING - [main.py:9096] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅   - Quiz Started: False
2025-07-20 13:46:21,044 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q3
2025-07-20 13:46:21,044 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:46:21,856 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q3
2025-07-20 13:46:21,858 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:21,858 - INFO - [main.py:9211] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:21,859 - INFO - [main.py:16959] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:46:21,860 - DEBUG - [main.py:4726] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:46:21,860 - DEBUG - [main.py:9298] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] No final assessment data found in AI response
2025-07-20 13:46:21,861 - DEBUG - [main.py:9321] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] No lesson completion detected (Phase: smart_diagnostic_q3, Complete: False)
2025-07-20 13:46:21,861 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:46:21,862 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_q2
2025-07-20 13:46:21,863 - DEBUG - [main.py:9347] - 🔒   Final Phase: smart_diagnostic_q3
2025-07-20 13:46:21,864 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: False
2025-07-20 13:46:21,865 - DEBUG - [main.py:9349] - 🔒   Assigned Level: None
2025-07-20 13:46:21,866 - INFO - [main.py:9422] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:46:21,867 - INFO - [main.py:9456] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:46:21,868 - INFO - [main.py:9464] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:46:21,868 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:46:21,869 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:21,869 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-20 13:46:21,870 - DEBUG - [main.py:9504] - 🎯   Content: That's a perfect example, Andrea! TV commercials a...
2025-07-20 13:46:21,871 - DEBUG - [main.py:9505] - 🎯   Request ID: d62b5c1a-b49b-4f68-b9b1-f8ca938dc824
2025-07-20 13:46:21,872 - INFO - [main.py:9511] - [d62b5c1a-b49b-4f68-b9b1-f8ca938dc824] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:46:21,872 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,873 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,873 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,873 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:21,873 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:21,874 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:21,874 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:21,875 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,875 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:21,875 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,876 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,876 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,876 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:21,877 - WARNING - [main.py:703] - High response time detected: 4.73s for enhance_content_api
2025-07-20 13:46:45,071 - INFO - [main.py:7105] - Incoming request: {"request_id": "65d56f05-729e-413d-bfdd-6c086f7d8ecf", "timestamp": "2025-07-20T12:46:45.071004+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-20T12:44:56.588Z"}, {"role": "user", "content": "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", "timestamp": "2025-07-20T12:45:49.393Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You're absolutely right \u2013 it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", "timestamp": "2025-07-20T12:45:54.932Z"}, {"role": "user", "content": "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", "timestamp": "2025-07-20T12:46:16.772Z"}, {"role": "assistant", "content": "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", "timestamp": "2025-07-20T12:46:21.900Z"}]}}
2025-07-20 13:46:45,072 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:46:45,072 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '2663', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:46:45,073 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 65d56f05-729e-413d-bfdd-6c086f7d8ecf
2025-07-20 13:46:45,073 - INFO - [auth_decorator.py:74] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:46:45,073 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:46:45,073 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:46:45,074 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:46:45,074 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:46:45,074 - INFO - [auth_decorator.py:95] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:46:45,075 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:46:45,075 - INFO - [auth_decorator.py:121] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:46:45,382 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:46:45,383 - INFO - [auth_decorator.py:164] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:46:45,383 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:46:45,384 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:46:45,385 - WARNING - [main.py:7283] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:46:45,385 - WARNING - [main.py:7284] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:46:45,385 - INFO - [main.py:7289] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snac...
2025-07-20 13:46:45,385 - INFO - [main.py:7291] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': 'Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-20T12:44:56.588Z'}, {'role': 'user', 'content': "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", 'timestamp': '2025-07-20T12:45:49.393Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", 'timestamp': '2025-07-20T12:45:54.932Z'}, {'role': 'user', 'content': "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", 'timestamp': '2025-07-20T12:46:16.772Z'}, {'role': 'assistant', 'content': "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", 'timestamp': '2025-07-20T12:46:21.900Z'}]}
2025-07-20 13:46:45,386 - INFO - [main.py:7293] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:45,386 - INFO - [main.py:7294] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:46:45,387 - INFO - [main.py:7295] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:46:45,387 - DEBUG - [main.py:7331] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:46:45,388 - INFO - [main.py:7332] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:46:45,388 - INFO - [main.py:7372] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:46:45,681 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:46:45,682 - INFO - [main.py:7389] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:46:45,682 - INFO - [main.py:7390] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:46:45,682 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:46:46,199 - INFO - [main.py:7420] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:46:46,200 - INFO - [main.py:7483] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:46:46,200 - INFO - [main.py:7608] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:46:46,200 - INFO - [main.py:7619] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:46:46,201 - INFO - [main.py:7658] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:46:46,201 - INFO - [main.py:4239] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:46:46,521 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:46:46,521 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:46:46,521 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:46:46,521 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:46:46,522 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:46:46,522 - INFO - [main.py:4305] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:46:46,522 - INFO - [main.py:4374] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:46:46,522 - DEBUG - [main.py:4388] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:46:46,522 - DEBUG - [main.py:4391] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:46:46,523 - DEBUG - [main.py:4392] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:46:46,523 - DEBUG - [main.py:4393] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:46:46,523 - INFO - [main.py:4397] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:46:46,998 - INFO - [main.py:4407] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Gemini API call completed in 0.48s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:46:46,999 - DEBUG - [main.py:4429] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:46:46,999 - INFO - [main.py:4434] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:46:46,999 - INFO - [main.py:7692] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:46:47,000 - INFO - [main.py:7729] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:46:47,274 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:46:47,759 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:46:47,760 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:47,760 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:46:47,760 - DEBUG - [main.py:7785] - 🔍   - Current Phase: smart_diagnostic_q3
2025-07-20 13:46:47,761 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 2
2025-07-20 13:46:47,761 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:46:47,761 - WARNING - [main.py:7793] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 SESSION STATE DEBUG:
2025-07-20 13:46:47,761 - WARNING - [main.py:7794] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   - Session exists: True
2025-07-20 13:46:47,761 - WARNING - [main.py:7795] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   - Current phase: smart_diagnostic_q3
2025-07-20 13:46:47,762 - WARNING - [main.py:7796] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_phase', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:46:47,762 - DEBUG - [main.py:7814] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:46:47,762 - DEBUG - [main.py:7815] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Retrieved Phase: 'smart_diagnostic_q3'
2025-07-20 13:46:47,762 - DEBUG - [main.py:7816] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Diagnostic Completed: False
2025-07-20 13:46:47,762 - DEBUG - [main.py:7817] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Assigned Level: None
2025-07-20 13:46:47,763 - WARNING - [main.py:7818] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔒 STATE PROTECTION: phase='smart_diagnostic_q3', diagnostic_done=False, level=None
2025-07-20 13:46:47,764 - INFO - [main.py:7850] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:46:47,765 - INFO - [main.py:7851] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] State protection not triggered
2025-07-20 13:46:47,766 - INFO - [main.py:7899] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:46:47,766 - INFO - [main.py:7900] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   assigned_level_for_teaching (session): None
2025-07-20 13:46:47,767 - INFO - [main.py:7901] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   latest_assessed_level (profile): None
2025-07-20 13:46:47,767 - INFO - [main.py:7902] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   teaching_level_for_returning_student: None
2025-07-20 13:46:47,768 - INFO - [main.py:7903] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   has_completed_diagnostic_before: False
2025-07-20 13:46:47,769 - INFO - [main.py:7904] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   is_first_encounter_for_module: True
2025-07-20 13:46:47,769 - WARNING - [main.py:7909] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:46:47,769 - INFO - [main.py:7915] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 PHASE INVESTIGATION:
2025-07-20 13:46:47,769 - INFO - [main.py:7916] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Retrieved from Firestore: 'smart_diagnostic_q3'
2025-07-20 13:46:47,770 - INFO - [main.py:7917] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:46:47,770 - INFO - [main.py:7918] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Is first encounter: True
2025-07-20 13:46:47,770 - INFO - [main.py:7919] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Diagnostic completed: False
2025-07-20 13:46:47,771 - INFO - [main.py:7925] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ Using stored phase from Firestore: 'smart_diagnostic_q3'
2025-07-20 13:46:47,771 - INFO - [main.py:7939] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:46:47,772 - INFO - [main.py:7941] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Final phase for AI logic: smart_diagnostic_q3
2025-07-20 13:46:47,772 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:46:47,772 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:46:47,772 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:46:47,773 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:46:47,773 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:46:47,773 - INFO - [main.py:7961] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:46:47,773 - INFO - [main.py:5858] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Diagnostic context validation passed
2025-07-20 13:46:47,774 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_q3'
2025-07-20 13:46:47,774 - WARNING - [main.py:8101] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q3' for first encounter
2025-07-20 13:46:47,774 - INFO - [main.py:8124] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q3
2025-07-20 13:46:47,774 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q3'
2025-07-20 13:46:47,775 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q3'
2025-07-20 13:46:47,775 - INFO - [main.py:8136] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Robust context prepared successfully. Phase: smart_diagnostic_q3
2025-07-20 13:46:47,775 - DEBUG - [main.py:8137] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:46:47,776 - WARNING - [main.py:8360] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖 AI PROMPT GENERATION:
2025-07-20 13:46:47,776 - WARNING - [main.py:8361] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - Current phase: smart_diagnostic_q3
2025-07-20 13:46:47,776 - WARNING - [main.py:8362] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - Student query: Businesses want to tell *lots* of people about what they offer because:  1.  **More people means mor...
2025-07-20 13:46:47,777 - WARNING - [main.py:8363] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:46:47,777 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:46:47,777 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_q3
2025-07-20 13:46:47,778 - DEBUG - [main.py:8368] - 🤖   Query: Businesses want to tell *lots* of people about wha...
2025-07-20 13:46:47,778 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:46:47,779 - INFO - [main.py:10008] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:47,779 - INFO - [main.py:10041] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📤 Sending message to existing session: Businesses want to tell *lots* of people about wha...
2025-07-20 13:46:47,779 - INFO - [main.py:5679] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:46:48,628 - INFO - [main.py:5684] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:46:48,628 - INFO - [main.py:10048] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📥 Received response from session: You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential ...
2025-07-20 13:46:48,628 - INFO - [main.py:10073] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q3
2025-07-20 13:46:48,629 - INFO - [main.py:10131] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📝 Diagnostic in progress, checking for question progression
2025-07-20 13:46:48,629 - INFO - [main.py:10162] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,629 - WARNING - [main.py:8391] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:46:48,630 - WARNING - [main.py:8392] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - Content length: 513 chars
2025-07-20 13:46:48,631 - WARNING - [main.py:8393] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q4'}
2025-07-20 13:46:48,632 - WARNING - [main.py:8394] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🤖   - Raw state block: None...
2025-07-20 13:46:48,633 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:46:48,633 - DEBUG - [main.py:8790] - 🤖   Content: You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential ...
2025-07-20 13:46:48,634 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'smart_diagnostic_q4'}
2025-07-20 13:46:48,634 - INFO - [main.py:8817] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:46:48,634 - INFO - [main.py:8818] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q4, Session=smart_diagnostic_q3, Final=smart_diagnostic_q4
2025-07-20 13:46:48,938 - WARNING - [main.py:8907] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q3', new_phase='smart_diagnostic_q4'
2025-07-20 13:46:48,938 - INFO - [main.py:6099] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,939 - WARNING - [main.py:8916] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:46:48,939 - WARNING - [main.py:8935] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔄 PHASE TRANSITION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,939 - INFO - [main.py:8946] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:46:48,939 - INFO - [phase_transition_integrity.py:328] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,939 - INFO - [phase_transition_integrity.py:291] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_q3
2025-07-20 13:46:48,940 - INFO - [phase_transition_integrity.py:153] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,940 - INFO - [phase_transition_integrity.py:233] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ TRANSITION VALIDATED: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,940 - INFO - [phase_transition_integrity.py:358] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ TRANSITION APPLIED: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,940 - DEBUG - [phase_transition_integrity.py:841] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:46:48,940 - DEBUG - [phase_transition_integrity.py:874] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 📝 TRANSITION RECORDED: smart_diagnostic_q3 → smart_diagnostic_q4 (valid)
2025-07-20 13:46:48,941 - INFO - [phase_transition_integrity.py:404] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q4
2025-07-20 13:46:48,941 - INFO - [main.py:8993] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ TRANSITION VALIDATED: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,941 - WARNING - [main.py:8998] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:46:48,942 - WARNING - [main.py:8999] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   1. Input phase: 'smart_diagnostic_q3'
2025-07-20 13:46:48,942 - WARNING - [main.py:9000] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:46:48,943 - WARNING - [main.py:9001] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   3. Proposed phase: 'smart_diagnostic_q4'
2025-07-20 13:46:48,943 - WARNING - [main.py:9002] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:46:48,943 - WARNING - [main.py:9003] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍   5. Final phase to save: 'smart_diagnostic_q4'
2025-07-20 13:46:48,944 - WARNING - [main.py:9006] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾 FINAL STATE APPLICATION:
2025-07-20 13:46:48,945 - WARNING - [main.py:9007] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾   - Current phase input: 'smart_diagnostic_q3'
2025-07-20 13:46:48,946 - WARNING - [main.py:9008] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾   - Validated state updates: 24 fields
2025-07-20 13:46:48,946 - WARNING - [main.py:9009] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾   - Final phase to save: 'smart_diagnostic_q4'
2025-07-20 13:46:48,948 - WARNING - [main.py:9010] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾   - Phase change: True
2025-07-20 13:46:48,948 - WARNING - [main.py:9011] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 💾   - Integrity applied: True
2025-07-20 13:46:48,948 - INFO - [main.py:6131] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:46:48,949 - INFO - [main.py:6132] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Phase transition: smart_diagnostic_q3 -> smart_diagnostic_q4
2025-07-20 13:46:48,949 - INFO - [main.py:6133] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Current level: 2
2025-07-20 13:46:48,949 - INFO - [main.py:6134] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Question index: 0
2025-07-20 13:46:48,949 - INFO - [main.py:6135] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   First encounter: True
2025-07-20 13:46:48,949 - INFO - [main.py:6140] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Answers collected: 0
2025-07-20 13:46:48,950 - INFO - [main.py:6141] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Levels failed: 0
2025-07-20 13:46:48,950 - INFO - [main.py:6099] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:48,950 - INFO - [main.py:6145] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   State update valid: True
2025-07-20 13:46:48,950 - INFO - [main.py:6152] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf]   Diagnostic complete: False
2025-07-20 13:46:48,951 - WARNING - [main.py:9029] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:46:48,951 - INFO - [main.py:9037] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:46:48,952 - INFO - [main.py:9038] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:46:49,442 - WARNING - [main.py:9083] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:46:49,442 - WARNING - [main.py:9084] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Phase: smart_diagnostic_q4
2025-07-20 13:46:49,443 - WARNING - [main.py:9085] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Probing Level: 2
2025-07-20 13:46:49,443 - WARNING - [main.py:9086] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Question Index: 0
2025-07-20 13:46:49,443 - WARNING - [main.py:9087] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Diagnostic Complete: False
2025-07-20 13:46:49,443 - WARNING - [main.py:9094] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Quiz Questions Saved: 0
2025-07-20 13:46:49,444 - WARNING - [main.py:9095] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Quiz Answers Saved: 0
2025-07-20 13:46:49,444 - WARNING - [main.py:9096] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅   - Quiz Started: False
2025-07-20 13:46:49,444 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q4
2025-07-20 13:46:49,445 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:46:50,277 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q4
2025-07-20 13:46:50,278 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:50,278 - INFO - [main.py:9211] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:50,279 - INFO - [main.py:16959] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:46:50,281 - DEBUG - [main.py:4726] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:46:50,282 - DEBUG - [main.py:9298] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] No final assessment data found in AI response
2025-07-20 13:46:50,282 - DEBUG - [main.py:9321] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] No lesson completion detected (Phase: smart_diagnostic_q4, Complete: False)
2025-07-20 13:46:50,283 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:46:50,285 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_q3
2025-07-20 13:46:50,285 - DEBUG - [main.py:9347] - 🔒   Final Phase: smart_diagnostic_q4
2025-07-20 13:46:50,286 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: False
2025-07-20 13:46:50,286 - DEBUG - [main.py:9349] - 🔒   Assigned Level: None
2025-07-20 13:46:50,286 - INFO - [main.py:9422] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:46:50,287 - INFO - [main.py:9456] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:46:50,287 - INFO - [main.py:9464] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:46:50,288 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:46:50,288 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:46:50,289 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-20 13:46:50,289 - DEBUG - [main.py:9504] - 🎯   Content: You've hit the nail on the head, Andrea! Your reas...
2025-07-20 13:46:50,289 - DEBUG - [main.py:9505] - 🎯   Request ID: 65d56f05-729e-413d-bfdd-6c086f7d8ecf
2025-07-20 13:46:50,290 - INFO - [main.py:9511] - [65d56f05-729e-413d-bfdd-6c086f7d8ecf] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:46:50,290 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,290 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:50,291 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,292 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:46:50,292 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,292 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,292 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,292 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:46:50,293 - WARNING - [main.py:703] - High response time detected: 4.91s for enhance_content_api
2025-07-20 13:47:11,040 - INFO - [main.py:7105] - Incoming request: {"request_id": "df70c6cd-3496-42a0-84a6-e61b3b8b6807", "timestamp": "2025-07-20T12:47:11.040326+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-20T12:44:56.588Z"}, {"role": "user", "content": "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", "timestamp": "2025-07-20T12:45:49.393Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You're absolutely right \u2013 it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", "timestamp": "2025-07-20T12:45:54.932Z"}, {"role": "user", "content": "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", "timestamp": "2025-07-20T12:46:16.772Z"}, {"role": "assistant", "content": "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", "timestamp": "2025-07-20T12:46:21.900Z"}, {"role": "user", "content": "Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!", "timestamp": "2025-07-20T12:46:44.923Z"}, {"role": "assistant", "content": "You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or \"famous\" for a product definitely helps it sell better. Great thinking!\n\nLet's try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you've invented a brand new, super fun board game. What's one way you could tell your classmates at school about your game to get them excited to play it?", "timestamp": "2025-07-20T12:46:50.311Z"}]}}
2025-07-20 13:47:11,042 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:47:11,043 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '3663', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:47:11,043 - INFO - [auth_decorator.py:70] - 🔒 Request ID: df70c6cd-3496-42a0-84a6-e61b3b8b6807
2025-07-20 13:47:11,043 - INFO - [auth_decorator.py:74] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:47:11,044 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:47:11,044 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:47:11,044 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:47:11,044 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:47:11,044 - INFO - [auth_decorator.py:95] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:47:11,045 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:47:11,045 - INFO - [auth_decorator.py:121] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:47:11,387 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:47:11,387 - INFO - [auth_decorator.py:164] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:47:11,388 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:47:11,390 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:47:11,390 - WARNING - [main.py:7283] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:47:11,391 - WARNING - [main.py:7284] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:47:11,391 - INFO - [main.py:7289] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5",...
2025-07-20 13:47:11,392 - INFO - [main.py:7291] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on Introduction to Marketing. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-20T12:44:56.588Z'}, {'role': 'user', 'content': "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", 'timestamp': '2025-07-20T12:45:49.393Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", 'timestamp': '2025-07-20T12:45:54.932Z'}, {'role': 'user', 'content': "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", 'timestamp': '2025-07-20T12:46:16.772Z'}, {'role': 'assistant', 'content': "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", 'timestamp': '2025-07-20T12:46:21.900Z'}, {'role': 'user', 'content': 'Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!', 'timestamp': '2025-07-20T12:46:44.923Z'}, {'role': 'assistant', 'content': 'You\'ve hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or "famous" for a product definitely helps it sell better. Great thinking!\n\nLet\'s try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you\'ve invented a brand new, super fun board game. What\'s one way you could tell your classmates at school about your game to get them excited to play it?', 'timestamp': '2025-07-20T12:46:50.311Z'}]}
2025-07-20 13:47:11,393 - INFO - [main.py:7293] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:11,393 - INFO - [main.py:7294] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:47:11,393 - INFO - [main.py:7295] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:47:11,394 - DEBUG - [main.py:7331] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:47:11,394 - INFO - [main.py:7332] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:47:11,395 - INFO - [main.py:7372] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:47:11,720 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:47:11,722 - INFO - [main.py:7389] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:47:11,723 - INFO - [main.py:7390] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:47:11,724 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:47:12,184 - INFO - [main.py:7420] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:47:12,185 - INFO - [main.py:7483] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:47:12,186 - INFO - [main.py:7608] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:47:12,187 - INFO - [main.py:7619] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:47:12,188 - INFO - [main.py:7658] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:47:12,189 - INFO - [main.py:4239] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:47:12,601 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:47:12,602 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:47:12,603 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:47:12,604 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:47:12,605 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:47:12,605 - INFO - [main.py:4305] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:47:12,606 - INFO - [main.py:4374] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:47:12,607 - DEBUG - [main.py:4388] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:47:12,607 - DEBUG - [main.py:4391] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:47:12,608 - DEBUG - [main.py:4392] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:47:12,609 - DEBUG - [main.py:4393] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:47:12,610 - INFO - [main.py:4397] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:47:13,066 - INFO - [main.py:4407] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Gemini API call completed in 0.45s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:47:13,066 - DEBUG - [main.py:4429] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:47:13,067 - INFO - [main.py:4434] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:47:13,067 - INFO - [main.py:7692] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:47:13,068 - INFO - [main.py:7729] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:47:13,365 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:47:13,907 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:47:13,908 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:13,909 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:47:13,910 - DEBUG - [main.py:7785] - 🔍   - Current Phase: smart_diagnostic_q4
2025-07-20 13:47:13,911 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 2
2025-07-20 13:47:13,912 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:47:13,913 - WARNING - [main.py:7793] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 SESSION STATE DEBUG:
2025-07-20 13:47:13,913 - WARNING - [main.py:7794] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   - Session exists: True
2025-07-20 13:47:13,914 - WARNING - [main.py:7795] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   - Current phase: smart_diagnostic_q4
2025-07-20 13:47:13,915 - WARNING - [main.py:7796] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_phase', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:47:13,916 - DEBUG - [main.py:7814] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:47:13,917 - DEBUG - [main.py:7815] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Retrieved Phase: 'smart_diagnostic_q4'
2025-07-20 13:47:13,917 - DEBUG - [main.py:7816] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Diagnostic Completed: False
2025-07-20 13:47:13,918 - DEBUG - [main.py:7817] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Assigned Level: None
2025-07-20 13:47:13,919 - WARNING - [main.py:7818] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔒 STATE PROTECTION: phase='smart_diagnostic_q4', diagnostic_done=False, level=None
2025-07-20 13:47:13,920 - INFO - [main.py:7850] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:47:13,920 - INFO - [main.py:7851] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] State protection not triggered
2025-07-20 13:47:13,921 - INFO - [main.py:7899] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:47:13,922 - INFO - [main.py:7900] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   assigned_level_for_teaching (session): None
2025-07-20 13:47:13,923 - INFO - [main.py:7901] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   latest_assessed_level (profile): None
2025-07-20 13:47:13,923 - INFO - [main.py:7902] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   teaching_level_for_returning_student: None
2025-07-20 13:47:13,924 - INFO - [main.py:7903] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   has_completed_diagnostic_before: False
2025-07-20 13:47:13,924 - INFO - [main.py:7904] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   is_first_encounter_for_module: True
2025-07-20 13:47:13,925 - WARNING - [main.py:7909] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:47:13,926 - INFO - [main.py:7915] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 PHASE INVESTIGATION:
2025-07-20 13:47:13,926 - INFO - [main.py:7916] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Retrieved from Firestore: 'smart_diagnostic_q4'
2025-07-20 13:47:13,927 - INFO - [main.py:7917] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:47:13,927 - INFO - [main.py:7918] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Is first encounter: True
2025-07-20 13:47:13,928 - INFO - [main.py:7919] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Diagnostic completed: False
2025-07-20 13:47:13,929 - INFO - [main.py:7925] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ Using stored phase from Firestore: 'smart_diagnostic_q4'
2025-07-20 13:47:13,929 - INFO - [main.py:7939] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:47:13,930 - INFO - [main.py:7941] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Final phase for AI logic: smart_diagnostic_q4
2025-07-20 13:47:13,930 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:47:13,931 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:47:13,931 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:47:13,932 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:47:13,933 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:47:13,933 - INFO - [main.py:7961] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:47:13,934 - INFO - [main.py:5858] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Diagnostic context validation passed
2025-07-20 13:47:13,934 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_q4'
2025-07-20 13:47:13,934 - WARNING - [main.py:8101] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q4' for first encounter
2025-07-20 13:47:13,935 - INFO - [main.py:8124] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q4
2025-07-20 13:47:13,935 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q4'
2025-07-20 13:47:13,936 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q4'
2025-07-20 13:47:13,937 - INFO - [main.py:8136] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Robust context prepared successfully. Phase: smart_diagnostic_q4
2025-07-20 13:47:13,938 - DEBUG - [main.py:8137] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:47:13,938 - WARNING - [main.py:8360] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖 AI PROMPT GENERATION:
2025-07-20 13:47:13,939 - WARNING - [main.py:8361] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - Current phase: smart_diagnostic_q4
2025-07-20 13:47:13,939 - WARNING - [main.py:8362] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - Student query: One way I could tell my classmates about my super fun board game to get them excited is to **bring i...
2025-07-20 13:47:13,940 - WARNING - [main.py:8363] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:47:13,940 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:47:13,941 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_q4
2025-07-20 13:47:13,941 - DEBUG - [main.py:8368] - 🤖   Query: One way I could tell my classmates about my super ...
2025-07-20 13:47:13,942 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:47:13,942 - INFO - [main.py:10008] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:13,943 - INFO - [main.py:10041] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📤 Sending message to existing session: One way I could tell my classmates about my super ...
2025-07-20 13:47:13,943 - INFO - [main.py:5679] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:47:14,706 - INFO - [main.py:5684] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:47:14,707 - INFO - [main.py:10048] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📥 Received response from session: That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fa...
2025-07-20 13:47:14,708 - INFO - [main.py:10073] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q4
2025-07-20 13:47:14,709 - INFO - [main.py:10131] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📝 Diagnostic in progress, checking for question progression
2025-07-20 13:47:14,710 - INFO - [main.py:10162] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:14,711 - WARNING - [main.py:8391] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:47:14,712 - WARNING - [main.py:8392] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - Content length: 498 chars
2025-07-20 13:47:14,713 - WARNING - [main.py:8393] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q5'}
2025-07-20 13:47:14,714 - WARNING - [main.py:8394] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🤖   - Raw state block: None...
2025-07-20 13:47:14,716 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:47:14,717 - DEBUG - [main.py:8790] - 🤖   Content: That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fa...
2025-07-20 13:47:14,719 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'smart_diagnostic_q5'}
2025-07-20 13:47:14,720 - INFO - [main.py:8817] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:47:14,721 - INFO - [main.py:8818] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q5, Session=smart_diagnostic_q4, Final=smart_diagnostic_q5
2025-07-20 13:47:15,003 - WARNING - [main.py:8907] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q4', new_phase='smart_diagnostic_q5'
2025-07-20 13:47:15,004 - INFO - [main.py:6099] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,005 - WARNING - [main.py:8916] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:47:15,006 - WARNING - [main.py:8935] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔄 PHASE TRANSITION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,007 - INFO - [main.py:8946] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:47:15,008 - INFO - [phase_transition_integrity.py:328] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,009 - INFO - [phase_transition_integrity.py:291] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_q4
2025-07-20 13:47:15,009 - INFO - [phase_transition_integrity.py:153] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,010 - INFO - [phase_transition_integrity.py:233] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ TRANSITION VALIDATED: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,012 - INFO - [phase_transition_integrity.py:358] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ TRANSITION APPLIED: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,013 - DEBUG - [phase_transition_integrity.py:841] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:47:15,014 - DEBUG - [phase_transition_integrity.py:874] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 📝 TRANSITION RECORDED: smart_diagnostic_q4 → smart_diagnostic_q5 (valid)
2025-07-20 13:47:15,015 - INFO - [phase_transition_integrity.py:404] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q5
2025-07-20 13:47:15,016 - INFO - [main.py:8993] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ TRANSITION VALIDATED: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,018 - WARNING - [main.py:8998] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:47:15,019 - WARNING - [main.py:8999] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   1. Input phase: 'smart_diagnostic_q4'
2025-07-20 13:47:15,020 - WARNING - [main.py:9000] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:47:15,021 - WARNING - [main.py:9001] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   3. Proposed phase: 'smart_diagnostic_q5'
2025-07-20 13:47:15,023 - WARNING - [main.py:9002] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:47:15,024 - WARNING - [main.py:9003] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍   5. Final phase to save: 'smart_diagnostic_q5'
2025-07-20 13:47:15,025 - WARNING - [main.py:9006] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾 FINAL STATE APPLICATION:
2025-07-20 13:47:15,026 - WARNING - [main.py:9007] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾   - Current phase input: 'smart_diagnostic_q4'
2025-07-20 13:47:15,027 - WARNING - [main.py:9008] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾   - Validated state updates: 24 fields
2025-07-20 13:47:15,028 - WARNING - [main.py:9009] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾   - Final phase to save: 'smart_diagnostic_q5'
2025-07-20 13:47:15,030 - WARNING - [main.py:9010] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾   - Phase change: True
2025-07-20 13:47:15,031 - WARNING - [main.py:9011] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 💾   - Integrity applied: True
2025-07-20 13:47:15,032 - INFO - [main.py:6131] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:47:15,032 - INFO - [main.py:6132] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Phase transition: smart_diagnostic_q4 -> smart_diagnostic_q5
2025-07-20 13:47:15,033 - INFO - [main.py:6133] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Current level: 2
2025-07-20 13:47:15,034 - INFO - [main.py:6134] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Question index: 0
2025-07-20 13:47:15,034 - INFO - [main.py:6135] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   First encounter: True
2025-07-20 13:47:15,035 - INFO - [main.py:6140] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Answers collected: 0
2025-07-20 13:47:15,035 - INFO - [main.py:6141] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Levels failed: 0
2025-07-20 13:47:15,035 - INFO - [main.py:6099] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:15,036 - INFO - [main.py:6145] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   State update valid: True
2025-07-20 13:47:15,036 - INFO - [main.py:6152] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807]   Diagnostic complete: False
2025-07-20 13:47:15,037 - WARNING - [main.py:9029] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:47:15,038 - INFO - [main.py:9037] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:47:15,039 - INFO - [main.py:9038] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:47:15,540 - WARNING - [main.py:9083] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:47:15,541 - WARNING - [main.py:9084] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Phase: smart_diagnostic_q5
2025-07-20 13:47:15,541 - WARNING - [main.py:9085] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Probing Level: 2
2025-07-20 13:47:15,541 - WARNING - [main.py:9086] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Question Index: 0
2025-07-20 13:47:15,542 - WARNING - [main.py:9087] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Diagnostic Complete: False
2025-07-20 13:47:15,542 - WARNING - [main.py:9094] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Quiz Questions Saved: 0
2025-07-20 13:47:15,542 - WARNING - [main.py:9095] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Quiz Answers Saved: 0
2025-07-20 13:47:15,543 - WARNING - [main.py:9096] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅   - Quiz Started: False
2025-07-20 13:47:15,543 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q5
2025-07-20 13:47:15,543 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:47:16,361 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: smart_diagnostic_q5
2025-07-20 13:47:16,361 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:16,362 - INFO - [main.py:9211] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:16,362 - INFO - [main.py:16959] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:47:16,362 - DEBUG - [main.py:4726] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:47:16,363 - DEBUG - [main.py:9298] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] No final assessment data found in AI response
2025-07-20 13:47:16,363 - DEBUG - [main.py:9321] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] No lesson completion detected (Phase: smart_diagnostic_q5, Complete: False)
2025-07-20 13:47:16,363 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:47:16,364 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_q4
2025-07-20 13:47:16,365 - DEBUG - [main.py:9347] - 🔒   Final Phase: smart_diagnostic_q5
2025-07-20 13:47:16,366 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: False
2025-07-20 13:47:16,367 - DEBUG - [main.py:9349] - 🔒   Assigned Level: None
2025-07-20 13:47:16,367 - INFO - [main.py:9422] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:47:16,368 - INFO - [main.py:9456] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:47:16,368 - INFO - [main.py:9464] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:47:16,369 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:47:16,369 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:16,370 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-20 13:47:16,370 - DEBUG - [main.py:9504] - 🎯   Content: That's a brilliant idea, Andrea! Showing your frie...
2025-07-20 13:47:16,371 - DEBUG - [main.py:9505] - 🎯   Request ID: df70c6cd-3496-42a0-84a6-e61b3b8b6807
2025-07-20 13:47:16,371 - INFO - [main.py:9511] - [df70c6cd-3496-42a0-84a6-e61b3b8b6807] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:47:16,372 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,372 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,372 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,372 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:16,373 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:16,373 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:16,373 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:16,373 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,374 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:16,374 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,374 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,374 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,375 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:16,375 - WARNING - [main.py:703] - High response time detected: 4.99s for enhance_content_api
2025-07-20 13:47:35,708 - INFO - [main.py:7105] - Incoming request: {"request_id": "2bea668e-880c-4552-b3b4-b6f23f5252bc", "timestamp": "2025-07-20T12:47:35.707950+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like \"Super Fun New Board Game!\" and \"Adventure Awaits!\" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I'm not playing right then.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "user", "content": "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", "timestamp": "2025-07-20T12:45:49.393Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You're absolutely right \u2013 it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", "timestamp": "2025-07-20T12:45:54.932Z"}, {"role": "user", "content": "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", "timestamp": "2025-07-20T12:46:16.772Z"}, {"role": "assistant", "content": "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", "timestamp": "2025-07-20T12:46:21.900Z"}, {"role": "user", "content": "Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!", "timestamp": "2025-07-20T12:46:44.923Z"}, {"role": "assistant", "content": "You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or \"famous\" for a product definitely helps it sell better. Great thinking!\n\nLet's try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you've invented a brand new, super fun board game. What's one way you could tell your classmates at school about your game to get them excited to play it?", "timestamp": "2025-07-20T12:46:50.311Z"}, {"role": "user", "content": "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", "timestamp": "2025-07-20T12:47:10.934Z"}, {"role": "assistant", "content": "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", "timestamp": "2025-07-20T12:47:16.390Z"}]}}
2025-07-20 13:47:35,708 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:47:35,709 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '4386', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:47:35,709 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 2bea668e-880c-4552-b3b4-b6f23f5252bc
2025-07-20 13:47:35,709 - INFO - [auth_decorator.py:74] - [2bea668e-880c-4552-b3b4-b6f23f5252bc][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:47:35,709 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:47:35,710 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:47:35,710 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:47:35,710 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:47:35,710 - INFO - [auth_decorator.py:95] - [2bea668e-880c-4552-b3b4-b6f23f5252bc][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:47:35,711 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:47:35,711 - INFO - [auth_decorator.py:121] - [2bea668e-880c-4552-b3b4-b6f23f5252bc][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:47:36,020 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:47:36,021 - INFO - [auth_decorator.py:164] - [2bea668e-880c-4552-b3b4-b6f23f5252bc][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:47:36,022 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:47:36,024 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:47:36,025 - WARNING - [main.py:7283] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:47:36,025 - WARNING - [main.py:7284] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:47:36,026 - INFO - [main.py:7289] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like \"Super Fun New Board Game!\" and \"Adventure Awaits!\" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious...
2025-07-20 13:47:36,027 - INFO - [main.py:7291] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': 'Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like "Super Fun New Board Game!" and "Adventure Awaits!" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I\'m not playing right then.', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'user', 'content': "Hello!  Hmm, marketing is like telling people about something you have, so they want to buy it. It's about making sure lots of people know about your toys or your yummy snacks.", 'timestamp': '2025-07-20T12:45:49.393Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You're absolutely right – it's about letting people know about what you have so they want it.\n\nLet's move on to the next question.\n\n**Question 2 of 5 (Level 3 Exploration):** Have you ever seen or heard of ways people try to tell others about their products or services? Can you give me an example?", 'timestamp': '2025-07-20T12:45:54.932Z'}, {'role': 'user', 'content': "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", 'timestamp': '2025-07-20T12:46:16.772Z'}, {'role': 'assistant', 'content': "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", 'timestamp': '2025-07-20T12:46:21.900Z'}, {'role': 'user', 'content': 'Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!', 'timestamp': '2025-07-20T12:46:44.923Z'}, {'role': 'assistant', 'content': 'You\'ve hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or "famous" for a product definitely helps it sell better. Great thinking!\n\nLet\'s try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you\'ve invented a brand new, super fun board game. What\'s one way you could tell your classmates at school about your game to get them excited to play it?', 'timestamp': '2025-07-20T12:46:50.311Z'}, {'role': 'user', 'content': "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", 'timestamp': '2025-07-20T12:47:10.934Z'}, {'role': 'assistant', 'content': "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", 'timestamp': '2025-07-20T12:47:16.390Z'}]}
2025-07-20 13:47:36,028 - INFO - [main.py:7293] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:36,029 - INFO - [main.py:7294] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:47:36,029 - INFO - [main.py:7295] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:47:36,030 - DEBUG - [main.py:7331] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:47:36,031 - INFO - [main.py:7332] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:47:36,031 - INFO - [main.py:7372] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:47:36,346 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:47:36,346 - INFO - [main.py:7389] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:47:36,347 - INFO - [main.py:7390] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:47:36,347 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:47:36,830 - INFO - [main.py:7420] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:47:36,833 - INFO - [main.py:7483] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:47:36,834 - INFO - [main.py:7608] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:47:36,835 - INFO - [main.py:7619] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:47:36,836 - INFO - [main.py:7658] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:47:36,837 - INFO - [main.py:4239] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:47:37,154 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:47:37,155 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:47:37,155 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:47:37,156 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:47:37,156 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:47:37,157 - INFO - [main.py:4305] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:47:37,158 - INFO - [main.py:4374] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:47:37,158 - DEBUG - [main.py:4388] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:47:37,159 - DEBUG - [main.py:4391] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:47:37,159 - DEBUG - [main.py:4392] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:47:37,160 - DEBUG - [main.py:4393] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:47:37,161 - INFO - [main.py:4397] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:47:37,661 - INFO - [main.py:4407] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Gemini API call completed in 0.50s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:47:37,662 - DEBUG - [main.py:4429] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:47:37,663 - INFO - [main.py:4434] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:47:37,665 - INFO - [main.py:7692] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:47:37,666 - INFO - [main.py:7729] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:47:37,959 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:47:38,472 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:47:38,473 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:38,473 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:47:38,473 - DEBUG - [main.py:7785] - 🔍   - Current Phase: smart_diagnostic_q5
2025-07-20 13:47:38,474 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 2
2025-07-20 13:47:38,474 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:47:38,474 - WARNING - [main.py:7793] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 SESSION STATE DEBUG:
2025-07-20 13:47:38,474 - WARNING - [main.py:7794] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - Session exists: True
2025-07-20 13:47:38,475 - WARNING - [main.py:7795] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - Current phase: smart_diagnostic_q5
2025-07-20 13:47:38,475 - WARNING - [main.py:7796] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_phase', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:47:38,476 - DEBUG - [main.py:7814] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:47:38,476 - DEBUG - [main.py:7815] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Retrieved Phase: 'smart_diagnostic_q5'
2025-07-20 13:47:38,476 - DEBUG - [main.py:7816] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Diagnostic Completed: False
2025-07-20 13:47:38,477 - DEBUG - [main.py:7817] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Assigned Level: None
2025-07-20 13:47:38,478 - WARNING - [main.py:7818] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔒 STATE PROTECTION: phase='smart_diagnostic_q5', diagnostic_done=False, level=None
2025-07-20 13:47:38,478 - INFO - [main.py:7850] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:47:38,479 - INFO - [main.py:7851] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] State protection not triggered
2025-07-20 13:47:38,479 - INFO - [main.py:7899] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:47:38,481 - INFO - [main.py:7900] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   assigned_level_for_teaching (session): None
2025-07-20 13:47:38,482 - INFO - [main.py:7901] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   latest_assessed_level (profile): None
2025-07-20 13:47:38,482 - INFO - [main.py:7902] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   teaching_level_for_returning_student: None
2025-07-20 13:47:38,482 - INFO - [main.py:7903] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   has_completed_diagnostic_before: False
2025-07-20 13:47:38,483 - INFO - [main.py:7904] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   is_first_encounter_for_module: True
2025-07-20 13:47:38,484 - WARNING - [main.py:7909] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:47:38,484 - INFO - [main.py:7915] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 PHASE INVESTIGATION:
2025-07-20 13:47:38,485 - INFO - [main.py:7916] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Retrieved from Firestore: 'smart_diagnostic_q5'
2025-07-20 13:47:38,485 - INFO - [main.py:7917] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:47:38,485 - INFO - [main.py:7918] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Is first encounter: True
2025-07-20 13:47:38,486 - INFO - [main.py:7919] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Diagnostic completed: False
2025-07-20 13:47:38,486 - INFO - [main.py:7925] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ Using stored phase from Firestore: 'smart_diagnostic_q5'
2025-07-20 13:47:38,487 - INFO - [main.py:7939] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:47:38,487 - INFO - [main.py:7941] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Final phase for AI logic: smart_diagnostic_q5
2025-07-20 13:47:38,487 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:47:38,488 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:47:38,488 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:47:38,488 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:47:38,488 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:47:38,489 - INFO - [main.py:7961] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:47:38,489 - INFO - [main.py:5858] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Diagnostic context validation passed
2025-07-20 13:47:38,489 - INFO - [main.py:5887] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_q5'
2025-07-20 13:47:38,489 - WARNING - [main.py:8101] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q5' for first encounter
2025-07-20 13:47:38,490 - INFO - [main.py:8124] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q5
2025-07-20 13:47:38,490 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q5'
2025-07-20 13:47:38,490 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q5'
2025-07-20 13:47:38,490 - INFO - [main.py:8136] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Robust context prepared successfully. Phase: smart_diagnostic_q5
2025-07-20 13:47:38,491 - DEBUG - [main.py:8137] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:47:38,491 - WARNING - [main.py:8360] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖 AI PROMPT GENERATION:
2025-07-20 13:47:38,491 - WARNING - [main.py:8361] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - Current phase: smart_diagnostic_q5
2025-07-20 13:47:38,491 - WARNING - [main.py:8362] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - Student query: Okay, another way I could tell people about my new board game without them seeing me play it directl...
2025-07-20 13:47:38,492 - WARNING - [main.py:8363] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:47:38,492 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:47:38,492 - DEBUG - [main.py:8367] - 🤖   Phase: smart_diagnostic_q5
2025-07-20 13:47:38,493 - DEBUG - [main.py:8368] - 🤖   Query: Okay, another way I could tell people about my new...
2025-07-20 13:47:38,493 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:47:38,493 - INFO - [main.py:10008] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:38,493 - INFO - [main.py:10041] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📤 Sending message to existing session: Okay, another way I could tell people about my new...
2025-07-20 13:47:38,494 - INFO - [main.py:5679] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:47:39,501 - INFO - [main.py:5684] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:47:39,503 - INFO - [main.py:10048] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📥 Received response from session: Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉

**Diagnostic Resu...
2025-07-20 13:47:39,504 - INFO - [main.py:10073] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q5
2025-07-20 13:47:39,506 - INFO - [main.py:10091] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ DIAGNOSTIC COMPLETION DETECTED
2025-07-20 13:47:39,519 - INFO - [main.py:10116] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🎯 EXTRACTED TEACHING LEVEL: 6
2025-07-20 13:47:39,520 - INFO - [main.py:10128] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🚀 DIAGNOSTIC → TEACHING TRANSITION PREPARED
2025-07-20 13:47:39,520 - WARNING - [main.py:8391] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:47:39,521 - WARNING - [main.py:8392] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - Content length: 1065 chars
2025-07-20 13:47:39,522 - WARNING - [main.py:8393] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - State updates: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 6, 'current_session_working_level': 6, 'latest_assessed_level_for_module': 6, 'diagnostic_complete': True}
2025-07-20 13:47:39,523 - WARNING - [main.py:8394] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🤖   - Raw state block: None...
2025-07-20 13:47:39,523 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:47:39,524 - DEBUG - [main.py:8790] - 🤖   Content: Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉

**Diagnostic Resu...
2025-07-20 13:47:39,525 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 6, 'current_session_working_level': 6, 'latest_assessed_level_for_module': 6, 'diagnostic_complete': True}
2025-07-20 13:47:39,526 - INFO - [main.py:8817] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:47:39,527 - INFO - [main.py:8818] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] CURRENT PHASE DETERMINATION: AI=teaching_start, Session=smart_diagnostic_q5, Final=teaching_start
2025-07-20 13:47:39,851 - WARNING - [main.py:8907] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q5', new_phase='teaching_start'
2025-07-20 13:47:39,852 - INFO - [main.py:6099] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,853 - WARNING - [main.py:8916] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:47:39,854 - WARNING - [main.py:8935] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔄 PHASE TRANSITION: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,855 - INFO - [main.py:8946] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:47:39,855 - INFO - [phase_transition_integrity.py:328] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,856 - INFO - [phase_transition_integrity.py:291] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase smart_diagnostic_q5
2025-07-20 13:47:39,857 - WARNING - [phase_transition_integrity.py:340] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-20 13:47:39,857 - WARNING - [phase_transition_integrity.py:341] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-20 13:47:39,858 - WARNING - [phase_transition_integrity.py:342] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - assigned_level_for_teaching in state_updates: True
2025-07-20 13:47:39,859 - WARNING - [phase_transition_integrity.py:343] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-20 13:47:39,860 - WARNING - [phase_transition_integrity.py:345] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - assigned_level_for_teaching value: 6
2025-07-20 13:47:39,861 - WARNING - [phase_transition_integrity.py:346] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - state_updates keys: ['new_phase', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'current_session_working_level', 'latest_assessed_level_for_module', 'diagnostic_complete']
2025-07-20 13:47:39,861 - WARNING - [phase_transition_integrity.py:347] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-20 13:47:39,862 - INFO - [phase_transition_integrity.py:153] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,864 - INFO - [phase_transition_integrity.py:233] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,865 - INFO - [phase_transition_integrity.py:358] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ TRANSITION APPLIED: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,866 - DEBUG - [phase_transition_integrity.py:841] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:47:39,866 - DEBUG - [phase_transition_integrity.py:874] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 📝 TRANSITION RECORDED: smart_diagnostic_q5 → teaching_start (valid)
2025-07-20 13:47:39,868 - INFO - [phase_transition_integrity.py:404] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching_start
2025-07-20 13:47:39,868 - INFO - [main.py:8993] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,869 - WARNING - [main.py:8998] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:47:39,870 - WARNING - [main.py:8999] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   1. Input phase: 'smart_diagnostic_q5'
2025-07-20 13:47:39,870 - WARNING - [main.py:9000] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:47:39,871 - WARNING - [main.py:9001] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   3. Proposed phase: 'teaching_start'
2025-07-20 13:47:39,872 - WARNING - [main.py:9002] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:47:39,873 - WARNING - [main.py:9003] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍   5. Final phase to save: 'teaching_start'
2025-07-20 13:47:39,874 - WARNING - [main.py:9006] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾 FINAL STATE APPLICATION:
2025-07-20 13:47:39,874 - WARNING - [main.py:9007] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾   - Current phase input: 'smart_diagnostic_q5'
2025-07-20 13:47:39,875 - WARNING - [main.py:9008] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾   - Validated state updates: 27 fields
2025-07-20 13:47:39,876 - WARNING - [main.py:9009] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾   - Final phase to save: 'teaching_start'
2025-07-20 13:47:39,876 - WARNING - [main.py:9010] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾   - Phase change: True
2025-07-20 13:47:39,877 - WARNING - [main.py:9011] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 💾   - Integrity applied: True
2025-07-20 13:47:39,878 - INFO - [main.py:6131] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:47:39,878 - INFO - [main.py:6132] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Phase transition: smart_diagnostic_q5 -> teaching_start
2025-07-20 13:47:39,879 - INFO - [main.py:6133] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Current level: 2
2025-07-20 13:47:39,879 - INFO - [main.py:6134] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Question index: 0
2025-07-20 13:47:39,880 - INFO - [main.py:6135] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   First encounter: True
2025-07-20 13:47:39,880 - INFO - [main.py:6140] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Answers collected: 0
2025-07-20 13:47:39,881 - INFO - [main.py:6141] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Levels failed: 0
2025-07-20 13:47:39,881 - INFO - [main.py:6099] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:39,882 - INFO - [main.py:6145] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   State update valid: True
2025-07-20 13:47:39,883 - INFO - [main.py:6152] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Diagnostic complete: True
2025-07-20 13:47:39,883 - INFO - [main.py:6154] - [2bea668e-880c-4552-b3b4-b6f23f5252bc]   Assigned level: 6
2025-07-20 13:47:39,884 - WARNING - [main.py:9024] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔧 PROBING LEVEL SYNC: Set probing level to 6 to match assigned teaching level
2025-07-20 13:47:39,885 - WARNING - [main.py:9029] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:47:39,886 - INFO - [main.py:9037] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-20 13:47:39,886 - INFO - [main.py:9038] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:47:40,348 - WARNING - [main.py:9083] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:47:40,349 - WARNING - [main.py:9084] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Phase: teaching_start
2025-07-20 13:47:40,350 - WARNING - [main.py:9085] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Probing Level: 6
2025-07-20 13:47:40,350 - WARNING - [main.py:9086] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Question Index: 0
2025-07-20 13:47:40,351 - WARNING - [main.py:9087] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Diagnostic Complete: True
2025-07-20 13:47:40,351 - WARNING - [main.py:9094] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Quiz Questions Saved: 0
2025-07-20 13:47:40,352 - WARNING - [main.py:9095] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Quiz Answers Saved: 0
2025-07-20 13:47:40,352 - WARNING - [main.py:9096] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅   - Quiz Started: False
2025-07-20 13:47:40,353 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching_start
2025-07-20 13:47:40,354 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:47:41,151 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching_start
2025-07-20 13:47:41,153 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:41,154 - INFO - [main.py:9211] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:41,155 - INFO - [main.py:16959] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:47:41,156 - DEBUG - [main.py:4726] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:47:41,157 - DEBUG - [main.py:9298] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] No final assessment data found in AI response
2025-07-20 13:47:41,158 - DEBUG - [main.py:9321] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] No lesson completion detected (Phase: teaching_start, Complete: False)
2025-07-20 13:47:41,159 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:47:41,160 - DEBUG - [main.py:9346] - 🔒   Current Phase: smart_diagnostic_q5
2025-07-20 13:47:41,162 - DEBUG - [main.py:9347] - 🔒   Final Phase: teaching_start
2025-07-20 13:47:41,163 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: True
2025-07-20 13:47:41,164 - DEBUG - [main.py:9349] - 🔒   Assigned Level: 6
2025-07-20 13:47:41,165 - INFO - [main.py:9422] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:47:41,167 - INFO - [main.py:9456] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:47:41,169 - INFO - [main.py:9464] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:47:41,170 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:47:41,170 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:47:41,171 - DEBUG - [main.py:9503] - 🎯   Phase: smart_diagnostic_q5 → teaching_start
2025-07-20 13:47:41,172 - DEBUG - [main.py:9504] - 🎯   Content: Excellent! You've completed all 5 questions of the...
2025-07-20 13:47:41,173 - DEBUG - [main.py:9505] - 🎯   Request ID: 2bea668e-880c-4552-b3b4-b6f23f5252bc
2025-07-20 13:47:41,173 - INFO - [main.py:9511] - [2bea668e-880c-4552-b3b4-b6f23f5252bc] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:47:41,174 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,175 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,175 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,176 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:41,177 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:41,177 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:41,178 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:41,178 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,179 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:47:41,179 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,180 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,180 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,180 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:47:41,183 - WARNING - [main.py:703] - High response time detected: 5.16s for enhance_content_api
2025-07-20 13:48:07,598 - INFO - [main.py:7105] - Incoming request: {"request_id": "9448890e-8e23-4d3c-9a3f-47ea0fe406cd", "timestamp": "2025-07-20T12:48:07.597287+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "user", "content": "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", "timestamp": "2025-07-20T12:46:16.772Z"}, {"role": "assistant", "content": "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", "timestamp": "2025-07-20T12:46:21.900Z"}, {"role": "user", "content": "Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!", "timestamp": "2025-07-20T12:46:44.923Z"}, {"role": "assistant", "content": "You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or \"famous\" for a product definitely helps it sell better. Great thinking!\n\nLet's try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you've invented a brand new, super fun board game. What's one way you could tell your classmates at school about your game to get them excited to play it?", "timestamp": "2025-07-20T12:46:50.311Z"}, {"role": "user", "content": "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", "timestamp": "2025-07-20T12:47:10.934Z"}, {"role": "assistant", "content": "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", "timestamp": "2025-07-20T12:47:16.390Z"}, {"role": "user", "content": "Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like \"Super Fun New Board Game!\" and \"Adventure Awaits!\" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I'm not playing right then.", "timestamp": "2025-07-20T12:47:35.601Z"}, {"role": "assistant", "content": "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n\n**Diagnostic Results:**\n\u2705 Questions Completed: 5/5\n\ud83d\udcca Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! \ud83d\ude80\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", "timestamp": "2025-07-20T12:47:41.207Z"}]}}
2025-07-20 13:48:07,600 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:48:07,601 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '5247', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:48:07,601 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 9448890e-8e23-4d3c-9a3f-47ea0fe406cd
2025-07-20 13:48:07,601 - INFO - [auth_decorator.py:74] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:48:07,602 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:48:07,602 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:48:07,603 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:48:07,603 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:48:07,603 - INFO - [auth_decorator.py:95] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:48:07,604 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:48:07,604 - INFO - [auth_decorator.py:121] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:48:07,914 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:07,916 - INFO - [auth_decorator.py:164] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:07,917 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:48:07,921 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:48:07,922 - WARNING - [main.py:7283] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:48:07,923 - WARNING - [main.py:7284] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:48:07,924 - INFO - [main.py:7289] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","subject":"Entrepreneurship","session_id":"fallback-1...
2025-07-20 13:48:07,926 - INFO - [main.py:7291] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': 'Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'user', 'content': "Oh yes! I see it all the time.  Like **TV commercials!** When I'm watching my cartoons, sometimes a super colourful and loud advert comes on showing a new toy or a yummy-looking cereal. It tries to make me really want it!", 'timestamp': '2025-07-20T12:46:16.772Z'}, {'role': 'assistant', 'content': "That's a perfect example, Andrea! TV commercials are a very common and effective way to market. They use visuals and sounds to grab your attention and make you want to try something.\n\nNow for the third question!\n\n**Question 3 of 5 (Level 4 Connection):** Why do you think businesses or people selling things want to tell *lots* of people about what they offer, instead of just a few?", 'timestamp': '2025-07-20T12:46:21.900Z'}, {'role': 'user', 'content': 'Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!', 'timestamp': '2025-07-20T12:46:44.923Z'}, {'role': 'assistant', 'content': 'You\'ve hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or "famous" for a product definitely helps it sell better. Great thinking!\n\nLet\'s try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you\'ve invented a brand new, super fun board game. What\'s one way you could tell your classmates at school about your game to get them excited to play it?', 'timestamp': '2025-07-20T12:46:50.311Z'}, {'role': 'user', 'content': "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", 'timestamp': '2025-07-20T12:47:10.934Z'}, {'role': 'assistant', 'content': "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", 'timestamp': '2025-07-20T12:47:16.390Z'}, {'role': 'user', 'content': 'Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like "Super Fun New Board Game!" and "Adventure Awaits!" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I\'m not playing right then.', 'timestamp': '2025-07-20T12:47:35.601Z'}, {'role': 'assistant', 'content': "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n\n**Diagnostic Results:**\n✅ Questions Completed: 5/5\n📊 Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! 🚀\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", 'timestamp': '2025-07-20T12:47:41.207Z'}]}
2025-07-20 13:48:07,927 - INFO - [main.py:7293] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:07,928 - INFO - [main.py:7294] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:48:07,929 - INFO - [main.py:7295] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:48:07,930 - DEBUG - [main.py:7331] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:07,931 - INFO - [main.py:7332] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:07,932 - INFO - [main.py:7372] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:48:08,229 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:48:08,229 - INFO - [main.py:7389] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:48:08,230 - INFO - [main.py:7390] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:48:08,231 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:48:08,702 - INFO - [main.py:7420] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:48:08,703 - INFO - [main.py:7483] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:48:08,703 - INFO - [main.py:7608] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:48:08,703 - INFO - [main.py:7619] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:48:08,704 - INFO - [main.py:7658] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:48:08,704 - INFO - [main.py:4239] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:48:09,051 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:48:09,052 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:48:09,052 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:48:09,053 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:48:09,054 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:48:09,054 - INFO - [main.py:4305] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:48:09,055 - INFO - [main.py:4374] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:48:09,056 - DEBUG - [main.py:4388] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:48:09,056 - DEBUG - [main.py:4391] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:48:09,057 - DEBUG - [main.py:4392] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:48:09,058 - DEBUG - [main.py:4393] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:48:09,058 - INFO - [main.py:4397] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:48:09,582 - INFO - [main.py:4407] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Gemini API call completed in 0.52s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:48:09,582 - DEBUG - [main.py:4429] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:48:09,583 - INFO - [main.py:4434] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:48:09,583 - INFO - [main.py:7692] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:48:09,583 - INFO - [main.py:7729] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:48:09,854 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:48:10,356 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:48:10,357 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:10,358 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:48:10,359 - DEBUG - [main.py:7785] - 🔍   - Current Phase: teaching_start
2025-07-20 13:48:10,359 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 6
2025-07-20 13:48:10,360 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:48:10,361 - WARNING - [main.py:7793] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 SESSION STATE DEBUG:
2025-07-20 13:48:10,361 - WARNING - [main.py:7794] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   - Session exists: True
2025-07-20 13:48:10,362 - WARNING - [main.py:7795] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   - Current phase: teaching_start
2025-07-20 13:48:10,363 - WARNING - [main.py:7796] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'current_phase', 'is_first_encounter_for_module', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-20 13:48:10,364 - DEBUG - [main.py:7814] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:48:10,365 - DEBUG - [main.py:7815] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Retrieved Phase: 'teaching_start'
2025-07-20 13:48:10,366 - DEBUG - [main.py:7816] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Diagnostic Completed: True
2025-07-20 13:48:10,367 - DEBUG - [main.py:7817] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Assigned Level: None
2025-07-20 13:48:10,367 - WARNING - [main.py:7818] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔒 STATE PROTECTION: phase='teaching_start', diagnostic_done=True, level=None
2025-07-20 13:48:10,368 - INFO - [main.py:7850] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ State protection not triggered (diagnostic=True, level=None)
2025-07-20 13:48:10,369 - INFO - [main.py:7851] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] State protection not triggered
2025-07-20 13:48:10,369 - INFO - [main.py:7880] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎯 ADVANCED PHASE DETECTED: 'teaching_start' - treating as returning student
2025-07-20 13:48:10,370 - INFO - [main.py:7899] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:48:10,371 - INFO - [main.py:7900] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   assigned_level_for_teaching (session): None
2025-07-20 13:48:10,371 - INFO - [main.py:7901] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   latest_assessed_level (profile): None
2025-07-20 13:48:10,372 - INFO - [main.py:7902] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   teaching_level_for_returning_student: None
2025-07-20 13:48:10,372 - INFO - [main.py:7903] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   has_completed_diagnostic_before: True
2025-07-20 13:48:10,373 - INFO - [main.py:7904] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   is_first_encounter_for_module: False
2025-07-20 13:48:10,373 - INFO - [main.py:7912] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ RETURNING STUDENT: Using existing diagnostic state - diagnostic_completed=True
2025-07-20 13:48:10,374 - INFO - [main.py:7915] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 PHASE INVESTIGATION:
2025-07-20 13:48:10,374 - INFO - [main.py:7916] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Retrieved from Firestore: 'teaching_start'
2025-07-20 13:48:10,375 - INFO - [main.py:7917] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:48:10,375 - INFO - [main.py:7918] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Is first encounter: False
2025-07-20 13:48:10,376 - INFO - [main.py:7919] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Diagnostic completed: True
2025-07-20 13:48:10,377 - INFO - [main.py:7925] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ Using stored phase from Firestore: 'teaching_start'
2025-07-20 13:48:10,377 - INFO - [main.py:7939] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:48:10,378 - INFO - [main.py:7941] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Final phase for AI logic: teaching_start
2025-07-20 13:48:10,378 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:48:10,379 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:48:10,379 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:48:10,380 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:48:10,380 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:48:10,381 - INFO - [main.py:5858] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Diagnostic context validation passed
2025-07-20 13:48:10,381 - INFO - [main.py:5881] - DETERMINE_PHASE: Preserving advanced phase: 'teaching_start' - no backward transitions allowed
2025-07-20 13:48:10,382 - INFO - [main.py:8092] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📊 TEACHING PROGRESS CALCULATED:
2025-07-20 13:48:10,383 - INFO - [main.py:8093] -    Interactions: 0
2025-07-20 13:48:10,384 - INFO - [main.py:8094] -    Objectives: 0/2 (0.0%)
2025-07-20 13:48:10,384 - INFO - [main.py:8095] -    Time: 0.0 minutes
2025-07-20 13:48:10,385 - INFO - [main.py:8096] -    Depth Score: 0.00
2025-07-20 13:48:10,386 - INFO - [main.py:8107] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎯 PRESERVING ADVANCED PHASE: 'teaching_start' - no override for advanced phases
2025-07-20 13:48:10,386 - INFO - [main.py:8124] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Skipping diagnostic context enhancement for non-diagnostic phase: teaching_start
2025-07-20 13:48:10,386 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'teaching_start'
2025-07-20 13:48:10,387 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'teaching_start'
2025-07-20 13:48:10,387 - INFO - [main.py:8136] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Robust context prepared successfully. Phase: teaching_start
2025-07-20 13:48:10,388 - DEBUG - [main.py:8137] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:48:10,389 - WARNING - [main.py:8360] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖 AI PROMPT GENERATION:
2025-07-20 13:48:10,389 - WARNING - [main.py:8361] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - Current phase: teaching_start
2025-07-20 13:48:10,390 - WARNING - [main.py:8362] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - Student query: Okay! I remember those!  One was **posters**, where people see something like a big drawing or words...
2025-07-20 13:48:10,390 - WARNING - [main.py:8363] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:48:10,391 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:48:10,391 - DEBUG - [main.py:8367] - 🤖   Phase: teaching_start
2025-07-20 13:48:10,392 - DEBUG - [main.py:8368] - 🤖   Query: Okay! I remember those!  One was **posters**, wher...
2025-07-20 13:48:10,392 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:48:10,393 - INFO - [main.py:10008] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:10,393 - INFO - [main.py:10041] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📤 Sending message to existing session: Okay! I remember those!  One was **posters**, wher...
2025-07-20 13:48:10,393 - INFO - [main.py:5679] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:48:11,516 - INFO - [main.py:5684] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:48:11,517 - INFO - [main.py:10048] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📥 Received response from session: Fantastic recall, Andrea! You've absolutely nailed it.

*   **Posters** are a visual way to market. ...
2025-07-20 13:48:11,518 - INFO - [main.py:10177] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📋 LOGIC-BASED STATE EXTRACTION: No text patterns, relying on structured data only
2025-07-20 13:48:11,519 - WARNING - [main.py:8391] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:48:11,520 - WARNING - [main.py:8392] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - Content length: 833 chars
2025-07-20 13:48:11,521 - WARNING - [main.py:8393] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - State updates: {'new_phase': 'teaching_start'}
2025-07-20 13:48:11,522 - WARNING - [main.py:8394] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🤖   - Raw state block: None...
2025-07-20 13:48:11,522 - INFO - [main.py:8402] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎓 APPLYING TEACHING PHASE ENHANCEMENT
2025-07-20 13:48:11,526 - INFO - [teaching_phase_enhancement.py:396] - Initialized teaching phase for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d: level 6, 2 objectives, max 30 interactions
2025-07-20 13:48:11,526 - INFO - [main.py:8424] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎓 Teaching phase initialized: level 6, 2 objectives
2025-07-20 13:48:11,529 - DEBUG - [teaching_phase_enhancement.py:212] - FAST content depth analysis: 0.50 (length: 833)
2025-07-20 13:48:11,551 - DEBUG - [teaching_phase_enhancement.py:329] - Level consistency validation passed: level 5
2025-07-20 13:48:11,561 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_1': 0.25
2025-07-20 13:48:11,561 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_1' coverage: 0.25
2025-07-20 13:48:11,562 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_2': 0.44
2025-07-20 13:48:11,563 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_2' coverage: 0.44
2025-07-20 13:48:11,565 - INFO - [teaching_phase_enhancement.py:237] - Teaching depth validation failed: insufficient interactions (1 < 3)
2025-07-20 13:48:11,566 - INFO - [teaching_phase_enhancement.py:503] - Teaching interaction processed for fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d: 0/2 objectives covered, depth 0.50
2025-07-20 13:48:11,567 - ERROR - [main.py:8785] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ❌ Teaching phase enhancement error: name 'lesson_phase_from_context' is not defined
2025-07-20 13:48:11,568 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:48:11,569 - DEBUG - [main.py:8790] - 🤖   Content: Fantastic recall, Andrea! You've absolutely nailed it.

*   **Posters** are a visual way to market. ...
2025-07-20 13:48:11,570 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'teaching_start', 'teaching_interactions': 1, 'teaching_depth_score': 0.5, 'objectives_covered': 0, 'coverage_percentage': 0.0, 'teaching_level_consistent': True}
2025-07-20 13:48:11,570 - INFO - [main.py:8817] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:48:11,571 - INFO - [main.py:8818] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] CURRENT PHASE DETERMINATION: AI=teaching_start, Session=teaching_start, Final=teaching_start
2025-07-20 13:48:11,842 - WARNING - [main.py:8907] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 STATE UPDATE VALIDATION: current_phase='teaching_start', new_phase='teaching_start'
2025-07-20 13:48:11,843 - INFO - [main.py:6099] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI state update validation passed: teaching_start → teaching_start
2025-07-20 13:48:11,844 - WARNING - [main.py:8916] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:48:11,845 - WARNING - [main.py:8926] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔄 AUTO-PROGRESSION: teaching_start → teaching (AI maintained same phase)
2025-07-20 13:48:11,846 - WARNING - [main.py:8935] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔄 PHASE TRANSITION: teaching_start → teaching
2025-07-20 13:48:11,847 - INFO - [main.py:8946] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:48:11,848 - INFO - [phase_transition_integrity.py:328] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔄 APPLYING TRANSITION WITH INTEGRITY: teaching_start → teaching
2025-07-20 13:48:11,849 - INFO - [phase_transition_integrity.py:291] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase teaching_start
2025-07-20 13:48:11,849 - INFO - [phase_transition_integrity.py:153] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 PHASE TRANSITION VALIDATION: teaching_start → teaching
2025-07-20 13:48:11,850 - INFO - [phase_transition_integrity.py:233] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ TRANSITION VALIDATED: teaching_start → teaching
2025-07-20 13:48:11,850 - INFO - [phase_transition_integrity.py:358] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ TRANSITION APPLIED: teaching_start → teaching
2025-07-20 13:48:11,851 - DEBUG - [phase_transition_integrity.py:841] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:48:11,851 - DEBUG - [phase_transition_integrity.py:874] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 📝 TRANSITION RECORDED: teaching_start → teaching (valid)
2025-07-20 13:48:11,852 - INFO - [phase_transition_integrity.py:404] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching
2025-07-20 13:48:11,853 - INFO - [main.py:8993] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ TRANSITION VALIDATED: teaching_start → teaching
2025-07-20 13:48:11,854 - WARNING - [main.py:8998] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:48:11,854 - WARNING - [main.py:8999] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   1. Input phase: 'teaching_start'
2025-07-20 13:48:11,855 - WARNING - [main.py:9000] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:48:11,855 - WARNING - [main.py:9001] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   3. Proposed phase: 'teaching'
2025-07-20 13:48:11,856 - WARNING - [main.py:9002] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   4. Integrity validation result: 'valid'
2025-07-20 13:48:11,856 - WARNING - [main.py:9003] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍   5. Final phase to save: 'teaching'
2025-07-20 13:48:11,856 - WARNING - [main.py:9006] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾 FINAL STATE APPLICATION:
2025-07-20 13:48:11,857 - WARNING - [main.py:9007] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾   - Current phase input: 'teaching_start'
2025-07-20 13:48:11,857 - WARNING - [main.py:9008] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾   - Validated state updates: 28 fields
2025-07-20 13:48:11,858 - WARNING - [main.py:9009] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾   - Final phase to save: 'teaching'
2025-07-20 13:48:11,858 - WARNING - [main.py:9010] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾   - Phase change: True
2025-07-20 13:48:11,859 - WARNING - [main.py:9011] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 💾   - Integrity applied: True
2025-07-20 13:48:11,859 - INFO - [main.py:6131] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:48:11,859 - INFO - [main.py:6132] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Phase transition: teaching_start -> teaching
2025-07-20 13:48:11,859 - INFO - [main.py:6133] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Current level: 6
2025-07-20 13:48:11,860 - INFO - [main.py:6134] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Question index: 0
2025-07-20 13:48:11,860 - INFO - [main.py:6135] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   First encounter: False
2025-07-20 13:48:11,860 - INFO - [main.py:6140] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Answers collected: 0
2025-07-20 13:48:11,861 - INFO - [main.py:6141] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Levels failed: 0
2025-07-20 13:48:11,861 - INFO - [main.py:6099] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI state update validation passed: teaching_start → teaching
2025-07-20 13:48:11,861 - INFO - [main.py:6145] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   State update valid: True
2025-07-20 13:48:11,862 - INFO - [main.py:6152] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Diagnostic complete: False
2025-07-20 13:48:11,862 - INFO - [main.py:6154] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd]   Assigned level: 6
2025-07-20 13:48:11,863 - WARNING - [main.py:9024] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔧 PROBING LEVEL SYNC: Set probing level to 6 to match assigned teaching level
2025-07-20 13:48:11,863 - WARNING - [main.py:9029] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:48:11,864 - INFO - [main.py:9037] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-20 13:48:11,864 - INFO - [main.py:9038] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔍 DEBUG original teaching_interactions: 0
2025-07-20 13:48:12,368 - WARNING - [main.py:9083] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:48:12,368 - WARNING - [main.py:9084] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Phase: teaching
2025-07-20 13:48:12,369 - WARNING - [main.py:9085] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Probing Level: 6
2025-07-20 13:48:12,369 - WARNING - [main.py:9086] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Question Index: 0
2025-07-20 13:48:12,370 - WARNING - [main.py:9087] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Diagnostic Complete: True
2025-07-20 13:48:12,371 - WARNING - [main.py:9094] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Quiz Questions Saved: 0
2025-07-20 13:48:12,371 - WARNING - [main.py:9095] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Quiz Answers Saved: 0
2025-07-20 13:48:12,372 - WARNING - [main.py:9096] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅   - Quiz Started: False
2025-07-20 13:48:12,373 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:48:12,373 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:48:13,216 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:48:13,217 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: teaching_start → teaching
2025-07-20 13:48:13,218 - INFO - [main.py:9211] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:13,219 - INFO - [main.py:16959] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:48:13,220 - DEBUG - [main.py:4726] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:48:13,221 - DEBUG - [main.py:9298] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] No final assessment data found in AI response
2025-07-20 13:48:13,222 - DEBUG - [main.py:9321] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] No lesson completion detected (Phase: teaching, Complete: False)
2025-07-20 13:48:13,224 - WARNING - [main.py:9340] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching'.
2025-07-20 13:48:13,226 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:48:13,227 - DEBUG - [main.py:9346] - 🔒   Current Phase: teaching_start
2025-07-20 13:48:13,228 - DEBUG - [main.py:9347] - 🔒   Final Phase: teaching
2025-07-20 13:48:13,229 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: True
2025-07-20 13:48:13,231 - DEBUG - [main.py:9349] - 🔒   Assigned Level: 6
2025-07-20 13:48:13,232 - INFO - [main.py:9422] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:48:13,233 - INFO - [main.py:9456] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:48:13,234 - INFO - [main.py:9464] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:48:13,235 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:48:13,236 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:13,237 - DEBUG - [main.py:9503] - 🎯   Phase: teaching_start → teaching
2025-07-20 13:48:13,238 - DEBUG - [main.py:9504] - 🎯   Content: Fantastic recall, Andrea! You've absolutely nailed...
2025-07-20 13:48:13,239 - DEBUG - [main.py:9505] - 🎯   Request ID: 9448890e-8e23-4d3c-9a3f-47ea0fe406cd
2025-07-20 13:48:13,239 - INFO - [main.py:9511] - [9448890e-8e23-4d3c-9a3f-47ea0fe406cd] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:48:13,240 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,241 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,241 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,242 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:13,242 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:13,243 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:13,244 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:13,244 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,245 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:13,245 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,246 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,247 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,248 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:13,249 - WARNING - [main.py:703] - High response time detected: 5.33s for enhance_content_api
2025-07-20 13:48:32,568 - INFO - [main.py:7105] - Incoming request: {"request_id": "a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b", "timestamp": "2025-07-20T12:48:32.567368+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and happy colours!** Like really colourful and not just boring brown or grey. That makes my eyes go straight to it. 2.  **A really cool picture!** If it's for a game, I'd want to see excited kids playing, or the fun characters from the game. The picture should tell a story right away. 3.  **Big, easy-to-read words** that say something exciting, like \"NEW!\" or \"SUPER FUN!\" or \"YOU'LL LOVE THIS!\" 4.  **A big, clear name of the thing** it's about. Like \"THE AWESOME BOARD GAME!\" 5.  It needs to look **interesting and not too messy**, so I can understand it quickly without having to read a lot.  It's all about catching my eye really fast and making me curious!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "user", "content": "Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!", "timestamp": "2025-07-20T12:46:44.923Z"}, {"role": "assistant", "content": "You've hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or \"famous\" for a product definitely helps it sell better. Great thinking!\n\nLet's try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you've invented a brand new, super fun board game. What's one way you could tell your classmates at school about your game to get them excited to play it?", "timestamp": "2025-07-20T12:46:50.311Z"}, {"role": "user", "content": "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", "timestamp": "2025-07-20T12:47:10.934Z"}, {"role": "assistant", "content": "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", "timestamp": "2025-07-20T12:47:16.390Z"}, {"role": "user", "content": "Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like \"Super Fun New Board Game!\" and \"Adventure Awaits!\" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I'm not playing right then.", "timestamp": "2025-07-20T12:47:35.601Z"}, {"role": "assistant", "content": "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n\n**Diagnostic Results:**\n\u2705 Questions Completed: 5/5\n\ud83d\udcca Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! \ud83d\ude80\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", "timestamp": "2025-07-20T12:47:41.207Z"}, {"role": "user", "content": "Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!", "timestamp": "2025-07-20T12:48:07.493Z"}, {"role": "assistant", "content": "Fantastic recall, Andrea! You've absolutely nailed it.\n\n*   **Posters** are a visual way to market. They catch people's eyes with colourful pictures and catchy words, just like you described for your board game. They're great for getting attention in a specific place, like a school hallway or a shop window.\n\n*   And **word-of-mouth** is incredibly powerful! When your friends tell their friends how much they love your game, that's word-of-mouth marketing. It's like a personal recommendation, and people tend to trust what their friends say.\n\nThese are just two of the many ways entrepreneurs get their names and products out there.\n\nNow, let's think about *why* these methods work. For posters, what do you think makes a poster really effective in grabbing someone's attention? What elements would make *you* stop and look at it?", "timestamp": "2025-07-20T12:48:13.273Z"}]}}
2025-07-20 13:48:32,569 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:48:32,570 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '6244', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:70] - 🔒 Request ID: a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:74] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:48:32,571 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:48:32,572 - INFO - [auth_decorator.py:95] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:48:32,572 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:48:32,572 - INFO - [auth_decorator.py:121] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:48:32,849 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:32,850 - INFO - [auth_decorator.py:164] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:32,850 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-20 13:48:32,851 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:48:32,852 - WARNING - [main.py:7283] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:48:32,852 - WARNING - [main.py:7284] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:48:32,852 - INFO - [main.py:7289] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and happy colours!** Like really colourful and not just boring brown or grey. That makes my eyes go straight to it. 2.  **A really cool picture!** If it's for a game, I'd want to see excited kids playing, or the fun characters from the game. The picture should tell a story right away. 3.  **Big, easy-to-read words** that s...
2025-07-20 13:48:32,853 - INFO - [main.py:7291] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': 'Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and happy colours!** Like really colourful and not just boring brown or grey. That makes my eyes go straight to it. 2.  **A really cool picture!** If it\'s for a game, I\'d want to see excited kids playing, or the fun characters from the game. The picture should tell a story right away. 3.  **Big, easy-to-read words** that say something exciting, like "NEW!" or "SUPER FUN!" or "YOU\'LL LOVE THIS!" 4.  **A big, clear name of the thing** it\'s about. Like "THE AWESOME BOARD GAME!" 5.  It needs to look **interesting and not too messy**, so I can understand it quickly without having to read a lot.  It\'s all about catching my eye really fast and making me curious!', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'user', 'content': 'Businesses want to tell *lots* of people about what they offer because:  1.  **More people means more buyers!** If only a few people know about your yummy snacks, only a few people will buy them. But if lots and lots of people know, then lots and lots of people might buy them. 2.  **To make more money.** The more things they sell, the more money the business makes. This helps them keep making cool new toys or snacks. 3.  **To get famous!** If everyone knows about their product, it becomes popular and everyone talks about it, which helps sell even more.  So, telling lots of people helps them sell much, much more!', 'timestamp': '2025-07-20T12:46:44.923Z'}, {'role': 'assistant', 'content': 'You\'ve hit the nail on the head, Andrea! Your reasons are spot on: more people means more potential customers, which leads to more sales and more money for the business. And yes, becoming well-known or "famous" for a product definitely helps it sell better. Great thinking!\n\nLet\'s try question number four.\n\n**Question 4 of 5 (Level 5 Application):** Imagine you\'ve invented a brand new, super fun board game. What\'s one way you could tell your classmates at school about your game to get them excited to play it?', 'timestamp': '2025-07-20T12:46:50.311Z'}, {'role': 'user', 'content': "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", 'timestamp': '2025-07-20T12:47:10.934Z'}, {'role': 'assistant', 'content': "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", 'timestamp': '2025-07-20T12:47:16.390Z'}, {'role': 'user', 'content': 'Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like "Super Fun New Board Game!" and "Adventure Awaits!" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I\'m not playing right then.', 'timestamp': '2025-07-20T12:47:35.601Z'}, {'role': 'assistant', 'content': "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n\n**Diagnostic Results:**\n✅ Questions Completed: 5/5\n📊 Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! 🚀\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", 'timestamp': '2025-07-20T12:47:41.207Z'}, {'role': 'user', 'content': 'Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!', 'timestamp': '2025-07-20T12:48:07.493Z'}, {'role': 'assistant', 'content': "Fantastic recall, Andrea! You've absolutely nailed it.\n\n*   **Posters** are a visual way to market. They catch people's eyes with colourful pictures and catchy words, just like you described for your board game. They're great for getting attention in a specific place, like a school hallway or a shop window.\n\n*   And **word-of-mouth** is incredibly powerful! When your friends tell their friends how much they love your game, that's word-of-mouth marketing. It's like a personal recommendation, and people tend to trust what their friends say.\n\nThese are just two of the many ways entrepreneurs get their names and products out there.\n\nNow, let's think about *why* these methods work. For posters, what do you think makes a poster really effective in grabbing someone's attention? What elements would make *you* stop and look at it?", 'timestamp': '2025-07-20T12:48:13.273Z'}]}
2025-07-20 13:48:32,853 - INFO - [main.py:7293] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:32,854 - INFO - [main.py:7294] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:48:32,855 - INFO - [main.py:7295] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:48:32,855 - DEBUG - [main.py:7331] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:32,856 - INFO - [main.py:7332] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:32,856 - INFO - [main.py:7372] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:48:33,144 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:48:33,145 - INFO - [main.py:7389] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:48:33,146 - INFO - [main.py:7390] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:48:33,147 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:48:33,655 - INFO - [main.py:7420] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:48:33,656 - INFO - [main.py:7483] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:48:33,657 - INFO - [main.py:7608] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:48:33,658 - INFO - [main.py:7619] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:48:33,658 - INFO - [main.py:7658] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:48:33,659 - INFO - [main.py:4239] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:48:33,944 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:48:33,945 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:48:33,945 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:48:33,945 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:48:33,945 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:48:33,946 - INFO - [main.py:4305] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:48:33,946 - INFO - [main.py:4374] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:48:33,946 - DEBUG - [main.py:4388] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:48:33,946 - DEBUG - [main.py:4391] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:48:33,947 - DEBUG - [main.py:4392] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:48:33,947 - DEBUG - [main.py:4393] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:48:33,948 - INFO - [main.py:4397] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:48:34,461 - INFO - [main.py:4407] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Gemini API call completed in 0.51s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:48:34,462 - DEBUG - [main.py:4429] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:48:34,462 - INFO - [main.py:4434] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:48:34,462 - INFO - [main.py:7692] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:48:34,462 - INFO - [main.py:7729] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:48:34,734 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:48:35,253 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:48:35,254 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:35,255 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:48:35,256 - DEBUG - [main.py:7785] - 🔍   - Current Phase: teaching
2025-07-20 13:48:35,256 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 6
2025-07-20 13:48:35,257 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:48:35,258 - WARNING - [main.py:7793] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 SESSION STATE DEBUG:
2025-07-20 13:48:35,258 - WARNING - [main.py:7794] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   - Session exists: True
2025-07-20 13:48:35,259 - WARNING - [main.py:7795] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   - Current phase: teaching
2025-07-20 13:48:35,260 - WARNING - [main.py:7796] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_phase', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'quiz_started', 'quiz_answers', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'current_session_working_level', 'teaching_interactions']
2025-07-20 13:48:35,261 - DEBUG - [main.py:7814] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:48:35,262 - DEBUG - [main.py:7815] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Retrieved Phase: 'teaching'
2025-07-20 13:48:35,262 - DEBUG - [main.py:7816] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Diagnostic Completed: True
2025-07-20 13:48:35,263 - DEBUG - [main.py:7817] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Assigned Level: None
2025-07-20 13:48:35,264 - WARNING - [main.py:7818] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔒 STATE PROTECTION: phase='teaching', diagnostic_done=True, level=None
2025-07-20 13:48:35,266 - INFO - [main.py:7850] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ State protection not triggered (diagnostic=True, level=None)
2025-07-20 13:48:35,267 - INFO - [main.py:7851] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] State protection not triggered
2025-07-20 13:48:35,267 - INFO - [main.py:7899] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:48:35,268 - INFO - [main.py:7900] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   assigned_level_for_teaching (session): None
2025-07-20 13:48:35,268 - INFO - [main.py:7901] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   latest_assessed_level (profile): None
2025-07-20 13:48:35,268 - INFO - [main.py:7902] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   teaching_level_for_returning_student: None
2025-07-20 13:48:35,269 - INFO - [main.py:7903] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   has_completed_diagnostic_before: False
2025-07-20 13:48:35,269 - INFO - [main.py:7904] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   is_first_encounter_for_module: True
2025-07-20 13:48:35,270 - WARNING - [main.py:7909] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:48:35,270 - INFO - [main.py:7915] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 PHASE INVESTIGATION:
2025-07-20 13:48:35,271 - INFO - [main.py:7916] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Retrieved from Firestore: 'teaching'
2025-07-20 13:48:35,271 - INFO - [main.py:7917] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:48:35,272 - INFO - [main.py:7918] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Is first encounter: True
2025-07-20 13:48:35,273 - INFO - [main.py:7919] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Diagnostic completed: False
2025-07-20 13:48:35,273 - WARNING - [main.py:7927] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ⚠️ Invalid phase format: 'teaching', using default
2025-07-20 13:48:35,274 - INFO - [main.py:7939] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:48:35,274 - INFO - [main.py:7941] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Final phase for AI logic: smart_diagnostic_start
2025-07-20 13:48:35,274 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:48:35,275 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:48:35,275 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:48:35,276 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:48:35,276 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:48:35,276 - INFO - [main.py:7961] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:48:35,277 - INFO - [main.py:5858] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Diagnostic context validation passed
2025-07-20 13:48:35,277 - WARNING - [main.py:5920] - DETERMINE_PHASE: Unclear state but preserving current phase 'teaching' to prevent backward transition
2025-07-20 13:48:35,278 - WARNING - [main.py:8101] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'teaching' for first encounter
2025-07-20 13:48:35,278 - INFO - [main.py:8124] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Skipping diagnostic context enhancement for non-diagnostic phase: teaching
2025-07-20 13:48:35,279 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'teaching'
2025-07-20 13:48:35,279 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'teaching'
2025-07-20 13:48:35,280 - INFO - [main.py:8136] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Robust context prepared successfully. Phase: teaching
2025-07-20 13:48:35,280 - DEBUG - [main.py:8137] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:48:35,281 - WARNING - [main.py:8360] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖 AI PROMPT GENERATION:
2025-07-20 13:48:35,282 - WARNING - [main.py:8361] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - Current phase: teaching
2025-07-20 13:48:35,282 - WARNING - [main.py:8362] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - Student query: Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and hap...
2025-07-20 13:48:35,283 - WARNING - [main.py:8363] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:48:35,283 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:48:35,284 - DEBUG - [main.py:8367] - 🤖   Phase: teaching
2025-07-20 13:48:35,284 - DEBUG - [main.py:8368] - 🤖   Query: Okay, for a poster to really work and make *me* st...
2025-07-20 13:48:35,285 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:48:35,286 - INFO - [main.py:10008] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:35,287 - INFO - [main.py:10041] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📤 Sending message to existing session: Okay, for a poster to really work and make *me* st...
2025-07-20 13:48:35,287 - INFO - [main.py:5679] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:48:36,235 - INFO - [main.py:5684] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:48:36,237 - INFO - [main.py:10048] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📥 Received response from session: That's a brilliant breakdown, Andrea! You've perfectly described what makes a poster effective for y...
2025-07-20 13:48:36,238 - INFO - [main.py:10177] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📋 LOGIC-BASED STATE EXTRACTION: No text patterns, relying on structured data only
2025-07-20 13:48:36,239 - WARNING - [main.py:8391] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:48:36,240 - WARNING - [main.py:8392] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - Content length: 776 chars
2025-07-20 13:48:36,241 - WARNING - [main.py:8393] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - State updates: {'new_phase': 'teaching'}
2025-07-20 13:48:36,242 - WARNING - [main.py:8394] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🤖   - Raw state block: None...
2025-07-20 13:48:36,243 - INFO - [main.py:8402] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🎓 APPLYING TEACHING PHASE ENHANCEMENT
2025-07-20 13:48:36,245 - DEBUG - [teaching_phase_enhancement.py:212] - FAST content depth analysis: 0.50 (length: 776)
2025-07-20 13:48:36,252 - DEBUG - [teaching_phase_enhancement.py:300] - Content level analysis: estimated level 2
2025-07-20 13:48:36,253 - WARNING - [teaching_phase_enhancement.py:327] - Level consistency validation failed: analyzed level 2 vs expected 6
2025-07-20 13:48:36,255 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_1': 0.06
2025-07-20 13:48:36,255 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_1' coverage: 0.20
2025-07-20 13:48:36,257 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_2': 0.14
2025-07-20 13:48:36,258 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_2' coverage: 0.35
2025-07-20 13:48:36,258 - INFO - [teaching_phase_enhancement.py:237] - Teaching depth validation failed: insufficient interactions (2 < 3)
2025-07-20 13:48:36,259 - INFO - [teaching_phase_enhancement.py:503] - Teaching interaction processed for fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d: 0/2 objectives covered, depth 0.50
2025-07-20 13:48:36,260 - ERROR - [main.py:8785] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ❌ Teaching phase enhancement error: name 'lesson_phase_from_context' is not defined
2025-07-20 13:48:36,261 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:48:36,261 - DEBUG - [main.py:8790] - 🤖   Content: That's a brilliant breakdown, Andrea! You've perfectly described what makes a poster effective for y...
2025-07-20 13:48:36,262 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'teaching', 'teaching_interactions': 2, 'teaching_depth_score': 0.5, 'objectives_covered': 0, 'coverage_percentage': 0.0, 'teaching_level_consistent': False}
2025-07-20 13:48:36,263 - INFO - [main.py:8817] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:48:36,263 - INFO - [main.py:8818] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] CURRENT PHASE DETERMINATION: AI=teaching, Session=teaching, Final=teaching
2025-07-20 13:48:36,511 - WARNING - [main.py:8907] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 STATE UPDATE VALIDATION: current_phase='teaching', new_phase='teaching'
2025-07-20 13:48:36,512 - INFO - [main.py:6099] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI state update validation passed: teaching → teaching
2025-07-20 13:48:36,512 - WARNING - [main.py:8916] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:48:36,513 - WARNING - [main.py:8937] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   PHASE MAINTAINED: teaching
2025-07-20 13:48:36,513 - INFO - [main.py:8946] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:48:36,514 - INFO - [phase_transition_integrity.py:328] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔄 APPLYING TRANSITION WITH INTEGRITY: teaching → teaching
2025-07-20 13:48:36,514 - INFO - [phase_transition_integrity.py:291] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase teaching
2025-07-20 13:48:36,515 - INFO - [phase_transition_integrity.py:153] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-20 13:48:36,515 - ERROR - [phase_transition_integrity.py:713] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-20 13:48:36,516 - ERROR - [phase_transition_integrity.py:715] - 🔍   1. Cannot enter teaching phase without completing diagnostic
2025-07-20 13:48:36,517 - ERROR - [phase_transition_integrity.py:716] - 🔍 SESSION DATA DEBUG:
2025-07-20 13:48:36,518 - ERROR - [phase_transition_integrity.py:717] - 🔍   - diagnostic_complete: False
2025-07-20 13:48:36,518 - ERROR - [phase_transition_integrity.py:718] - 🔍   - assigned_level_for_teaching: 6
2025-07-20 13:48:36,518 - ERROR - [phase_transition_integrity.py:719] - 🔍   - teaching_complete: False
2025-07-20 13:48:36,519 - ERROR - [phase_transition_integrity.py:720] - 🔍   - teaching_interactions: 2
2025-07-20 13:48:36,519 - ERROR - [phase_transition_integrity.py:721] - 🔍   - from_phase: teaching, to_phase: teaching
2025-07-20 13:48:36,520 - ERROR - [phase_transition_integrity.py:735] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 STATE CORRECTION ATTEMPT: teaching → teaching
2025-07-20 13:48:36,520 - ERROR - [phase_transition_integrity.py:742] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 CORRECTION CHECK: diagnostic_complete=False, assigned_level=6
2025-07-20 13:48:36,521 - ERROR - [phase_transition_integrity.py:748] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 APPLYING CORRECTION: Setting diagnostic_complete=True
2025-07-20 13:48:36,521 - ERROR - [phase_transition_integrity.py:775] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 CORRECTION RESULT: {'corrected': True, 'corrected_phase': 'teaching', 'corrected_data': {'diagnostic_complete': True}, 'message': 'Corrected diagnostic_complete to True based on assigned level'}
2025-07-20 13:48:36,522 - WARNING - [phase_transition_integrity.py:209] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 STATE CORRECTION APPLIED: Corrected diagnostic_complete to True based on assigned level
2025-07-20 13:48:36,522 - WARNING - [phase_transition_integrity.py:365] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 TRANSITION CORRECTED: teaching → teaching
2025-07-20 13:48:36,523 - DEBUG - [phase_transition_integrity.py:841] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:48:36,523 - DEBUG - [phase_transition_integrity.py:874] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 📝 TRANSITION RECORDED: teaching → teaching (corrected)
2025-07-20 13:48:36,525 - INFO - [phase_transition_integrity.py:404] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching
2025-07-20 13:48:36,525 - WARNING - [main.py:8991] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 TRANSITION CORRECTED: teaching → teaching
2025-07-20 13:48:36,526 - WARNING - [main.py:8998] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:48:36,527 - WARNING - [main.py:8999] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   1. Input phase: 'teaching'
2025-07-20 13:48:36,528 - WARNING - [main.py:9000] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:48:36,528 - WARNING - [main.py:9001] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   3. Proposed phase: 'teaching'
2025-07-20 13:48:36,529 - WARNING - [main.py:9002] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   4. Integrity validation result: 'corrected'
2025-07-20 13:48:36,530 - WARNING - [main.py:9003] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍   5. Final phase to save: 'teaching'
2025-07-20 13:48:36,530 - WARNING - [main.py:9006] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾 FINAL STATE APPLICATION:
2025-07-20 13:48:36,531 - WARNING - [main.py:9007] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾   - Current phase input: 'teaching'
2025-07-20 13:48:36,532 - WARNING - [main.py:9008] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾   - Validated state updates: 28 fields
2025-07-20 13:48:36,533 - WARNING - [main.py:9009] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾   - Final phase to save: 'teaching'
2025-07-20 13:48:36,533 - WARNING - [main.py:9010] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾   - Phase change: False
2025-07-20 13:48:36,534 - WARNING - [main.py:9011] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 💾   - Integrity applied: True
2025-07-20 13:48:36,534 - INFO - [main.py:6131] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:48:36,535 - INFO - [main.py:6132] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Phase transition: teaching -> teaching
2025-07-20 13:48:36,535 - INFO - [main.py:6133] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Current level: 6
2025-07-20 13:48:36,535 - INFO - [main.py:6134] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Question index: 0
2025-07-20 13:48:36,536 - INFO - [main.py:6135] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   First encounter: True
2025-07-20 13:48:36,536 - INFO - [main.py:6140] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Answers collected: 0
2025-07-20 13:48:36,537 - INFO - [main.py:6141] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Levels failed: 0
2025-07-20 13:48:36,537 - INFO - [main.py:6099] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI state update validation passed: teaching → teaching
2025-07-20 13:48:36,538 - INFO - [main.py:6145] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   State update valid: True
2025-07-20 13:48:36,538 - INFO - [main.py:6152] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Diagnostic complete: False
2025-07-20 13:48:36,538 - INFO - [main.py:6154] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b]   Assigned level: 6
2025-07-20 13:48:36,539 - WARNING - [main.py:9024] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 PROBING LEVEL SYNC: Set probing level to 6 to match assigned teaching level
2025-07-20 13:48:36,539 - WARNING - [main.py:9029] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:48:36,540 - INFO - [main.py:9037] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 DEBUG state_updates_from_ai teaching_interactions: 2
2025-07-20 13:48:36,540 - INFO - [main.py:9038] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔍 DEBUG original teaching_interactions: 1
2025-07-20 13:48:37,044 - WARNING - [main.py:9083] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:48:37,045 - WARNING - [main.py:9084] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Phase: teaching
2025-07-20 13:48:37,045 - WARNING - [main.py:9085] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Probing Level: 6
2025-07-20 13:48:37,046 - WARNING - [main.py:9086] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Question Index: 0
2025-07-20 13:48:37,046 - WARNING - [main.py:9087] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Diagnostic Complete: False
2025-07-20 13:48:37,046 - WARNING - [main.py:9094] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Quiz Questions Saved: 0
2025-07-20 13:48:37,047 - WARNING - [main.py:9095] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Quiz Answers Saved: 0
2025-07-20 13:48:37,047 - WARNING - [main.py:9096] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅   - Quiz Started: False
2025-07-20 13:48:37,048 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:48:37,048 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:48:37,861 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:48:37,862 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: teaching → teaching
2025-07-20 13:48:37,863 - INFO - [main.py:9211] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:37,864 - INFO - [main.py:16959] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:48:37,865 - DEBUG - [main.py:4726] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:48:37,866 - DEBUG - [main.py:9298] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] No final assessment data found in AI response
2025-07-20 13:48:37,867 - DEBUG - [main.py:9321] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] No lesson completion detected (Phase: teaching, Complete: False)
2025-07-20 13:48:37,868 - WARNING - [main.py:9340] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching'.
2025-07-20 13:48:37,870 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:48:37,871 - DEBUG - [main.py:9346] - 🔒   Current Phase: teaching
2025-07-20 13:48:37,872 - DEBUG - [main.py:9347] - 🔒   Final Phase: teaching
2025-07-20 13:48:37,873 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: True
2025-07-20 13:48:37,874 - DEBUG - [main.py:9349] - 🔒   Assigned Level: 6
2025-07-20 13:48:37,875 - INFO - [main.py:9422] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:48:37,876 - INFO - [main.py:9456] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:48:37,877 - INFO - [main.py:9464] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:48:37,878 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:48:37,879 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:37,879 - DEBUG - [main.py:9503] - 🎯   Phase: teaching → teaching
2025-07-20 13:48:37,880 - DEBUG - [main.py:9504] - 🎯   Content: That's a brilliant breakdown, Andrea! You've perfe...
2025-07-20 13:48:37,881 - DEBUG - [main.py:9505] - 🎯   Request ID: a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b
2025-07-20 13:48:37,882 - INFO - [main.py:9511] - [a1216bfc-25d3-46d3-a9b4-5d670a3c5d5b] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:48:37,883 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,883 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,884 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,884 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:37,884 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:37,885 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:37,885 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:37,886 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,887 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:48:37,888 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,888 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,889 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,889 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:48:37,890 - WARNING - [main.py:703] - High response time detected: 5.04s for enhance_content_api
2025-07-20 13:48:57,343 - INFO - [main.py:7105] - Incoming request: {"request_id": "d47b82f2-533f-489f-83b9-5241dcc65e30", "timestamp": "2025-07-20T12:48:57.343278+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-ENT-001", "content_to_enhance": "That's a great question! I usually believe my friend more than an advertisement because:  1.  **I trust my friend.** I know them, and I know they wouldn't trick me or just say something is good if it isn't. An advert is made by a company I don't know, and maybe they just want my money. 2.  **My friend isn't trying to sell me anything.** They're just telling me about something they genuinely liked, not because someone paid them to say it. 3.  **They know me!** My friend often knows what kind of movies or toys I like, so their recommendation feels like it's specially for *me*, not just for everyone.  It feels more real and honest when it comes from a friend!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "subject": "Entrepreneurship", "session_id": "fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d", "chat_history": [{"role": "user", "content": "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", "timestamp": "2025-07-20T12:47:10.934Z"}, {"role": "assistant", "content": "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", "timestamp": "2025-07-20T12:47:16.390Z"}, {"role": "user", "content": "Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like \"Super Fun New Board Game!\" and \"Adventure Awaits!\" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I'm not playing right then.", "timestamp": "2025-07-20T12:47:35.601Z"}, {"role": "assistant", "content": "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n\n**Diagnostic Results:**\n\u2705 Questions Completed: 5/5\n\ud83d\udcca Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! \ud83d\ude80\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", "timestamp": "2025-07-20T12:47:41.207Z"}, {"role": "user", "content": "Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!", "timestamp": "2025-07-20T12:48:07.493Z"}, {"role": "assistant", "content": "Fantastic recall, Andrea! You've absolutely nailed it.\n\n*   **Posters** are a visual way to market. They catch people's eyes with colourful pictures and catchy words, just like you described for your board game. They're great for getting attention in a specific place, like a school hallway or a shop window.\n\n*   And **word-of-mouth** is incredibly powerful! When your friends tell their friends how much they love your game, that's word-of-mouth marketing. It's like a personal recommendation, and people tend to trust what their friends say.\n\nThese are just two of the many ways entrepreneurs get their names and products out there.\n\nNow, let's think about *why* these methods work. For posters, what do you think makes a poster really effective in grabbing someone's attention? What elements would make *you* stop and look at it?", "timestamp": "2025-07-20T12:48:13.273Z"}, {"role": "user", "content": "Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and happy colours!** Like really colourful and not just boring brown or grey. That makes my eyes go straight to it. 2.  **A really cool picture!** If it's for a game, I'd want to see excited kids playing, or the fun characters from the game. The picture should tell a story right away. 3.  **Big, easy-to-read words** that say something exciting, like \"NEW!\" or \"SUPER FUN!\" or \"YOU'LL LOVE THIS!\" 4.  **A big, clear name of the thing** it's about. Like \"THE AWESOME BOARD GAME!\" 5.  It needs to look **interesting and not too messy**, so I can understand it quickly without having to read a lot.  It's all about catching my eye really fast and making me curious!", "timestamp": "2025-07-20T12:48:32.471Z"}, {"role": "assistant", "content": "That's a brilliant breakdown, Andrea! You've perfectly described what makes a poster effective for your age group.\n\n*   **Bright colours and exciting pictures** are key to grabbing attention. They make the poster stand out from everything else.\n*   **Clear, catchy words** tell people important information quickly and create excitement.\n*   And ensuring the **name of the product is prominent** helps people remember what they're looking at.\n\nIt's all about making that first impression count!\n\nNow, let's switch gears to **word-of-mouth**. We said it's when people talk to each other. When you hear a friend raving about a new movie or a cool toy, why do you usually believe them more than if you saw an advertisement for it? What's different about hearing it from a friend?", "timestamp": "2025-07-20T12:48:37.905Z"}]}}
2025-07-20 13:48:57,344 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-20 13:48:57,344 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hpOhgKomULZ-A8iPkkN2GomGHglnz2lwZKjouBQ9qlTbXCg0_D11S3C1Bw6np28tANlsf11ThHkiD15-Udh5NfZeV1NX2DOEfdQ6cPzA1z2XMR00lSEc4bcoSFy3oZ4v6Lz8K6eGucSE5HGH3OBgz-jLKAhffPBKfCtkYC-aaRCVS0Ywff5YhbwHMDAdluTKtB-U60GHMtoK3Tk8odKiy4LptkInLVHc_mi87AwJJw3yS3IGfqtSBLQAXMM8VZm9AKSGK-n56NLU-vCacsXcnoE7Qw0s-B5qa_xqiogN4Tnpb61Z8z74Ip3tZFuSY3GkKabnBbEKEDh-nPboyQc0rw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '6554', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-20 13:48:57,344 - INFO - [auth_decorator.py:70] - 🔒 Request ID: d47b82f2-533f-489f-83b9-5241dcc65e30
2025-07-20 13:48:57,344 - INFO - [auth_decorator.py:74] - [d47b82f2-533f-489f-83b9-5241dcc65e30][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-20 13:48:57,344 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:95] - [d47b82f2-533f-489f-83b9-5241dcc65e30][require_auth] Development mode detected - bypassing authentication
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-20 13:48:57,345 - INFO - [auth_decorator.py:121] - [d47b82f2-533f-489f-83b9-5241dcc65e30][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-20 13:48:57,669 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:57,670 - INFO - [auth_decorator.py:164] - [d47b82f2-533f-489f-83b9-5241dcc65e30][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-20 13:48:57,674 - INFO - [main.py:7282] -
================================================================================
2025-07-20 13:48:57,674 - WARNING - [main.py:7283] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-20 13:48:57,675 - WARNING - [main.py:7284] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-20 13:48:57,676 - INFO - [main.py:7289] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-ENT-001","content_to_enhance":"That's a great question! I usually believe my friend more than an advertisement because:  1.  **I trust my friend.** I know them, and I know they wouldn't trick me or just say something is good if it isn't. An advert is made by a company I don't know, and maybe they just want my money. 2.  **My friend isn't trying to sell me anything.** They're just telling me about something they genuinely liked, not because some...
2025-07-20 13:48:57,677 - INFO - [main.py:7291] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-ENT-001', 'content_to_enhance': "That's a great question! I usually believe my friend more than an advertisement because:  1.  **I trust my friend.** I know them, and I know they wouldn't trick me or just say something is good if it isn't. An advert is made by a company I don't know, and maybe they just want my money. 2.  **My friend isn't trying to sell me anything.** They're just telling me about something they genuinely liked, not because someone paid them to say it. 3.  **They know me!** My friend often knows what kind of movies or toys I like, so their recommendation feels like it's specially for *me*, not just for everyone.  It feels more real and honest when it comes from a friend!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'subject': 'Entrepreneurship', 'session_id': 'fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', 'chat_history': [{'role': 'user', 'content': "One way I could tell my classmates about my super fun board game to get them excited is to **bring it to school and play it with my friends during break time in a spot where other kids can see us!**  When they see us laughing, cheering, and having so much fun, they'll get curious and want to know what game it is and how they can play too!", 'timestamp': '2025-07-20T12:47:10.934Z'}, {'role': 'assistant', 'content': "That's a brilliant idea, Andrea! Showing your friends how much fun the game is by playing it is a fantastic way to get others interested. It's a very visual and engaging method.\n\nWe're almost there! Here's the final question for our diagnostic.\n\n**Question 5 of 5 (Level 6 Synthesis):** Besides showing it to friends, can you think of *another* way you might tell people about your new board game that doesn't involve them seeing you play it directly? Think about something you might create or say.", 'timestamp': '2025-07-20T12:47:16.390Z'}, {'role': 'user', 'content': 'Okay, another way I could tell people about my new board game without them seeing me play it directly would be to **make a cool, colourful poster**!  I would draw exciting pictures of the game characters or the board, and write big, catchy words like "Super Fun New Board Game!" and "Adventure Awaits!" I could put it on the school notice board or where lots of kids walk by. That way, they see it and get curious even if I\'m not playing right then.', 'timestamp': '2025-07-20T12:47:35.601Z'}, {'role': 'assistant', 'content': "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n\n**Diagnostic Results:**\n✅ Questions Completed: 5/5\n📊 Recommended Teaching Level: **Level 6**\n\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on Introduction to Marketing! 🚀\n\nToday, we're going to dive deeper into marketing and explore different ways businesses and entrepreneurs like you can get the word out about their amazing products and services.\n\nWe've already talked about how marketing is about telling people what you have and making them want it. Think about it like this: if you have the most delicious cookies in the world, but no one knows about them, you won't sell many, right? Marketing is the bridge that connects your wonderful creation to the people who will love it!\n\nWe've already touched on a couple of great **marketing methods**. Can you remind me of the two we discussed through your answers? One involved people seeing something, and the other involved people talking about something.", 'timestamp': '2025-07-20T12:47:41.207Z'}, {'role': 'user', 'content': 'Okay! I remember those!  One was **posters**, where people see something like a big drawing or words that tell them about the product.  And the other was **word-of-mouth**, where people talk about something they like to their friends and family, and then those people want to try it too!', 'timestamp': '2025-07-20T12:48:07.493Z'}, {'role': 'assistant', 'content': "Fantastic recall, Andrea! You've absolutely nailed it.\n\n*   **Posters** are a visual way to market. They catch people's eyes with colourful pictures and catchy words, just like you described for your board game. They're great for getting attention in a specific place, like a school hallway or a shop window.\n\n*   And **word-of-mouth** is incredibly powerful! When your friends tell their friends how much they love your game, that's word-of-mouth marketing. It's like a personal recommendation, and people tend to trust what their friends say.\n\nThese are just two of the many ways entrepreneurs get their names and products out there.\n\nNow, let's think about *why* these methods work. For posters, what do you think makes a poster really effective in grabbing someone's attention? What elements would make *you* stop and look at it?", 'timestamp': '2025-07-20T12:48:13.273Z'}, {'role': 'user', 'content': 'Okay, for a poster to really work and make *me* stop and look, it needs:  1.  **Super bright and happy colours!** Like really colourful and not just boring brown or grey. That makes my eyes go straight to it. 2.  **A really cool picture!** If it\'s for a game, I\'d want to see excited kids playing, or the fun characters from the game. The picture should tell a story right away. 3.  **Big, easy-to-read words** that say something exciting, like "NEW!" or "SUPER FUN!" or "YOU\'LL LOVE THIS!" 4.  **A big, clear name of the thing** it\'s about. Like "THE AWESOME BOARD GAME!" 5.  It needs to look **interesting and not too messy**, so I can understand it quickly without having to read a lot.  It\'s all about catching my eye really fast and making me curious!', 'timestamp': '2025-07-20T12:48:32.471Z'}, {'role': 'assistant', 'content': "That's a brilliant breakdown, Andrea! You've perfectly described what makes a poster effective for your age group.\n\n*   **Bright colours and exciting pictures** are key to grabbing attention. They make the poster stand out from everything else.\n*   **Clear, catchy words** tell people important information quickly and create excitement.\n*   And ensuring the **name of the product is prominent** helps people remember what they're looking at.\n\nIt's all about making that first impression count!\n\nNow, let's switch gears to **word-of-mouth**. We said it's when people talk to each other. When you hear a friend raving about a new movie or a cool toy, why do you usually believe them more than if you saw an advertisement for it? What's different about hearing it from a friend?", 'timestamp': '2025-07-20T12:48:37.905Z'}]}
2025-07-20 13:48:57,678 - INFO - [main.py:7293] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍  - Session ID from payload: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:48:57,678 - INFO - [main.py:7294] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-20 13:48:57,679 - INFO - [main.py:7295] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍  - Lesson Ref from payload: P5-ENT-001
2025-07-20 13:48:57,680 - DEBUG - [main.py:7331] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:57,680 - INFO - [main.py:7332] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d', lesson_ref='P5-ENT-001'
2025-07-20 13:48:57,681 - INFO - [main.py:7372] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Level not provided, determined from grade 'Primary 5': 5
2025-07-20 13:48:57,953 - INFO - [main.py:6626] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-20 13:48:57,955 - INFO - [main.py:7389] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-ENT-001
2025-07-20 13:48:57,956 - INFO - [main.py:7390] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='Primary 5', level='5', subject='Entrepreneurship'
2025-07-20 13:48:57,957 - DEBUG - [main.py:674] - Cache hit for fetch_lesson_data
2025-07-20 13:48:58,492 - INFO - [main.py:7420] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-20 13:48:58,492 - INFO - [main.py:7483] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-20 13:48:58,493 - INFO - [main.py:7608] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ Successfully retrieved lesson from primary path
2025-07-20 13:48:58,493 - INFO - [main.py:7619] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ All required fields present after lesson content parsing and mapping
2025-07-20 13:48:58,493 - INFO - [main.py:7658] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Attempting to infer module. GS Subject Slug: 'entrepreneurship'.
2025-07-20 13:48:58,493 - INFO - [main.py:4239] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Inferring module for subject 'entrepreneurship', lesson 'Introduction to Marketing'.
2025-07-20 13:48:58,810 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'business_planning_and_finance' ('Business Planning & Finance')
2025-07-20 13:48:58,811 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'digital_technology_and_ai' ('Digital Technology & AI')
2025-07-20 13:48:58,812 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'entrepreneurial_mindset' ('Entrepreneurial Mindset')
2025-07-20 13:48:58,813 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'marketing_and_customer_engagement' ('Marketing & Customer Engagement')
2025-07-20 13:48:58,814 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'opportunity_identification_and_innovation' ('Opportunity Identification & Innovation')
2025-07-20 13:48:58,815 - INFO - [main.py:4305] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Loaded metadata for module 'social_responsibility_and_ethics' ('Social Responsibility & Ethics')
2025-07-20 13:48:58,816 - INFO - [main.py:4374] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Starting module inference for subject 'entrepreneurship' with 6 module options
2025-07-20 13:48:58,817 - DEBUG - [main.py:4388] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Sample modules available (first 3):
- business_planning_and_finance: Business Planning & Finance
- digital_technology_and_ai: Digital Technology & AI
- entrepreneurial_mindset: Entrepreneurial Mindset
2025-07-20 13:48:58,818 - DEBUG - [main.py:4391] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction...
2025-07-20 13:48:58,819 - DEBUG - [main.py:4392] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference Lesson Summary (first 300 chars): Lesson Title: Introduction to Marketing. Topic: Introduction to Marketing. Learning Objectives: Understand the purpose of marketing.; Identify marketing methods (posters, word-of-mouth).. Key Concepts: purpose; marketing; methods; posters; word; mouth; Warm; students; name; different. Introduction: ...
2025-07-20 13:48:58,820 - DEBUG - [main.py:4393] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference Module Options (first 200 chars): 1. Slug: "business_planning_and_finance", Name: "Business Planning & Finance", Description: "From lemonade-stand budgeting to full financial projections, funding and AI-driven analytics...."
2. Slug: ...
2025-07-20 13:48:58,821 - INFO - [main.py:4397] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Calling Gemini API for module inference...
2025-07-20 13:48:59,388 - INFO - [main.py:4407] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Gemini API call completed in 0.57s. Raw response: 'marketing_and_customer_engagement'
2025-07-20 13:48:59,388 - DEBUG - [main.py:4429] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Cleaned slug: 'marketing_and_customer_engagement'
2025-07-20 13:48:59,389 - INFO - [main.py:4434] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI Inference: Successfully matched module by slug. Chosen: 'marketing_and_customer_engagement' (Marketing & Customer Engagement)
2025-07-20 13:48:59,389 - INFO - [main.py:7692] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Successfully inferred module ID via AI: marketing_and_customer_engagement
2025-07-20 13:48:59,390 - INFO - [main.py:7729] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Effective module name for prompt context: 'Marketing & Customer Engagement' (Module ID: marketing_and_customer_engagement)
2025-07-20 13:48:59,671 - INFO - [main.py:3954] - No prior student performance document found for Topic: entrepreneurship_Primary 5_entrepreneurship_marketing_and_customer_engagement
2025-07-20 13:49:00,165 - DEBUG - [main.py:7782] - 🔍 SESSION STATE RETRIEVAL:
2025-07-20 13:49:00,166 - DEBUG - [main.py:7783] - 🔍   - Session ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:49:00,167 - DEBUG - [main.py:7784] - 🔍   - Document Exists: True
2025-07-20 13:49:00,167 - DEBUG - [main.py:7785] - 🔍   - Current Phase: teaching
2025-07-20 13:49:00,167 - DEBUG - [main.py:7786] - 🔍   - Probing Level: 6
2025-07-20 13:49:00,168 - DEBUG - [main.py:7787] - 🔍   - Question Index: 0
2025-07-20 13:49:00,168 - WARNING - [main.py:7793] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 SESSION STATE DEBUG:
2025-07-20 13:49:00,168 - WARNING - [main.py:7794] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   - Session exists: True
2025-07-20 13:49:00,169 - WARNING - [main.py:7795] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   - Current phase: teaching
2025-07-20 13:49:00,169 - WARNING - [main.py:7796] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_probing_level_number', 'quiz_performance', 'current_phase', 'is_first_encounter_for_module', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'quiz_started', 'quiz_answers', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'current_session_working_level', 'teaching_interactions']
2025-07-20 13:49:00,170 - DEBUG - [main.py:7814] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-20 13:49:00,171 - DEBUG - [main.py:7815] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Retrieved Phase: 'teaching'
2025-07-20 13:49:00,171 - DEBUG - [main.py:7816] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Diagnostic Completed: False
2025-07-20 13:49:00,172 - DEBUG - [main.py:7817] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Assigned Level: None
2025-07-20 13:49:00,173 - WARNING - [main.py:7818] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔒 STATE PROTECTION: phase='teaching', diagnostic_done=False, level=None
2025-07-20 13:49:00,174 - INFO - [main.py:7850] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-20 13:49:00,174 - INFO - [main.py:7851] - [d47b82f2-533f-489f-83b9-5241dcc65e30] State protection not triggered
2025-07-20 13:49:00,175 - INFO - [main.py:7899] - [d47b82f2-533f-489f-83b9-5241dcc65e30]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-20 13:49:00,175 - INFO - [main.py:7900] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   assigned_level_for_teaching (session): None
2025-07-20 13:49:00,176 - INFO - [main.py:7901] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   latest_assessed_level (profile): None
2025-07-20 13:49:00,176 - INFO - [main.py:7902] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   teaching_level_for_returning_student: None
2025-07-20 13:49:00,177 - INFO - [main.py:7903] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   has_completed_diagnostic_before: False
2025-07-20 13:49:00,177 - INFO - [main.py:7904] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   is_first_encounter_for_module: True
2025-07-20 13:49:00,177 - WARNING - [main.py:7909] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-20 13:49:00,178 - INFO - [main.py:7915] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 PHASE INVESTIGATION:
2025-07-20 13:49:00,178 - INFO - [main.py:7916] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Retrieved from Firestore: 'teaching'
2025-07-20 13:49:00,178 - INFO - [main.py:7917] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-20 13:49:00,179 - INFO - [main.py:7918] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Is first encounter: True
2025-07-20 13:49:00,179 - INFO - [main.py:7919] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Diagnostic completed: False
2025-07-20 13:49:00,179 - WARNING - [main.py:7927] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ⚠️ Invalid phase format: 'teaching', using default
2025-07-20 13:49:00,180 - INFO - [main.py:7939] - [d47b82f2-533f-489f-83b9-5241dcc65e30] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-20 13:49:00,180 - INFO - [main.py:7941] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Final phase for AI logic: smart_diagnostic_start
2025-07-20 13:49:00,181 - INFO - [main.py:3900] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='Primary 5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-20 13:49:00,181 - INFO - [main.py:3616] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='Primary 5'
2025-07-20 13:49:00,181 - INFO - [main.py:3625] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY 5'
2025-07-20 13:49:00,182 - INFO - [main.py:3688] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY 5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-20 13:49:00,182 - INFO - [main.py:3908] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade Primary 5
2025-07-20 13:49:00,182 - INFO - [main.py:7961] - [d47b82f2-533f-489f-83b9-5241dcc65e30] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-20 13:49:00,183 - INFO - [main.py:5858] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Diagnostic context validation passed
2025-07-20 13:49:00,183 - WARNING - [main.py:5920] - DETERMINE_PHASE: Unclear state but preserving current phase 'teaching' to prevent backward transition
2025-07-20 13:49:00,183 - WARNING - [main.py:8101] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'teaching' for first encounter
2025-07-20 13:49:00,184 - INFO - [main.py:8124] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Skipping diagnostic context enhancement for non-diagnostic phase: teaching
2025-07-20 13:49:00,184 - DEBUG - [main.py:8133] - 🧪 DEBUG PHASE: current_phase_for_ai = 'teaching'
2025-07-20 13:49:00,185 - DEBUG - [main.py:8134] - 🧪 DEBUG PHASE: determined_phase = 'teaching'
2025-07-20 13:49:00,186 - INFO - [main.py:8136] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Robust context prepared successfully. Phase: teaching
2025-07-20 13:49:00,186 - DEBUG - [main.py:8137] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:49:00,187 - WARNING - [main.py:8360] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖 AI PROMPT GENERATION:
2025-07-20 13:49:00,187 - WARNING - [main.py:8361] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - Current phase: teaching
2025-07-20 13:49:00,188 - WARNING - [main.py:8362] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - Student query: That's a great question! I usually believe my friend more than an advertisement because:  1.  **I tr...
2025-07-20 13:49:00,188 - WARNING - [main.py:8363] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-20 13:49:00,189 - DEBUG - [main.py:8366] - 🤖 GENERATING AI PROMPT:
2025-07-20 13:49:00,189 - DEBUG - [main.py:8367] - 🤖   Phase: teaching
2025-07-20 13:49:00,190 - DEBUG - [main.py:8368] - 🤖   Query: That's a great question! I usually believe my frie...
2025-07-20 13:49:00,190 - DEBUG - [main.py:8369] - 🤖   Student: Andrea
2025-07-20 13:49:00,191 - INFO - [main.py:10008] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:49:00,191 - INFO - [main.py:10041] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📤 Sending message to existing session: That's a great question! I usually believe my frie...
2025-07-20 13:49:00,191 - INFO - [main.py:5679] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💰 Sending message to existing session (NO API CALL)
2025-07-20 13:49:01,162 - INFO - [main.py:5684] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ Response received from session (NO API CALL COST)
2025-07-20 13:49:01,163 - INFO - [main.py:10048] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📥 Received response from session: Absolutely spot on, Andrea! You've perfectly captured why word-of-mouth is so powerful.

*   **Trust...
2025-07-20 13:49:01,164 - INFO - [main.py:10177] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📋 LOGIC-BASED STATE EXTRACTION: No text patterns, relying on structured data only
2025-07-20 13:49:01,166 - WARNING - [main.py:8391] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖 AI RESPONSE RECEIVED:
2025-07-20 13:49:01,167 - WARNING - [main.py:8392] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - Content length: 861 chars
2025-07-20 13:49:01,168 - WARNING - [main.py:8393] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - State updates: {'new_phase': 'teaching'}
2025-07-20 13:49:01,169 - WARNING - [main.py:8394] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🤖   - Raw state block: None...
2025-07-20 13:49:01,170 - INFO - [main.py:8402] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🎓 APPLYING TEACHING PHASE ENHANCEMENT
2025-07-20 13:49:01,172 - DEBUG - [teaching_phase_enhancement.py:212] - FAST content depth analysis: 0.36 (length: 861)
2025-07-20 13:49:01,179 - DEBUG - [teaching_phase_enhancement.py:300] - Content level analysis: estimated level 2
2025-07-20 13:49:01,181 - WARNING - [teaching_phase_enhancement.py:327] - Level consistency validation failed: analyzed level 2 vs expected 6
2025-07-20 13:49:01,184 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_1': 0.39
2025-07-20 13:49:01,185 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_1' coverage: 0.25
2025-07-20 13:49:01,187 - DEBUG - [teaching_phase_enhancement.py:102] - Content coverage analysis for objective 'objective_2': 0.44
2025-07-20 13:49:01,188 - INFO - [teaching_phase_enhancement.py:126] - Updated objective 'objective_2' coverage: 0.38
2025-07-20 13:49:01,189 - INFO - [teaching_phase_enhancement.py:243] - Teaching depth validation failed: insufficient objective coverage (0.00 < 0.8)
2025-07-20 13:49:01,190 - INFO - [teaching_phase_enhancement.py:503] - Teaching interaction processed for fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d: 0/2 objectives covered, depth 0.36
2025-07-20 13:49:01,191 - ERROR - [main.py:8785] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ❌ Teaching phase enhancement error: name 'lesson_phase_from_context' is not defined
2025-07-20 13:49:01,192 - DEBUG - [main.py:8789] - 🤖 AI RESPONSE PROCESSED:
2025-07-20 13:49:01,192 - DEBUG - [main.py:8790] - 🤖   Content: Absolutely spot on, Andrea! You've perfectly captured why word-of-mouth is so powerful.

*   **Trust...
2025-07-20 13:49:01,193 - DEBUG - [main.py:8791] - 🤖   State: {'new_phase': 'teaching', 'teaching_interactions': 3, 'teaching_depth_score': 0.47200000000000003, 'objectives_covered': 0, 'coverage_percentage': 0.0, 'teaching_level_consistent': False}
2025-07-20 13:49:01,194 - INFO - [main.py:8817] - [d47b82f2-533f-489f-83b9-5241dcc65e30] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-20 13:49:01,195 - INFO - [main.py:8818] - [d47b82f2-533f-489f-83b9-5241dcc65e30] CURRENT PHASE DETERMINATION: AI=teaching, Session=teaching, Final=teaching
2025-07-20 13:49:01,492 - WARNING - [main.py:8907] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 STATE UPDATE VALIDATION: current_phase='teaching', new_phase='teaching'
2025-07-20 13:49:01,493 - INFO - [main.py:6099] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI state update validation passed: teaching → teaching
2025-07-20 13:49:01,494 - WARNING - [main.py:8916] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ STATE UPDATE VALIDATION PASSED
2025-07-20 13:49:01,495 - WARNING - [main.py:8937] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   PHASE MAINTAINED: teaching
2025-07-20 13:49:01,496 - INFO - [main.py:8946] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-20 13:49:01,497 - INFO - [phase_transition_integrity.py:328] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔄 APPLYING TRANSITION WITH INTEGRITY: teaching → teaching
2025-07-20 13:49:01,498 - INFO - [phase_transition_integrity.py:291] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📸 DATA SNAPSHOT CREATED: Session fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase teaching
2025-07-20 13:49:01,499 - INFO - [phase_transition_integrity.py:153] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-20 13:49:01,500 - ERROR - [phase_transition_integrity.py:713] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-20 13:49:01,500 - ERROR - [phase_transition_integrity.py:715] - 🔍   1. Cannot enter teaching phase without completing diagnostic
2025-07-20 13:49:01,501 - ERROR - [phase_transition_integrity.py:716] - 🔍 SESSION DATA DEBUG:
2025-07-20 13:49:01,502 - ERROR - [phase_transition_integrity.py:717] - 🔍   - diagnostic_complete: False
2025-07-20 13:49:01,502 - ERROR - [phase_transition_integrity.py:718] - 🔍   - assigned_level_for_teaching: 6
2025-07-20 13:49:01,503 - ERROR - [phase_transition_integrity.py:719] - 🔍   - teaching_complete: False
2025-07-20 13:49:01,504 - ERROR - [phase_transition_integrity.py:720] - 🔍   - teaching_interactions: 3
2025-07-20 13:49:01,504 - ERROR - [phase_transition_integrity.py:721] - 🔍   - from_phase: teaching, to_phase: teaching
2025-07-20 13:49:01,505 - ERROR - [phase_transition_integrity.py:735] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 STATE CORRECTION ATTEMPT: teaching → teaching
2025-07-20 13:49:01,506 - ERROR - [phase_transition_integrity.py:742] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 CORRECTION CHECK: diagnostic_complete=False, assigned_level=6
2025-07-20 13:49:01,507 - ERROR - [phase_transition_integrity.py:748] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 APPLYING CORRECTION: Setting diagnostic_complete=True
2025-07-20 13:49:01,508 - ERROR - [phase_transition_integrity.py:775] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 CORRECTION RESULT: {'corrected': True, 'corrected_phase': 'teaching', 'corrected_data': {'diagnostic_complete': True}, 'message': 'Corrected diagnostic_complete to True based on assigned level'}
2025-07-20 13:49:01,509 - WARNING - [phase_transition_integrity.py:209] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 STATE CORRECTION APPLIED: Corrected diagnostic_complete to True based on assigned level
2025-07-20 13:49:01,510 - WARNING - [phase_transition_integrity.py:365] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 TRANSITION CORRECTED: teaching → teaching
2025-07-20 13:49:01,511 - DEBUG - [phase_transition_integrity.py:841] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-20 13:49:01,512 - DEBUG - [phase_transition_integrity.py:874] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 📝 TRANSITION RECORDED: teaching → teaching (corrected)
2025-07-20 13:49:01,513 - INFO - [phase_transition_integrity.py:404] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching
2025-07-20 13:49:01,514 - WARNING - [main.py:8991] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 TRANSITION CORRECTED: teaching → teaching
2025-07-20 13:49:01,515 - WARNING - [main.py:8998] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-20 13:49:01,517 - WARNING - [main.py:8999] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   1. Input phase: 'teaching'
2025-07-20 13:49:01,518 - WARNING - [main.py:9000] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-20 13:49:01,519 - WARNING - [main.py:9001] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   3. Proposed phase: 'teaching'
2025-07-20 13:49:01,521 - WARNING - [main.py:9002] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   4. Integrity validation result: 'corrected'
2025-07-20 13:49:01,522 - WARNING - [main.py:9003] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍   5. Final phase to save: 'teaching'
2025-07-20 13:49:01,523 - WARNING - [main.py:9006] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾 FINAL STATE APPLICATION:
2025-07-20 13:49:01,524 - WARNING - [main.py:9007] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾   - Current phase input: 'teaching'
2025-07-20 13:49:01,525 - WARNING - [main.py:9008] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾   - Validated state updates: 28 fields
2025-07-20 13:49:01,526 - WARNING - [main.py:9009] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾   - Final phase to save: 'teaching'
2025-07-20 13:49:01,527 - WARNING - [main.py:9010] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾   - Phase change: False
2025-07-20 13:49:01,528 - WARNING - [main.py:9011] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 💾   - Integrity applied: True
2025-07-20 13:49:01,529 - INFO - [main.py:6131] - [d47b82f2-533f-489f-83b9-5241dcc65e30] DIAGNOSTIC_FLOW_METRICS:
2025-07-20 13:49:01,530 - INFO - [main.py:6132] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Phase transition: teaching -> teaching
2025-07-20 13:49:01,531 - INFO - [main.py:6133] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Current level: 6
2025-07-20 13:49:01,532 - INFO - [main.py:6134] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Question index: 0
2025-07-20 13:49:01,533 - INFO - [main.py:6135] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   First encounter: True
2025-07-20 13:49:01,534 - INFO - [main.py:6140] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Answers collected: 0
2025-07-20 13:49:01,535 - INFO - [main.py:6141] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Levels failed: 0
2025-07-20 13:49:01,535 - INFO - [main.py:6099] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI state update validation passed: teaching → teaching
2025-07-20 13:49:01,536 - INFO - [main.py:6145] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   State update valid: True
2025-07-20 13:49:01,537 - INFO - [main.py:6152] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Diagnostic complete: False
2025-07-20 13:49:01,537 - INFO - [main.py:6154] - [d47b82f2-533f-489f-83b9-5241dcc65e30]   Assigned level: 6
2025-07-20 13:49:01,539 - WARNING - [main.py:9024] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 PROBING LEVEL SYNC: Set probing level to 6 to match assigned teaching level
2025-07-20 13:49:01,540 - WARNING - [main.py:9029] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-20 13:49:01,541 - INFO - [main.py:9037] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 DEBUG state_updates_from_ai teaching_interactions: 3
2025-07-20 13:49:01,542 - INFO - [main.py:9038] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔍 DEBUG original teaching_interactions: 2
2025-07-20 13:49:02,058 - WARNING - [main.py:9083] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-20 13:49:02,059 - WARNING - [main.py:9084] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Phase: teaching
2025-07-20 13:49:02,060 - WARNING - [main.py:9085] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Probing Level: 6
2025-07-20 13:49:02,062 - WARNING - [main.py:9086] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Question Index: 0
2025-07-20 13:49:02,063 - WARNING - [main.py:9087] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Diagnostic Complete: False
2025-07-20 13:49:02,064 - WARNING - [main.py:9094] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Quiz Questions Saved: 0
2025-07-20 13:49:02,065 - WARNING - [main.py:9095] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Quiz Answers Saved: 0
2025-07-20 13:49:02,066 - WARNING - [main.py:9096] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅   - Quiz Started: False
2025-07-20 13:49:02,068 - DEBUG - [main.py:9145] - 🔥 STATE SAVED - Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:49:02,069 - DEBUG - [main.py:9146] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-20 13:49:02,862 - DEBUG - [main.py:9204] - ✅ SESSION UPDATED - ID: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d, Phase: teaching
2025-07-20 13:49:02,864 - DEBUG - [main.py:9205] - ✅ INTERACTION LOGGED - Phase: teaching → teaching
2025-07-20 13:49:02,865 - INFO - [main.py:9211] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ Updated existing session document: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:49:02,866 - INFO - [main.py:16959] - [d47b82f2-533f-489f-83b9-5241dcc65e30] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-20 13:49:02,867 - DEBUG - [main.py:4726] - [d47b82f2-533f-489f-83b9-5241dcc65e30] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-20 13:49:02,868 - DEBUG - [main.py:9298] - [d47b82f2-533f-489f-83b9-5241dcc65e30] No final assessment data found in AI response
2025-07-20 13:49:02,869 - DEBUG - [main.py:9321] - [d47b82f2-533f-489f-83b9-5241dcc65e30] No lesson completion detected (Phase: teaching, Complete: False)
2025-07-20 13:49:02,870 - WARNING - [main.py:9340] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching'.
2025-07-20 13:49:02,871 - DEBUG - [main.py:9345] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-20 13:49:02,873 - DEBUG - [main.py:9346] - 🔒   Current Phase: teaching
2025-07-20 13:49:02,874 - DEBUG - [main.py:9347] - 🔒   Final Phase: teaching
2025-07-20 13:49:02,875 - DEBUG - [main.py:9348] - 🔒   Diagnostic Complete: True
2025-07-20 13:49:02,876 - DEBUG - [main.py:9349] - 🔒   Assigned Level: 6
2025-07-20 13:49:02,877 - INFO - [main.py:9422] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-20 13:49:02,878 - INFO - [main.py:9456] - [d47b82f2-533f-489f-83b9-5241dcc65e30] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-20 13:49:02,879 - INFO - [main.py:9464] - [d47b82f2-533f-489f-83b9-5241dcc65e30] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-20 13:49:02,881 - DEBUG - [main.py:9501] - 🎯 RESPONSE READY:
2025-07-20 13:49:02,881 - DEBUG - [main.py:9502] - 🎯   Session: fallback-1bc4ad32-ae0e-4150-afde-55145be6c96d
2025-07-20 13:49:02,882 - DEBUG - [main.py:9503] - 🎯   Phase: teaching → teaching
2025-07-20 13:49:02,883 - DEBUG - [main.py:9504] - 🎯   Content: Absolutely spot on, Andrea! You've perfectly captu...
2025-07-20 13:49:02,884 - DEBUG - [main.py:9505] - 🎯   Request ID: d47b82f2-533f-489f-83b9-5241dcc65e30
2025-07-20 13:49:02,884 - INFO - [main.py:9511] - [d47b82f2-533f-489f-83b9-5241dcc65e30] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-20 13:49:02,885 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,885 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,886 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,887 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:49:02,888 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:49:02,888 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:49:02,888 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:49:02,889 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,889 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-20 13:49:02,889 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,890 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,890 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,891 - DEBUG - [main.py:22114] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-20 13:49:02,891 - WARNING - [main.py:703] - High response time detected: 5.22s for enhance_content_api
