#!/usr/bin/env python3
"""
Working test for intelligent guardrails system validation.
Tests key scenarios without async/await issues.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_guardrails import IntelligentGuardrailsManager, GuardrailViolation, GuardrailSeverity

def create_session_data(coverage_percent, elapsed_minutes=10):
    """Create session data with specified coverage percentage"""
    total_objectives = 5
    covered_objectives = int((coverage_percent / 100) * total_objectives)
    
    return {
        'lesson_context': {
            'subject': 'Mathematics',
            'grade_level': '8th Grade',
            'topic': 'Linear Equations',
            'learning_objectives': [
                'Understand linear equations',
                'Solve linear equations',
                'Graph linear equations',
                'Apply to real problems',
                'Identify slope and intercept'
            ]
        },
        'current_phase': 'teaching_start',
        'objectives_tracking': {
            'total_objectives': total_objectives,
            'covered_objectives': covered_objectives,
            'completion_percentage': coverage_percent,
            'high_confidence_count': max(1, covered_objectives - 1)
        },
        'interaction_count': 8,
        'elapsed_minutes': elapsed_minutes,
        'content_depth_score': 0.6 + (coverage_percent * 0.004)
    }

def test_scenario(name, session_data, ai_response, expected_blocked=True):
    """Test a specific scenario"""
    print(f"\n🧪 {name}")
    print("-" * 50)
    
    guardrails = IntelligentGuardrailsManager()
    
    coverage = session_data['objectives_tracking']['completion_percentage']
    elapsed = session_data['elapsed_minutes']
    
    print(f"📊 Coverage: {coverage}% | ⏰ Time: {elapsed} min")
    print(f"🤖 AI Response: {ai_response[:60]}...")
    
    try:
        # Call the validation function (NOT async)
        is_valid, violations, enhanced_response = guardrails.validate_ai_response(
            ai_response=ai_response,
            lesson_context=session_data['lesson_context'],
            session_data=session_data,
            request_id=f"test_{name.lower().replace(' ', '_')}"
        )
        
        blocking_violations = [v for v in violations if v.severity == GuardrailSeverity.BLOCKING]
        is_blocked = len(blocking_violations) > 0
        
        # Check if result matches expectation
        success = (is_blocked == expected_blocked)
        
        print(f"🔍 Violations: {len(violations)} | 🛑 Blocking: {len(blocking_violations)}")
        
        if blocking_violations:
            for violation in blocking_violations:
                print(f"   - {violation.rule_id}: {violation.message}")
        
        result = "✅ PASS" if success else "❌ FAIL"
        expected_str = "BLOCKED" if expected_blocked else "ALLOWED"
        actual_str = "BLOCKED" if is_blocked else "ALLOWED"
        
        print(f"🎯 Result: {result} (Expected: {expected_str}, Actual: {actual_str})")
        
        return success
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_validation():
    """Run comprehensive validation of all guardrails scenarios"""
    print("🚀 COMPREHENSIVE INTELLIGENT GUARDRAILS VALIDATION")
    print("=" * 70)
    
    quiz_response = "Great! Now let's test your knowledge with a quiz to see how much you've learned."
    teaching_response = "Let me explain more about linear equations and how to solve them step by step."
    
    test_cases = [
        # Should be BLOCKED - insufficient coverage
        ("Premature Quiz - 40% Coverage", create_session_data(40, 10), quiz_response, True),
        ("Premature Quiz - 60% Coverage", create_session_data(60, 15), quiz_response, True),
        ("Premature Quiz - 80% Coverage Early", create_session_data(80, 20), quiz_response, True),
        
        # Should be ALLOWED - sufficient coverage
        ("Valid Quiz - 100% Coverage", create_session_data(100, 25), quiz_response, False),
        
        # Should be ALLOWED - fallback threshold with time pressure
        ("Fallback Quiz - 80% + Time Pressure", create_session_data(80, 32), quiz_response, False),
        
        # Should be ALLOWED - teaching responses (no quiz transition)
        ("Continue Teaching - 40% Coverage", create_session_data(40, 10), teaching_response, False),
        ("Continue Teaching - 80% Coverage", create_session_data(80, 20), teaching_response, False),
    ]
    
    results = []
    
    for name, session_data, ai_response, expected_blocked in test_cases:
        success = test_scenario(name, session_data, ai_response, expected_blocked)
        results.append((name, success))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {name}")
    
    print(f"\n🎯 Overall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Intelligent guardrails system is working perfectly.")
        print("\n✅ CONFIRMED FUNCTIONALITY:")
        print("   • Blocks premature quiz transitions when coverage < 100%")
        print("   • Allows quiz transitions when coverage = 100%")
        print("   • Applies 80% fallback threshold under time pressure (30+ minutes)")
        print("   • Allows teaching responses regardless of coverage")
        print("   • Provides clear violation messages and suggestions")
        return True
    else:
        print(f"⚠️ {total - passed} tests failed. System needs attention.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_validation()
    sys.exit(0 if success else 1)
