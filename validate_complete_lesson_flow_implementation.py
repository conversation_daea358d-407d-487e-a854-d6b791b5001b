#!/usr/bin/env python3
"""
Validate that the complete lesson flow implementation has all necessary components
for proper phase transitions, teaching rules, and report generation.
"""

import re
import os

def validate_lesson_flow_implementation():
    """
    Validate the complete lesson flow implementation by checking code.
    """
    
    print("🔍 VALIDATING COMPLETE LESSON FLOW IMPLEMENTATION")
    print("=" * 60)
    
    main_py_path = "backend/cloud_function/lesson_manager/main.py"
    
    if not os.path.exists(main_py_path):
        print(f"❌ Cannot find {main_py_path}")
        return False
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        validations = []
        
        # 1. Check teaching_start → teaching auto-progression
        print("\n🔍 1. TEACHING_START AUTO-PROGRESSION")
        teaching_progression_pattern = r"if current_phase_for_ai == 'teaching_start' and \(not new_phase or new_phase == 'teaching_start'\):"
        if re.search(teaching_progression_pattern, content):
            print("✅ Teaching_start auto-progression logic implemented")
            validations.append(True)
        else:
            print("❌ Teaching_start auto-progression logic missing")
            validations.append(False)
        
        # 2. Check new_phase=None safety fixes
        print("\n🔍 2. NEW_PHASE=NONE SAFETY FIXES")
        safety_checks = [
            r"# CRITICAL FIX: Always initialize new_phase to current_phase to prevent None",
            r"if state_updates_from_ai\.get\('new_phase'\) is None:",
            r"if new_phase is None:"
        ]
        
        safety_fixes_found = 0
        for pattern in safety_checks:
            if re.search(pattern, content):
                safety_fixes_found += 1
        
        if safety_fixes_found >= 2:
            print(f"✅ New_phase=None safety fixes implemented ({safety_fixes_found}/3)")
            validations.append(True)
        else:
            print(f"❌ Insufficient new_phase=None safety fixes ({safety_fixes_found}/3)")
            validations.append(False)
        
        # 3. Check teaching phase enhancement integration
        print("\n🔍 3. TEACHING PHASE ENHANCEMENT")
        teaching_enhancement_patterns = [
            r"APPLYING TEACHING PHASE ENHANCEMENT",
            r"teaching_phase_enhancement",
            r"objectives_covered",
            r"teaching_depth_score",
            r"teaching_interactions"
        ]
        
        enhancement_features = 0
        for pattern in teaching_enhancement_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                enhancement_features += 1
        
        if enhancement_features >= 4:
            print(f"✅ Teaching phase enhancement integrated ({enhancement_features}/5)")
            validations.append(True)
        else:
            print(f"❌ Teaching phase enhancement incomplete ({enhancement_features}/5)")
            validations.append(False)
        
        # 4. Check teaching completion criteria
        print("\n🔍 4. TEACHING COMPLETION CRITERIA")
        completion_criteria_patterns = [
            r"teaching_complete",
            r"objectives_coverage",
            r"content_depth_sufficient",
            r"interactions_sufficient",
            r"minimum.*interactions"
        ]
        
        criteria_found = 0
        for pattern in completion_criteria_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                criteria_found += 1
        
        if criteria_found >= 4:
            print(f"✅ Teaching completion criteria implemented ({criteria_found}/5)")
            validations.append(True)
        else:
            print(f"❌ Teaching completion criteria incomplete ({criteria_found}/5)")
            validations.append(False)
        
        # 5. Check quiz triggering logic
        print("\n🔍 5. QUIZ TRIGGERING LOGIC")
        quiz_trigger_patterns = [
            r"quiz_initiate",
            r"teaching_truly_complete",
            r"QUIZ TRANSITION BLOCKED",
            r"premature.*quiz",
            r"37\.5.*min"
        ]
        
        quiz_logic_found = 0
        for pattern in quiz_trigger_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                quiz_logic_found += 1
        
        if quiz_logic_found >= 3:
            print(f"✅ Quiz triggering logic implemented ({quiz_logic_found}/5)")
            validations.append(True)
        else:
            print(f"❌ Quiz triggering logic incomplete ({quiz_logic_found}/5)")
            validations.append(False)
        
        # 6. Check phase transition integrity system
        print("\n🔍 6. PHASE TRANSITION INTEGRITY")
        integrity_patterns = [
            r"PHASE TRANSITION INTEGRITY SYSTEM",
            r"phase_transition_integrity",
            r"TRANSITION VALIDATED",
            r"CRITICAL DATA PRESERVED"
        ]
        
        integrity_features = 0
        for pattern in integrity_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                integrity_features += 1
        
        if integrity_features >= 3:
            print(f"✅ Phase transition integrity system active ({integrity_features}/4)")
            validations.append(True)
        else:
            print(f"❌ Phase transition integrity system incomplete ({integrity_features}/4)")
            validations.append(False)
        
        # 7. Check lesson completion and reporting
        print("\n🔍 7. LESSON COMPLETION & REPORTING")
        reporting_patterns = [
            r"lesson_complete",
            r"FINAL_ASSESSMENT_BLOCK",
            r"lesson_sessions",
            r"completion_time",
            r"performance_summary"
        ]
        
        reporting_features = 0
        for pattern in reporting_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                reporting_features += 1
        
        if reporting_features >= 3:
            print(f"✅ Lesson completion & reporting implemented ({reporting_features}/5)")
            validations.append(True)
        else:
            print(f"❌ Lesson completion & reporting incomplete ({reporting_features}/5)")
            validations.append(False)
        
        # 8. Check diagnostic flow fixes
        print("\n🔍 8. DIAGNOSTIC FLOW FIXES")
        diagnostic_patterns = [
            r"smart_diagnostic",
            r"diagnostic_complete",
            r"assigned_level_for_teaching",
            r"diagnostic_completed_this_session"
        ]
        
        diagnostic_features = 0
        for pattern in diagnostic_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                diagnostic_features += 1
        
        if diagnostic_features >= 3:
            print(f"✅ Diagnostic flow fixes implemented ({diagnostic_features}/4)")
            validations.append(True)
        else:
            print(f"❌ Diagnostic flow fixes incomplete ({diagnostic_features}/4)")
            validations.append(False)
        
        # Summary
        print(f"\n📊 VALIDATION SUMMARY")
        print("=" * 40)
        
        passed_validations = sum(validations)
        total_validations = len(validations)
        
        validation_items = [
            "Teaching_start auto-progression",
            "New_phase=None safety fixes", 
            "Teaching phase enhancement",
            "Teaching completion criteria",
            "Quiz triggering logic",
            "Phase transition integrity",
            "Lesson completion & reporting",
            "Diagnostic flow fixes"
        ]
        
        for i, (item, passed) in enumerate(zip(validation_items, validations)):
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {i+1}. {item:<30} {status}")
        
        success_rate = passed_validations / total_validations
        print(f"\n🎯 Overall Success Rate: {passed_validations}/{total_validations} ({success_rate:.1%})")
        
        if success_rate >= 0.875:  # 7/8 or better
            print("🎉 IMPLEMENTATION VALIDATION PASSED!")
            print("✅ All critical components are properly implemented")
            return True
        else:
            print("⚠️ IMPLEMENTATION VALIDATION NEEDS ATTENTION")
            print("❌ Some critical components may be missing or incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Error validating implementation: {e}")
        return False

def check_supporting_files():
    """Check for supporting files and modules."""
    print("\n🔍 CHECKING SUPPORTING FILES")
    print("=" * 40)
    
    files_to_check = [
        "backend/cloud_function/lesson_manager/teaching_phase_enhancement.py",
        "backend/cloud_function/lesson_manager/phase_transition_integrity.py",
        "backend/cloud_function/lesson_manager/monitoring_integration.py"
    ]
    
    files_found = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {os.path.basename(file_path)} exists")
            files_found += 1
        else:
            print(f"❌ {os.path.basename(file_path)} missing")
    
    print(f"\n📊 Supporting Files: {files_found}/{len(files_to_check)} found")
    return files_found >= 2  # At least 2/3 files should exist

def validate_lesson_flow_rules():
    """Validate specific lesson flow rules in the code."""
    print("\n🔍 VALIDATING LESSON FLOW RULES")
    print("=" * 40)
    
    main_py_path = "backend/cloud_function/lesson_manager/main.py"
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        rules_validated = []
        
        # Rule 1: Minimum teaching interactions (10)
        min_interactions_pattern = r"(10|ten).*interactions"
        if re.search(min_interactions_pattern, content, re.IGNORECASE):
            print("✅ Minimum teaching interactions rule (10) found")
            rules_validated.append(True)
        else:
            print("❌ Minimum teaching interactions rule not found")
            rules_validated.append(False)
        
        # Rule 2: 37.5 minute quiz trigger
        time_trigger_pattern = r"37\.5.*min"
        if re.search(time_trigger_pattern, content, re.IGNORECASE):
            print("✅ 37.5 minute quiz trigger rule found")
            rules_validated.append(True)
        else:
            print("❌ 37.5 minute quiz trigger rule not found")
            rules_validated.append(False)
        
        # Rule 3: Objectives coverage requirement
        objectives_pattern = r"objectives.*coverage|coverage.*objectives"
        if re.search(objectives_pattern, content, re.IGNORECASE):
            print("✅ Objectives coverage requirement found")
            rules_validated.append(True)
        else:
            print("❌ Objectives coverage requirement not found")
            rules_validated.append(False)
        
        # Rule 4: Content depth requirement
        depth_pattern = r"content.*depth|depth.*content"
        if re.search(depth_pattern, content, re.IGNORECASE):
            print("✅ Content depth requirement found")
            rules_validated.append(True)
        else:
            print("❌ Content depth requirement not found")
            rules_validated.append(False)
        
        rules_passed = sum(rules_validated)
        total_rules = len(rules_validated)
        
        print(f"\n📊 Lesson Flow Rules: {rules_passed}/{total_rules} validated")
        return rules_passed >= 3  # At least 3/4 rules should be found
        
    except Exception as e:
        print(f"❌ Error validating rules: {e}")
        return False

if __name__ == "__main__":
    print("🔧 COMPLETE LESSON FLOW IMPLEMENTATION VALIDATION")
    print("Checking: Phase transitions, teaching rules, reporting, safety fixes")
    print("=" * 70)
    
    # Run validations
    implementation_valid = validate_lesson_flow_implementation()
    supporting_files_ok = check_supporting_files()
    rules_valid = validate_lesson_flow_rules()
    
    # Overall assessment
    print("\n" + "=" * 70)
    print("🎯 FINAL ASSESSMENT")
    
    assessments = [
        ("Core Implementation", implementation_valid),
        ("Supporting Files", supporting_files_ok),
        ("Lesson Flow Rules", rules_valid)
    ]
    
    passed_assessments = sum(1 for _, passed in assessments if passed)
    total_assessments = len(assessments)
    
    for assessment_name, passed in assessments:
        print(f"   {assessment_name:<20} {'✅ PASS' if passed else '❌ FAIL'}")
    
    overall_success = passed_assessments >= 2  # At least 2/3 should pass
    
    print(f"\n🎉 OVERALL RESULT: {'✅ IMPLEMENTATION READY' if overall_success else '❌ NEEDS WORK'}")
    
    if overall_success:
        print("✅ Complete lesson flow implementation appears ready")
        print("✅ All critical components are in place")
        print("✅ Teaching rules and safety fixes implemented")
        print("✅ Ready for end-to-end testing when backend is available")
    else:
        print("❌ Implementation needs additional work")
        print("❌ Some critical components may be missing")
        print("❌ Review failed validations above")
    
    exit(0 if overall_success else 1)