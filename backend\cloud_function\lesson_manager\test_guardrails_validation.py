#!/usr/bin/env python3
"""
Comprehensive test script to validate the intelligent guardrails system.
Tests that guardrails properly enforce 100% objective coverage and prevent premature quiz transitions.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from unittest.mock import Mock, patch

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from intelligent_guardrails import IntelligentGuardrailsManager, GuardrailViolation, GuardrailSeverity

def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'guardrails_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)

def create_test_lesson_context():
    """Create a sample lesson context for testing"""
    return {
        'subject': 'Mathematics',
        'grade_level': '8th Grade',
        'topic': 'Linear Equations',
        'learning_objectives': [
            'Understand the concept of linear equations',
            'Solve linear equations with one variable',
            'Graph linear equations on coordinate plane',
            'Apply linear equations to real-world problems',
            'Identify slope and y-intercept from equations'
        ],
        'lesson_id': 'test_lesson_001',
        'student_age': 13,
        'cognitive_level': 7
    }

def create_test_session_data(objectives_covered=0, interactions=0, time_elapsed=0):
    """Create test session data with varying completion levels"""
    lesson_context = create_test_lesson_context()
    total_objectives = len(lesson_context['learning_objectives'])
    
    # Calculate coverage percentage
    coverage_percentage = (objectives_covered / total_objectives) * 100 if total_objectives > 0 else 0
    
    return {
        'lesson_context': lesson_context,
        'current_phase': 'teaching_start',
        'objectives_progress': {
            'total_objectives': total_objectives,
            'objectives_covered': objectives_covered,
            'coverage_percentage': coverage_percentage,
            'detailed_progress': {
                f'objective_{i+1}': i < objectives_covered 
                for i in range(total_objectives)
            }
        },
        'interaction_count': interactions,
        'teaching_time_minutes': time_elapsed,
        'content_depth_score': 0.6 + (objectives_covered * 0.1),  # Increases with coverage
        'student_engagement_score': 0.7,
        'ai_assessment': {
            'quiz_ready': objectives_covered >= 4,  # Ready if 4+ objectives covered
            'confidence_score': 0.5 + (objectives_covered * 0.1)
        }
    }

def create_quiz_transition_ai_response():
    """Create an AI response that attempts to transition to quiz"""
    return """
Based on our discussion, I think you're ready for a quiz to test your understanding of linear equations.

Let's move on to assess what you've learned with some practice questions.

**Quiz Time!** 
I'll now give you some problems to solve to check your mastery of linear equations.
"""

def create_teaching_ai_response():
    """Create an AI response that continues teaching"""
    return """
Great question! Let me explain more about linear equations.

A linear equation is an equation that makes a straight line when graphed. The general form is y = mx + b, where:
- m is the slope (how steep the line is)
- b is the y-intercept (where the line crosses the y-axis)

Let's work through an example together...
"""

async def test_guardrails_block_premature_quiz():
    """Test that guardrails block quiz transitions when objectives aren't fully covered"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing guardrails blocking premature quiz transitions...")
    
    guardrails = IntelligentGuardrailsManager()
    
    # Test case 1: Only 2 out of 5 objectives covered (40%)
    session_data = create_test_session_data(objectives_covered=2, interactions=5, time_elapsed=8)
    ai_response = create_quiz_transition_ai_response()
    
    logger.info(f"📊 Test scenario: {session_data['objectives_progress']['coverage_percentage']:.1f}% objectives covered")
    
    # Validate with guardrails
    validation_result = await guardrails.validate_ai_response(
        ai_response=ai_response,
        lesson_context=session_data['lesson_context'],
        session_data=session_data,
        request_id="test_001"
    )
    
    # Check results
    has_blocking_violations = any(
        violation.severity == GuardrailSeverity.BLOCKING 
        for violation in validation_result.violations
    )
    
    logger.info(f"🔍 Violations found: {len(validation_result.violations)}")
    for violation in validation_result.violations:
        logger.info(f"   - {violation.severity.name}: {violation.message}")
    
    # Should block the quiz transition
    if has_blocking_violations:
        logger.info("✅ SUCCESS: Guardrails correctly blocked premature quiz transition")
        return True
    else:
        logger.error("❌ FAILURE: Guardrails failed to block premature quiz transition")
        return False

async def test_guardrails_allow_complete_coverage():
    """Test that guardrails allow quiz transitions when objectives are fully covered"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing guardrails allowing quiz with complete coverage...")
    
    guardrails = IntelligentGuardrailsManager()
    
    # Test case 2: All 5 objectives covered (100%)
    session_data = create_test_session_data(objectives_covered=5, interactions=12, time_elapsed=15)
    ai_response = create_quiz_transition_ai_response()
    
    logger.info(f"📊 Test scenario: {session_data['objectives_progress']['coverage_percentage']:.1f}% objectives covered")
    
    # Validate with guardrails
    validation_result = await guardrails.validate_ai_response(
        ai_response=ai_response,
        lesson_context=session_data['lesson_context'],
        session_data=session_data,
        request_id="test_002"
    )
    
    # Check results
    has_blocking_violations = any(
        violation.severity == GuardrailSeverity.BLOCKING 
        for violation in validation_result.violations
    )
    
    logger.info(f"🔍 Violations found: {len(validation_result.violations)}")
    for violation in validation_result.violations:
        logger.info(f"   - {violation.severity.name}: {violation.message}")
    
    # Should allow the quiz transition
    if not has_blocking_violations:
        logger.info("✅ SUCCESS: Guardrails correctly allowed quiz with complete coverage")
        return True
    else:
        logger.error("❌ FAILURE: Guardrails incorrectly blocked quiz with complete coverage")
        return False

async def test_guardrails_fallback_threshold():
    """Test that guardrails use 80% fallback threshold under time pressure"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing guardrails fallback threshold under time pressure...")
    
    guardrails = IntelligentGuardrailsManager()
    
    # Test case 3: 4 out of 5 objectives covered (80%) with time pressure
    session_data = create_test_session_data(objectives_covered=4, interactions=15, time_elapsed=32)
    ai_response = create_quiz_transition_ai_response()
    
    logger.info(f"📊 Test scenario: {session_data['objectives_progress']['coverage_percentage']:.1f}% objectives covered, {session_data['teaching_time_minutes']} minutes elapsed")
    
    # Validate with guardrails
    validation_result = await guardrails.validate_ai_response(
        ai_response=ai_response,
        lesson_context=session_data['lesson_context'],
        session_data=session_data,
        request_id="test_003"
    )
    
    # Check results
    has_blocking_violations = any(
        violation.severity == GuardrailSeverity.BLOCKING 
        for violation in validation_result.violations
    )
    
    logger.info(f"🔍 Violations found: {len(validation_result.violations)}")
    for violation in validation_result.violations:
        logger.info(f"   - {violation.severity.name}: {violation.message}")
    
    # Should allow quiz transition due to time pressure and 80% coverage
    if not has_blocking_violations:
        logger.info("✅ SUCCESS: Guardrails correctly applied fallback threshold under time pressure")
        return True
    else:
        logger.error("❌ FAILURE: Guardrails failed to apply fallback threshold under time pressure")
        return False

async def test_guardrails_age_appropriateness():
    """Test that guardrails validate age-appropriate content"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing guardrails age-appropriateness validation...")
    
    guardrails = IntelligentGuardrailsManager()
    
    # Test case 4: Age-inappropriate content for 8th grader
    session_data = create_test_session_data(objectives_covered=3, interactions=8, time_elapsed=10)
    inappropriate_response = """
    Now let's discuss advanced calculus concepts including partial derivatives and multiple integrals.
    
    The Laplace transform of this function involves complex analysis that requires understanding of 
    graduate-level mathematics including topology and real analysis.
    """
    
    logger.info(f"📊 Test scenario: Age-inappropriate content for {session_data['lesson_context']['grade_level']} student")
    
    # Validate with guardrails
    validation_result = await guardrails.validate_ai_response(
        ai_response=inappropriate_response,
        lesson_context=session_data['lesson_context'],
        session_data=session_data,
        request_id="test_004"
    )
    
    # Check for age-appropriateness violations
    age_violations = [
        violation for violation in validation_result.violations
        if 'age' in violation.message.lower() or 'cognitive' in violation.message.lower()
    ]
    
    logger.info(f"🔍 Age-appropriateness violations found: {len(age_violations)}")
    for violation in age_violations:
        logger.info(f"   - {violation.severity.name}: {violation.message}")
    
    # Should detect age-appropriateness issues
    if age_violations:
        logger.info("✅ SUCCESS: Guardrails correctly detected age-inappropriate content")
        return True
    else:
        logger.error("❌ FAILURE: Guardrails failed to detect age-inappropriate content")
        return False

async def test_guardrails_response_enhancement():
    """Test that guardrails enhance AI responses with guidance"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing guardrails response enhancement...")
    
    guardrails = IntelligentGuardrailsManager()
    
    # Test case 5: Response that needs enhancement
    session_data = create_test_session_data(objectives_covered=2, interactions=6, time_elapsed=9)
    basic_response = "Let's move on to the next topic."
    
    logger.info("📊 Test scenario: Basic response that should be enhanced with guidance")
    
    # Validate with guardrails
    validation_result = await guardrails.validate_ai_response(
        ai_response=basic_response,
        lesson_context=session_data['lesson_context'],
        session_data=session_data,
        request_id="test_005"
    )
    
    # Check if response was enhanced
    enhanced_response = validation_result.enhanced_response
    response_was_enhanced = enhanced_response != basic_response
    
    logger.info(f"🔍 Original response length: {len(basic_response)} chars")
    logger.info(f"🔍 Enhanced response length: {len(enhanced_response)} chars")
    logger.info(f"🔍 Response was enhanced: {response_was_enhanced}")
    
    if response_was_enhanced:
        logger.info("✅ SUCCESS: Guardrails correctly enhanced the AI response")
        logger.info(f"📝 Enhanced response preview: {enhanced_response[:100]}...")
        return True
    else:
        logger.error("❌ FAILURE: Guardrails failed to enhance the AI response")
        return False

async def run_comprehensive_test():
    """Run all guardrails tests"""
    logger = setup_logging()
    logger.info("🚀 Starting comprehensive intelligent guardrails validation test")
    logger.info("=" * 80)
    
    test_results = []
    
    # Run all test cases
    test_cases = [
        ("Block Premature Quiz", test_guardrails_block_premature_quiz),
        ("Allow Complete Coverage", test_guardrails_allow_complete_coverage),
        ("Fallback Threshold", test_guardrails_fallback_threshold),
        ("Age Appropriateness", test_guardrails_age_appropriateness),
        ("Response Enhancement", test_guardrails_response_enhancement)
    ]
    
    for test_name, test_func in test_cases:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test '{test_name}' failed with exception: {str(e)}")
            test_results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*80)
    logger.info("📋 TEST RESULTS SUMMARY")
    logger.info("="*80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if passed:
            passed_tests += 1
    
    logger.info(f"\n🎯 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! Intelligent guardrails system is working correctly.")
        return True
    else:
        logger.error(f"⚠️  {total_tests - passed_tests} tests failed. Guardrails system needs attention.")
        return False

if __name__ == "__main__":
    # Run the comprehensive test
    success = asyncio.run(run_comprehensive_test())
    sys.exit(0 if success else 1)
