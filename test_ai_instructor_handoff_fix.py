#!/usr/bin/env python3
"""
Test AI Instructor Handoff to Quiz System Fix
Verifies that teaching completion properly triggers quiz transition.
"""

import sys
import os
import json
import time
import requests
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_backend_connectivity():
    """Test basic backend connectivity"""
    try:
        response = requests.get('http://localhost:5000/health-check', timeout=10)
        if response.status_code == 200:
            logger.info("✅ Backend connectivity test passed")
            return True
        else:
            logger.error(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Backend connectivity test failed: {e}")
        return False

def test_teaching_completion_handoff():
    """Test that teaching completion triggers handoff to quiz system"""
    try:
        # Test payload with high teaching completion metrics
        test_payload = {
            "student_id": "test_student_handoff",
            "lesson_ref": "P5_ENT_046",
            "content_to_enhance": "I understand all the concepts about entrepreneurship now. I'm ready for the quiz.",
            "country": "Nigeria",
            "curriculum": "National Curriculum",
            "grade": "primary-5",
            "subject": "Entrepreneurship",
            "session_id": "fallback-test-handoff-123",
            "chat_history": [],
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 12,  # Above minimum threshold
                "objectives_covered": 100,    # 100% coverage - should trigger handoff
                "total_objectives": 5,
                "content_depth_score": 0.85,  # Above minimum threshold
                "teaching_time_minutes": 18,  # Above minimum threshold
                "student_engagement": "high",
                "lesson_context": {
                    "subject": "Entrepreneurship",
                    "grade": "P5",
                    "lesson_title": "Introduction to Entrepreneurship",
                    "teaching_start_time": datetime.now().isoformat()
                }
            }
        }
        
        logger.info("🧪 Testing AI Instructor Handoff to Quiz System...")
        logger.info("📊 Test conditions:")
        logger.info(f"   - Teaching interactions: {test_payload['session_data']['teaching_interactions']}")
        logger.info(f"   - Objectives covered: {test_payload['session_data']['objectives_covered']}%")
        logger.info(f"   - Content depth score: {test_payload['session_data']['content_depth_score']}")
        logger.info(f"   - Teaching time: {test_payload['session_data']['teaching_time_minutes']} min")
        
        response = requests.post(
            'http://localhost:5000/api/enhance-content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Check for handoff indicators
            handoff_checks = {
                "Response Success": result.get('success', False),
                "State Updates Present": 'state_updates' in result.get('data', {}),
                "Quiz Phase Transition": False,
                "Teaching Complete Flag": False,
                "AI Instructor Handoff": False,
                "Teaching Phase Complete": False
            }
            
            # Extract state updates
            state_updates = result.get('data', {}).get('state_updates', {})
            current_phase = result.get('data', {}).get('current_phase', '')
            
            # Check for quiz transition
            new_phase = state_updates.get('new_phase', '')
            if 'quiz' in new_phase.lower():
                handoff_checks["Quiz Phase Transition"] = True
                logger.info(f"✅ Quiz phase transition detected: {new_phase}")
            
            # Check for teaching completion flags
            if state_updates.get('teaching_complete'):
                handoff_checks["Teaching Complete Flag"] = True
                logger.info("✅ Teaching complete flag detected")
            
            if state_updates.get('ai_instructor_handoff'):
                handoff_checks["AI Instructor Handoff"] = True
                logger.info("✅ AI instructor handoff flag detected")
            
            if state_updates.get('teaching_phase_complete'):
                handoff_checks["Teaching Phase Complete"] = True
                logger.info("✅ Teaching phase complete flag detected")
            
            # Log all results
            logger.info("📊 AI Instructor Handoff Test Results:")
            for check_name, passed in handoff_checks.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"  {status} {check_name}")
            
            # Log response details
            logger.info(f"📝 Current Phase: {current_phase}")
            logger.info(f"📝 New Phase: {new_phase}")
            logger.info(f"📝 State Updates: {json.dumps(state_updates, indent=2)}")
            
            # Check if handoff was successful
            critical_checks = [
                handoff_checks["Response Success"],
                handoff_checks["Quiz Phase Transition"],
                handoff_checks["Teaching Complete Flag"]
            ]
            
            handoff_successful = all(critical_checks)
            
            if handoff_successful:
                logger.info("🎉 AI INSTRUCTOR HANDOFF SUCCESSFUL!")
                logger.info("   Teaching completion properly triggered quiz transition")
                return True
            else:
                logger.warning("⚠️ AI INSTRUCTOR HANDOFF INCOMPLETE")
                logger.warning("   Some handoff indicators are missing")
                return False
            
        else:
            logger.error(f"❌ Handoff test failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Handoff test error: {e}")
        return False

def test_teaching_incomplete_no_handoff():
    """Test that incomplete teaching does NOT trigger handoff"""
    try:
        # Test payload with low teaching completion metrics
        test_payload = {
            "student_id": "test_student_no_handoff",
            "lesson_ref": "P5_ENT_046",
            "content_to_enhance": "Can we do the quiz now?",
            "country": "Nigeria",
            "curriculum": "National Curriculum",
            "grade": "primary-5",
            "subject": "Entrepreneurship",
            "session_id": "fallback-test-no-handoff-456",
            "chat_history": [],
            "session_data": {
                "current_phase": "teaching",
                "teaching_interactions": 3,    # Below minimum threshold
                "objectives_covered": 40,      # Below minimum threshold
                "total_objectives": 5,
                "content_depth_score": 0.45,   # Below minimum threshold
                "teaching_time_minutes": 8,    # Below minimum threshold
                "student_engagement": "medium",
                "lesson_context": {
                    "subject": "Entrepreneurship",
                    "grade": "P5",
                    "lesson_title": "Introduction to Entrepreneurship",
                    "teaching_start_time": datetime.now().isoformat()
                }
            }
        }
        
        logger.info("🧪 Testing Teaching Incomplete - No Handoff...")
        logger.info("📊 Test conditions (should NOT trigger handoff):")
        logger.info(f"   - Teaching interactions: {test_payload['session_data']['teaching_interactions']}")
        logger.info(f"   - Objectives covered: {test_payload['session_data']['objectives_covered']}%")
        logger.info(f"   - Content depth score: {test_payload['session_data']['content_depth_score']}")
        logger.info(f"   - Teaching time: {test_payload['session_data']['teaching_time_minutes']} min")
        
        response = requests.post(
            'http://localhost:5000/api/enhance-content',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract state updates
            state_updates = result.get('data', {}).get('state_updates', {})
            current_phase = result.get('data', {}).get('current_phase', '')
            new_phase = state_updates.get('new_phase', '')
            
            # Check that handoff did NOT occur
            no_handoff_checks = {
                "Response Success": result.get('success', False),
                "No Quiz Transition": 'quiz' not in new_phase.lower(),
                "No Teaching Complete Flag": not state_updates.get('teaching_complete', False),
                "No AI Instructor Handoff": not state_updates.get('ai_instructor_handoff', False),
                "Stays in Teaching Phase": current_phase.startswith('teaching') or new_phase.startswith('teaching')
            }
            
            # Log all results
            logger.info("📊 Teaching Incomplete Test Results:")
            for check_name, passed in no_handoff_checks.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"  {status} {check_name}")
            
            # Log response details
            logger.info(f"📝 Current Phase: {current_phase}")
            logger.info(f"📝 New Phase: {new_phase}")
            
            # Check if no handoff was correctly maintained
            critical_checks = [
                no_handoff_checks["Response Success"],
                no_handoff_checks["No Quiz Transition"],
                no_handoff_checks["Stays in Teaching Phase"]
            ]
            
            no_handoff_successful = all(critical_checks)
            
            if no_handoff_successful:
                logger.info("✅ TEACHING INCOMPLETE TEST PASSED!")
                logger.info("   System correctly prevented premature quiz transition")
                return True
            else:
                logger.warning("⚠️ TEACHING INCOMPLETE TEST FAILED")
                logger.warning("   System may have allowed premature quiz transition")
                return False
            
        else:
            logger.error(f"❌ No handoff test failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ No handoff test error: {e}")
        return False

def run_handoff_verification():
    """Run comprehensive handoff verification tests"""
    logger.info("🚀 Starting AI Instructor Handoff Verification")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Run all tests
    tests = [
        ("Backend Connectivity", test_backend_connectivity),
        ("Teaching Complete → Quiz Handoff", test_teaching_completion_handoff),
        ("Teaching Incomplete → No Handoff", test_teaching_incomplete_no_handoff)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            test_results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED {test_name}: {e}")
            test_results[test_name] = False
    
    # Generate summary report
    logger.info("\n" + "=" * 80)
    logger.info("📊 AI INSTRUCTOR HANDOFF VERIFICATION SUMMARY")
    logger.info("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n📈 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - AI Instructor Handoff is working correctly!")
        logger.info("✅ Teaching completion properly triggers quiz transition")
        logger.info("✅ Incomplete teaching correctly prevents quiz transition")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} tests failed - Handoff system needs attention")
        return False

if __name__ == "__main__":
    success = run_handoff_verification()
    sys.exit(0 if success else 1)