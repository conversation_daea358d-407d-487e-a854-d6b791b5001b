#!/usr/bin/env python3
"""
Fix AI Instructor Quiz Suggestions

This script addresses the issue where the AI Instructor is suggesting quizzes
prematurely (at interaction 8) instead of following the minimum interaction
requirements (10 interactions minimum).

The fix ensures that:
1. AI Instructor receives clear feedback about minimum requirements
2. Quiz suggestions are blocked until requirements are met
3. Teaching rules are enforced consistently
"""

import sys
import os

def add_ai_instructor_quiz_blocking():
    """
    Add explicit quiz suggestion blocking in the AI response processing
    to prevent premature quiz suggestions from reaching the frontend.
    """
    
    main_py_path = 'backend/cloud_function/lesson_manager/main.py'
    
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find where AI responses are processed and add quiz suggestion filtering
    # Look for the section where enhanced_content_text is processed
    
    old_ai_processing = '''        # FORCE CONSOLE OUTPUT
        logger.debug(f"🤖 AI RESPONSE PROCESSED:")
        logger.debug(f"🤖   Content: {enhanced_content_text[:100]}...")
        logger.debug(f"🤖   State: {state_updates_from_ai}")
        try:
            pass  # sys.stdout.flush() removed
        except (ValueError, OSError):
            pass'''
    
    new_ai_processing = '''        # CRITICAL FIX: Filter out premature quiz suggestions from AI responses
        if enhanced_content_text and current_phase_for_ai.startswith('teaching'):
            # Check if AI is suggesting quiz prematurely
            quiz_indicators = [
                'ready to test your knowledge',
                'time for a quiz',
                'let\'s do a quiz',
                'quiz time',
                'ready for the quiz',
                'test your understanding',
                'assessment time'
            ]
            
            ai_suggesting_quiz = any(indicator in enhanced_content_text.lower() for indicator in quiz_indicators)
            
            if ai_suggesting_quiz:
                # Check if requirements are met
                current_interactions = context_for_enhance.get('teaching_interactions', 0)
                objectives_covered = state_updates_from_ai.get('objectives_covered', 0)
                
                requirements_met = (
                    current_interactions >= min_interactions and
                    objectives_covered >= 1
                )
                
                if not requirements_met:
                    logger.warning(f"[{request_id}] 🚫 BLOCKING PREMATURE QUIZ SUGGESTION from AI")
                    logger.warning(f"[{request_id}]   Current interactions: {current_interactions} (need {min_interactions})")
                    logger.warning(f"[{request_id}]   Objectives covered: {objectives_covered} (need ≥1)")
                    
                    # Remove quiz suggestions from AI response
                    for indicator in quiz_indicators:
                        enhanced_content_text = enhanced_content_text.replace(indicator, "continue learning")
                    
                    # Add teaching continuation message
                    enhanced_content_text += "\\n\\nLet's continue exploring this topic to ensure you have a solid understanding before we move on."
                    
                    # Force teaching phase continuation
                    state_updates_from_ai['new_phase'] = 'teaching'
                    state_updates_from_ai['teaching_complete'] = False
                    
                    logger.info(f"[{request_id}] ✅ AI response filtered to continue teaching")
                else:
                    logger.info(f"[{request_id}] ✅ Quiz suggestion approved - requirements met")
        
        # FORCE CONSOLE OUTPUT
        logger.debug(f"🤖 AI RESPONSE PROCESSED:")
        logger.debug(f"🤖   Content: {enhanced_content_text[:100]}...")
        logger.debug(f"🤖   State: {state_updates_from_ai}")
        try:
            pass  # sys.stdout.flush() removed
        except (ValueError, OSError):
            pass'''
    
    if old_ai_processing in content:
        content = content.replace(old_ai_processing, new_ai_processing)
        print("✅ Added AI quiz suggestion filtering")
    else:
        print("⚠️ AI processing section not found - manual review needed")
    
    return content

def enhance_guardrail_feedback():
    """
    Enhance the guardrail feedback system to provide clearer guidance
    to the AI Instructor about teaching requirements.
    """
    
    def apply_feedback_enhancement(content):
        
        # Find the guardrail feedback section and enhance it
        old_feedback = '''                        # Add guardrail feedback to context for AI Instructor
                        state_updates_from_ai['guardrail_feedback'] = {
                            'violations': guardrail_violations,
                            'recommendation': 'Continue teaching to address these issues before quiz',
                            'objectives_covered': objectives_covered,
                            'interactions_count': current_interactions,
                            'min_interactions_required': min_interactions
                        }'''
        
        new_feedback = '''                        # Add enhanced guardrail feedback to context for AI Instructor
                        state_updates_from_ai['guardrail_feedback'] = {
                            'violations': guardrail_violations,
                            'recommendation': 'Continue teaching to address these issues before quiz',
                            'objectives_covered': objectives_covered,
                            'interactions_count': current_interactions,
                            'min_interactions_required': min_interactions,
                            'teaching_status': 'INCOMPLETE',
                            'next_steps': [
                                f'Need {min_interactions - current_interactions} more teaching interactions',
                                'Ensure all learning objectives are covered',
                                'Provide more examples and explanations',
                                'Check student understanding before quiz'
                            ],
                            'quiz_blocked': True,
                            'continue_teaching': True
                        }
                        
                        # Add explicit teaching continuation instruction
                        enhanced_content_text += "\\n\\n[SYSTEM: Continue teaching - quiz requirements not yet met]"'''
        
        if old_feedback in content:
            content = content.replace(old_feedback, new_feedback)
            print("✅ Enhanced guardrail feedback system")
        else:
            print("⚠️ Guardrail feedback section not found - manual review needed")
        
        return content
    
    return apply_feedback_enhancement

def add_teaching_rules_enforcement():
    """
    Add explicit teaching rules enforcement to prevent AI from bypassing requirements.
    """
    
    def apply_rules_enforcement(content):
        
        # Find where teaching phase is processed and add explicit rules
        teaching_phase_check = '''                # Update current_interactions with analysis results if in teaching phase
                if current_phase_for_ai.startswith('teaching'):
                    current_interactions = max(current_interactions, analysis_result.get('interaction_count', 0))'''
        
        enhanced_teaching_check = '''                # Update current_interactions with analysis results if in teaching phase
                if current_phase_for_ai.startswith('teaching'):
                    current_interactions = max(current_interactions, analysis_result.get('interaction_count', 0))
                    
                    # CRITICAL: Enforce teaching rules explicitly
                    teaching_rules_met = (
                        current_interactions >= min_interactions and
                        objectives_covered >= 1 and
                        content_depth_score >= 0.5
                    )
                    
                    if not teaching_rules_met:
                        # Block any quiz transition attempts
                        if state_updates_from_ai.get('new_phase') in ['quiz_initiate', 'quiz']:
                            logger.warning(f"[{request_id}] 🚫 ENFORCING TEACHING RULES - blocking quiz transition")
                            state_updates_from_ai['new_phase'] = 'teaching'
                            state_updates_from_ai['teaching_complete'] = False
                            state_updates_from_ai['teaching_rules_enforced'] = True
                            
                        logger.info(f"[{request_id}] 📚 TEACHING RULES STATUS:")
                        logger.info(f"[{request_id}]   Interactions: {current_interactions}/{min_interactions}")
                        logger.info(f"[{request_id}]   Objectives: {objectives_covered} covered")
                        logger.info(f"[{request_id}]   Content depth: {content_depth_score:.2f}")
                        logger.info(f"[{request_id}]   Rules met: {teaching_rules_met}")'''
        
        if teaching_phase_check in content:
            content = content.replace(teaching_phase_check, enhanced_teaching_check)
            print("✅ Added explicit teaching rules enforcement")
        else:
            print("⚠️ Teaching phase check not found - manual review needed")
        
        return content
    
    return apply_rules_enforcement

def apply_comprehensive_ai_instructor_fixes():
    """
    Apply all fixes to prevent premature quiz suggestions from AI Instructor.
    """
    
    print("🔧 Starting AI Instructor Quiz Suggestion Fixes...")
    print("=" * 60)
    
    try:
        # Step 1: Add AI quiz suggestion filtering
        print("\\n1. Adding AI quiz suggestion filtering...")
        content = add_ai_instructor_quiz_blocking()
        
        # Step 2: Enhance guardrail feedback
        print("\\n2. Enhancing guardrail feedback system...")
        feedback_fix = enhance_guardrail_feedback()
        content = feedback_fix(content)
        
        # Step 3: Add teaching rules enforcement
        print("\\n3. Adding explicit teaching rules enforcement...")
        rules_fix = add_teaching_rules_enforcement()
        content = rules_fix(content)
        
        # Step 4: Write the updated content back to main.py
        main_py_path = 'backend/cloud_function/lesson_manager/main.py'
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("\\n✅ Successfully applied all AI Instructor fixes!")
        print("\\nSummary of changes:")
        print("- Added AI response filtering to block premature quiz suggestions")
        print("- Enhanced guardrail feedback with detailed requirements")
        print("- Added explicit teaching rules enforcement")
        print("- Forced teaching continuation when requirements not met")
        
        return True
        
    except Exception as e:
        print(f"\\n❌ Error applying AI Instructor fixes: {e}")
        return False

if __name__ == "__main__":
    success = apply_comprehensive_ai_instructor_fixes()
    if success:
        print("\\n🎉 AI Instructor Quiz Suggestion Fixes completed successfully!")
        print("\\nNext steps:")
        print("1. Restart the backend server")
        print("2. Run the comprehensive E2E test again")
        print("3. Verify quiz suggestions only appear after 10+ interactions")
    else:
        print("\\n💥 Fix failed - manual intervention required")
        sys.exit(1)