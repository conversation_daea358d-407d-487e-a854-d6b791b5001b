#!/usr/bin/env python3
"""
AI Response Filtering Test

This test verifies that our AI response filtering is working correctly
to catch and block quiz suggestions in teaching phases.
"""

import sys
import os
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ai_response_filtering():
    """Test the AI response filtering patterns"""
    
    # Test responses that should be filtered (blocked)
    problematic_responses = [
        "Absolutely brilliant, <PERSON>! You've perfectly explained how marketing works. You've grasped the core ideas of marketing purpose, methods like posters and word-of-mouth, and even how to connect them with broader concepts. Are you ready to test your knowledge with a short quiz?",
        "Excellent work! I think you're ready to demonstrate what you've learned. Shall we start the quiz?",
        "You've shown great understanding. Ready to test your knowledge?",
        "Perfect! You've grasped the core ideas. Time for a quiz!",
        "Wonderful progress! Are you ready to show what you've learned?",
        "Great job! Let's see how well you understand these concepts with some questions."
    ]
    
    # Test responses that should NOT be filtered (allowed)
    good_responses = [
        "Excellent thinking, <PERSON>! You're really grasping these marketing ideas. Let me continue teaching you about the important aspects that will help you succeed.",
        "Great progress so far! Let me explain more about the key concepts to ensure you have a solid understanding.",
        "Wonderful insights! Let's explore another important aspect of marketing that will help you build a strong foundation.",
        "That's a fantastic observation! Let me build on that idea and show you how it connects to other marketing concepts."
    ]
    
    # Define the filtering patterns (from our implementation)
    quiz_content_patterns = [
        r'quiz\s+question', r'test\s+question', r'assessment\s+question',
        r'question\s+\d+:', r'q\d+:', r'question\s+#\d+',
        r'choose\s+the\s+correct', r'select\s+the\s+best',
        r'multiple\s+choice', r'true\s+or\s+false',
        r'[a-d]\)\s+', r'which\s+of\s+the\s+following',
        r'ready\s+for.*quiz', r'let\'s\s+do.*quiz',
        r'time\s+for.*quiz', r'quiz\s+time',
        # ENHANCED: Block the specific phrases appearing in our test
        r'ready\s+to\s+test\s+your\s+knowledge',
        r'grasped\s+the\s+core\s+ideas',
        r'ready\s+to\s+demonstrate',
        r'show\s+what\s+you\'ve\s+learned',
        r'test\s+your\s+understanding',
        r'demonstrate\s+your\s+knowledge',
        r'are\s+you\s+ready.*quiz',
        r'shall\s+we\s+start.*quiz'
    ]
    
    def check_for_quiz_content(response_text):
        """Check if response contains quiz content that should be filtered"""
        response_lower = response_text.lower()
        
        for pattern in quiz_content_patterns:
            if re.search(pattern, response_lower):
                return True, pattern
        
        return False, None
    
    logger.info("🧪 TESTING AI RESPONSE FILTERING")
    logger.info("=" * 60)
    
    # Test problematic responses (should be filtered)
    logger.info("\n🚨 TESTING PROBLEMATIC RESPONSES (should be filtered):")
    problematic_passed = 0
    
    for i, response in enumerate(problematic_responses, 1):
        should_filter, pattern = check_for_quiz_content(response)
        if should_filter:
            logger.info(f"✅ Response {i}: CORRECTLY FILTERED (pattern: {pattern})")
            problematic_passed += 1
        else:
            logger.error(f"❌ Response {i}: NOT FILTERED (should have been blocked)")
            logger.error(f"   Response: {response[:100]}...")
    
    # Test good responses (should NOT be filtered)
    logger.info("\n✅ TESTING GOOD RESPONSES (should NOT be filtered):")
    good_passed = 0
    
    for i, response in enumerate(good_responses, 1):
        should_filter, pattern = check_for_quiz_content(response)
        if not should_filter:
            logger.info(f"✅ Response {i}: CORRECTLY ALLOWED")
            good_passed += 1
        else:
            logger.error(f"❌ Response {i}: INCORRECTLY FILTERED (pattern: {pattern})")
            logger.error(f"   Response: {response[:100]}...")
    
    # Results
    logger.info("\n📊 FILTERING TEST RESULTS:")
    logger.info(f"   Problematic responses correctly filtered: {problematic_passed}/{len(problematic_responses)}")
    logger.info(f"   Good responses correctly allowed: {good_passed}/{len(good_responses)}")
    
    total_correct = problematic_passed + good_passed
    total_tests = len(problematic_responses) + len(good_responses)
    
    if total_correct == total_tests:
        logger.info("🎉 ALL FILTERING TESTS PASSED!")
        logger.info("✅ AI response filtering is working correctly")
        return True
    else:
        logger.error(f"❌ FILTERING TESTS FAILED: {total_correct}/{total_tests} correct")
        logger.error("🔧 Filtering patterns may need adjustment")
        return False

def main():
    """Main test execution"""
    print("🧪 AI RESPONSE FILTERING TEST")
    print("=" * 40)
    print("Testing if our filtering patterns correctly identify")
    print("quiz suggestions that should be blocked during teaching.")
    print("=" * 40)
    
    success = test_ai_response_filtering()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()