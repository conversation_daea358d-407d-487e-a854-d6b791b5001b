#!/usr/bin/env python3
"""
Enhanced Teaching Completion System Test
=======================================

This test validates the improved teaching completion validation system
with intelligent guardrails and AI instructor handoff logic.
"""

import sys
import os
import json
import time
import logging
from datetime import datetime, timezone

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_teaching_completion_validation():
    """Test the enhanced teaching completion validation system"""
    
    try:
        # Import the teaching rules system
        from teaching_rules import validate_teaching_completion, teaching_rules_engine
        
        logger.info("🧪 TESTING: Enhanced Teaching Completion Validation")
        
        # Test Case 1: Incomplete Teaching (should block quiz)
        logger.info("\n📋 TEST CASE 1: Incomplete Teaching")
        
        incomplete_session_data = {
            'teaching_start_time': datetime.now(timezone.utc).isoformat(),
            'lesson_start_time': datetime.now(timezone.utc).isoformat()
        }
        
        incomplete_context = {
            'teaching_interactions': 3,  # Below minimum
            'objectives_covered': 2,     # Below 100%
            'total_objectives': 5,
            'content_depth_score': 0.6,  # Below threshold
            'grade': 'primary_5',
            'teaching_level': 5,
            'instructional_content': 'Basic mathematics lesson on addition and subtraction'
        }
        
        is_complete, reason, details = validate_teaching_completion(
            session_data=incomplete_session_data,
            context=incomplete_context
        )
        
        logger.info(f"   Result: {'✅ PASS' if not is_complete else '❌ FAIL'}")
        logger.info(f"   Teaching Complete: {is_complete}")
        logger.info(f"   Reason: {reason}")
        logger.info(f"   Interactions: {details.get('teaching_interactions', 0)}/{details.get('adaptive_min_interactions', 10)}")
        logger.info(f"   Objectives: {details.get('objective_coverage_pct', 0):.1f}%")
        
        # Test Case 2: Complete Teaching (should allow quiz)
        logger.info("\n📋 TEST CASE 2: Complete Teaching")
        
        complete_context = {
            'teaching_interactions': 12,  # Above minimum
            'objectives_covered': 5,      # 100% coverage
            'total_objectives': 5,
            'content_depth_score': 0.85,  # Above threshold
            'grade': 'primary_5',
            'teaching_level': 5,
            'instructional_content': 'Comprehensive mathematics lesson with examples and practice'
        }
        
        is_complete, reason, details = validate_teaching_completion(
            session_data=incomplete_session_data,  # Same session data
            context=complete_context
        )
        
        logger.info(f"   Result: {'✅ PASS' if is_complete else '❌ FAIL'}")
        logger.info(f"   Teaching Complete: {is_complete}")
        logger.info(f"   Reason: {reason}")
        logger.info(f"   Interactions: {details.get('teaching_interactions', 0)}/{details.get('adaptive_min_interactions', 8)}")
        logger.info(f"   Objectives: {details.get('objective_coverage_pct', 0):.1f}%")
        
        # Test Case 3: Adaptive Requirements
        logger.info("\n📋 TEST CASE 3: Adaptive Requirements")
        
        adaptive_reqs = teaching_rules_engine.calculate_adaptive_requirements(complete_context)
        logger.info(f"   Grade: {adaptive_reqs['reasoning']['grade']}")
        logger.info(f"   Teaching Level: {adaptive_reqs['reasoning']['teaching_level']}")
        logger.info(f"   Lesson Complexity: {adaptive_reqs['reasoning']['complexity']}")
        logger.info(f"   Min Interactions: {adaptive_reqs['min_interactions']}")
        logger.info(f"   Min Content Depth: {adaptive_reqs['min_content_depth']:.2f}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import Error: {e}")
        logger.error("Make sure you're running this from the project root directory")
        return False
    except Exception as e:
        logger.error(f"❌ Test Error: {e}")
        return False

def test_intelligent_guardrails():
    """Test the intelligent guardrails system"""
    
    try:
        from intelligent_guardrails import apply_intelligent_guardrails
        
        logger.info("\n🛡️ TESTING: Intelligent Guardrails System")
        
        # Test Case 1: Quiz content blocked when teaching incomplete
        logger.info("\n📋 TEST CASE 1: Quiz Content Blocking")
        
        quiz_response = """
        Great! Now let's test your understanding with a quiz.
        
        Question 1: What is 2 + 2?
        a) 3
        b) 4
        c) 5
        d) 6
        
        Choose the correct answer.
        """
        
        lesson_context = {
            'teaching_interactions': 3,
            'objectives_covered': 2,
            'total_objectives': 5,
            'grade': 'primary_5'
        }
        
        session_data = {
            'teaching_start_time': datetime.now(timezone.utc).isoformat(),
            'current_phase': 'teaching'
        }
        
        is_valid, enhanced_response, violations = apply_intelligent_guardrails(
            ai_response=quiz_response,
            lesson_context=lesson_context,
            session_data=session_data,
            teaching_truly_complete=False,  # Teaching not complete
            request_id="test_001"
        )
        
        logger.info(f"   Result: {'✅ PASS' if not is_valid else '❌ FAIL'}")
        logger.info(f"   Response Valid: {is_valid}")
        logger.info(f"   Violations: {len(violations)}")
        
        if violations:
            for violation in violations:
                logger.info(f"   - {violation['rule_id']}: {violation['message']}")
        
        # Test Case 2: Quiz content allowed when teaching complete
        logger.info("\n📋 TEST CASE 2: Quiz Content Allowed")
        
        is_valid, enhanced_response, violations = apply_intelligent_guardrails(
            ai_response=quiz_response,
            lesson_context=lesson_context,
            session_data=session_data,
            teaching_truly_complete=True,  # Teaching complete
            request_id="test_002"
        )
        
        logger.info(f"   Result: {'✅ PASS' if is_valid else '❌ FAIL'}")
        logger.info(f"   Response Valid: {is_valid}")
        logger.info(f"   Violations: {len(violations)}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Test Error: {e}")
        return False

def test_ai_instructor_handoff():
    """Test the AI instructor handoff logic"""
    
    logger.info("\n🔄 TESTING: AI Instructor Handoff Logic")
    
    # Simulate the handoff logic from your code
    def simulate_handoff(teaching_truly_complete, completion_reason):
        """Simulate the AI instructor handoff logic"""
        
        state_updates = {}
        
        if teaching_truly_complete:
            logger.info(f"✅ TEACHING COMPLETE: {completion_reason}")
            state_updates['new_phase'] = 'quiz_initiate'
            state_updates['teaching_complete'] = True
            
            # CRITICAL: Hand off from AI Instructor to existing quiz system
            logger.info("🔄 HANDOFF: AI Instructor → Existing Quiz System")
            state_updates['ai_instructor_handoff'] = True
            state_updates['teaching_phase_complete'] = True
            
            return state_updates, "Handoff successful"
        else:
            logger.info(f"❌ TEACHING INCOMPLETE: {completion_reason}")
            state_updates['new_phase'] = 'teaching'
            state_updates['teaching_complete'] = False
            
            return state_updates, "Continue teaching"
    
    # Test Case 1: Successful handoff
    logger.info("\n📋 TEST CASE 1: Successful Handoff")
    state_updates, message = simulate_handoff(True, "100% objectives covered with sufficient interactions")
    
    logger.info(f"   Result: {'✅ PASS' if state_updates.get('ai_instructor_handoff') else '❌ FAIL'}")
    logger.info(f"   New Phase: {state_updates.get('new_phase')}")
    logger.info(f"   Teaching Complete: {state_updates.get('teaching_complete')}")
    logger.info(f"   AI Handoff: {state_updates.get('ai_instructor_handoff')}")
    
    # Test Case 2: Continue teaching
    logger.info("\n📋 TEST CASE 2: Continue Teaching")
    state_updates, message = simulate_handoff(False, "pursuing optimal 100% objective coverage")
    
    logger.info(f"   Result: {'✅ PASS' if not state_updates.get('ai_instructor_handoff') else '❌ FAIL'}")
    logger.info(f"   New Phase: {state_updates.get('new_phase')}")
    logger.info(f"   Teaching Complete: {state_updates.get('teaching_complete')}")
    
    return True

def main():
    """Run all tests"""
    
    logger.info("🚀 STARTING: Enhanced Teaching Completion System Tests")
    logger.info("=" * 80)
    
    test_results = []
    
    # Run tests
    test_results.append(("Teaching Completion Validation", test_teaching_completion_validation()))
    test_results.append(("Intelligent Guardrails", test_intelligent_guardrails()))
    test_results.append(("AI Instructor Handoff", test_ai_instructor_handoff()))
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! The enhanced teaching completion system is working correctly.")
    else:
        logger.warning("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)