#!/usr/bin/env python3
"""
Debug Teaching Completion Issue
==============================

This script examines why the teaching completion validation
is not working correctly in the live system.
"""

import requests
import json
import time

def debug_teaching_completion():
    """Debug the teaching completion validation"""
    
    url = "http://localhost:5000/api/enhance-content"
    session_id = f"debug_teaching_completion_{int(time.time())}"
    student_id = f"debug_student_{int(time.time())}"
    
    headers = {
        'Content-Type': 'application/json',
        'X-Testing-Mode': 'true',
        'X-Student-ID': student_id
    }
    
    print("🔍 DEBUGGING: Teaching Completion Validation Issue")
    print("=" * 70)
    
    # Simulate a teaching scenario with high completion metrics
    test_data = {
        "session_id": session_id,
        "lesson_ref": "P5_AI_088",  # Use the same lesson as in the example
        "subject": "artificial_intelligence",
        "grade": "primary_5",
        "student_input": "I understand everything now. Can we do the quiz?",
        "current_phase": "teaching",
        "teaching_interactions": 15,  # High number
        "objectives_covered": 5,      # 100% coverage
        "total_objectives": 5,
        "content_depth_score": 0.95,  # Very high
        "teaching_level": 6,          # Same as in example
        "lesson_start_time": time.time() - 1800,  # 30 minutes ago
        "teaching_start_time": time.time() - 900   # 15 minutes ago
    }
    
    print("📊 TEST DATA:")
    print(f"   Teaching Interactions: {test_data['teaching_interactions']}")
    print(f"   Objectives Covered: {test_data['objectives_covered']}/{test_data['total_objectives']} (100%)")
    print(f"   Content Depth Score: {test_data['content_depth_score']}")
    print(f"   Teaching Level: {test_data['teaching_level']}")
    print(f"   Lesson Duration: 30 minutes")
    print(f"   Teaching Duration: 15 minutes")
    print("=" * 70)
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Extract key information
            ai_response = response_data.get('data', {}).get('enhanced_content', '')
            current_phase = response_data.get('data', {}).get('current_phase', '')
            new_phase = response_data.get('data', {}).get('state_updates', {}).get('new_phase', '')
            teaching_complete = response_data.get('data', {}).get('state_updates', {}).get('teaching_complete', False)
            
            print(f"\n📋 RESPONSE ANALYSIS:")
            print(f"   Current Phase: {current_phase}")
            print(f"   New Phase: {new_phase}")
            print(f"   Teaching Complete: {teaching_complete}")
            print(f"   AI Response: {ai_response[:200]}...")
            
            # Check for teaching completion indicators
            response_str = json.dumps(response_data, default=str).lower()
            
            completion_indicators = [
                'teaching complete',
                'quiz initiate',
                'handoff',
                'primary success',
                '100% objectives',
                'teaching truly complete',
                'completion_reason'
            ]
            
            found_indicators = [indicator for indicator in completion_indicators 
                              if indicator in response_str]
            
            print(f"\n🔍 COMPLETION INDICATORS FOUND: {len(found_indicators)}")
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            # Check for teaching continuation indicators
            continuation_indicators = [
                'continue teaching',
                'completion criteria',
                'not complete',
                'more practice',
                'need more'
            ]
            
            found_continuation = [indicator for indicator in continuation_indicators 
                                if indicator in response_str]
            
            print(f"\n⏳ CONTINUATION INDICATORS FOUND: {len(found_continuation)}")
            for indicator in found_continuation:
                print(f"   ⚠️ {indicator}")
            
            # Look for validation details in debug info
            debug_info = response_data.get('data', {}).get('debug_info', {})
            if debug_info:
                print(f"\n🐛 DEBUG INFO AVAILABLE:")
                print(json.dumps(debug_info, indent=2, default=str))
            
            # Print full response for detailed analysis
            print(f"\n📄 FULL RESPONSE:")
            print(json.dumps(response_data, indent=2, default=str))
            
            # Determine if the issue is resolved
            if new_phase == 'quiz_initiate' or teaching_complete:
                print(f"\n✅ SUCCESS: Teaching completion validation is working!")
                return True
            elif 'continue teaching' in ai_response.lower():
                print(f"\n❌ ISSUE CONFIRMED: System is stuck in teaching loop")
                print(f"   The enhanced validation may not be receiving correct metrics")
                return False
            else:
                print(f"\n⚠️ UNCLEAR: Response doesn't clearly indicate completion or continuation")
                return False
                
        else:
            print("ERROR RESPONSE:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def main():
    """Main debug execution"""
    print("🚀 STARTING: Teaching Completion Debug")
    
    success = debug_teaching_completion()
    
    if success:
        print(f"\n🎉 RESULT: Enhanced teaching completion system is working correctly!")
    else:
        print(f"\n🔧 RESULT: Issue identified - enhanced system needs adjustment")
        print(f"\n💡 POSSIBLE CAUSES:")
        print(f"   1. Session context not passing correct metrics to validation")
        print(f"   2. Teaching completion criteria too strict")
        print(f"   3. Phase transition logic not triggering properly")
        print(f"   4. Guardrails blocking valid quiz transitions")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)