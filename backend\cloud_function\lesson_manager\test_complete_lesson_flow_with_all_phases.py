#!/usr/bin/env python3
"""
Comprehensive end-to-end test to verify complete lesson flow with all phases,
proper teaching rules, and end-of-lesson report generation.
"""

import sys
import os
import json
import time
import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteLessonFlowTest:
    def __init__(self):
        self.backend_url = "http://localhost:5000"
        self.test_session_id = f"complete-lesson-test-{int(time.time())}"
        self.test_data = {
            "session_id": self.test_session_id,
            "student_id": "test_student_comprehensive",
            "lesson_ref": "P5_MAT_180",
            "grade": "Primary 5",
            "subject": "Mathematics"
        }
        self.phase_history = []
        self.interaction_count = 0
        self.teaching_interactions = 0
        self.start_time = time.time()
        
    def log_phase_transition(self, from_phase, to_phase, interaction_type=""):
        """Log phase transitions for analysis."""
        self.phase_history.append({
            'timestamp': datetime.now().isoformat(),
            'from_phase': from_phase,
            'to_phase': to_phase,
            'interaction_count': self.interaction_count,
            'interaction_type': interaction_type,
            'elapsed_time': time.time() - self.start_time
        })
        print(f"📊 Phase Transition: {from_phase} → {to_phase} (Interaction #{self.interaction_count})")
    
    def send_interaction(self, user_query, interaction_type="general"):
        """Send an interaction and track the response."""
        self.interaction_count += 1
        
        print(f"\n🔄 Interaction #{self.interaction_count}: {interaction_type}")
        print(f"📝 Query: {user_query[:100]}...")
        
        try:
            response = requests.post(
                f"{self.backend_url}/lesson_interaction",
                json={
                    **self.test_data,
                    "user_query": user_query
                },
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                current_phase = result.get('data', {}).get('current_phase', 'unknown')
                ai_response = result.get('data', {}).get('ai_response', '')
                
                # Track phase transitions
                if self.phase_history:
                    last_phase = self.phase_history[-1]['to_phase']
                    if current_phase != last_phase:
                        self.log_phase_transition(last_phase, current_phase, interaction_type)
                else:
                    self.log_phase_transition('start', current_phase, interaction_type)
                
                # Count teaching interactions
                if 'teaching' in current_phase:
                    self.teaching_interactions += 1
                
                print(f"✅ Response received - Phase: {current_phase}")
                print(f"📄 Response length: {len(ai_response)} chars")
                
                return {
                    'success': True,
                    'phase': current_phase,
                    'response': ai_response,
                    'full_data': result.get('data', {})
                }
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text}")
                return {'success': False, 'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return {'success': False, 'error': str(e)}
    
    def start_lesson(self):
        """Start the lesson and return initial phase."""
        print("🚀 Starting comprehensive lesson flow test...")
        print(f"📋 Session ID: {self.test_session_id}")
        
        try:
            response = requests.post(
                f"{self.backend_url}/start_lesson",
                json=self.test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                initial_phase = result.get('data', {}).get('current_phase', 'unknown')
                print(f"✅ Lesson started - Initial phase: {initial_phase}")
                self.log_phase_transition('init', initial_phase, 'lesson_start')
                return initial_phase
            else:
                print(f"❌ Failed to start lesson: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Lesson start error: {e}")
            return None
    
    def complete_diagnostic_phase(self):
        """Complete the diagnostic phase with proper answers."""
        print("\n🎯 PHASE 1: DIAGNOSTIC COMPLETION")
        print("=" * 50)
        
        diagnostic_queries = [
            "I understand basic mathematical concepts and can work with numbers",
            "I can solve simple equations and understand mathematical relationships", 
            "I'm familiar with fractions, decimals, and basic algebraic concepts",
            "I can apply mathematical reasoning to solve word problems",
            "I'm ready to learn more advanced mathematical topics and concepts"
        ]
        
        diagnostic_complete = False
        max_diagnostic_attempts = 10
        
        for i, query in enumerate(diagnostic_queries):
            if i >= max_diagnostic_attempts:
                print("⚠️ Max diagnostic attempts reached")
                break
                
            result = self.send_interaction(query, f"diagnostic_q{i+1}")
            
            if not result['success']:
                print(f"❌ Diagnostic interaction {i+1} failed")
                return False
            
            current_phase = result['phase']
            
            # Check if diagnostic is complete
            if 'teaching' in current_phase.lower():
                print(f"✅ Diagnostic completed! Transitioned to: {current_phase}")
                diagnostic_complete = True
                break
            elif current_phase == 'completed':
                print("⚠️ Lesson completed during diagnostic - unexpected")
                return False
            
            print(f"📊 Diagnostic progress - Phase: {current_phase}")
        
        return diagnostic_complete
    
    def complete_teaching_phase(self):
        """Complete the teaching phase following comprehensive rules."""
        print("\n🎓 PHASE 2: TEACHING PHASE COMPLETION")
        print("=" * 50)
        
        teaching_queries = [
            "I understand the concept you just explained. Can you show me more examples?",
            "This makes sense. How does this apply to real-world situations?",
            "I'm following along. Can you explain the next part of this topic?",
            "That's clear. What other important aspects should I know about this?",
            "I see the connection. Can you dive deeper into this concept?",
            "This is interesting. How does this relate to what we learned before?",
            "I understand this part. What's the next step in mastering this topic?",
            "That makes sense. Can you show me some practice problems?",
            "I'm getting it. What are some common mistakes to avoid?",
            "This is helpful. Can you summarize the key points we've covered?",
            "I feel confident about this. What else should I know to master this topic?",
            "I think I understand the main concepts. Can you test my understanding?"
        ]
        
        teaching_complete = False
        premature_quiz_detected = False
        max_teaching_interactions = 15
        min_teaching_interactions = 10  # Based on comprehensive rules
        
        print(f"📋 Teaching Rules Check:")
        print(f"   - Minimum interactions: {min_teaching_interactions}")
        print(f"   - Maximum interactions: {max_teaching_interactions}")
        print(f"   - Must cover learning objectives")
        print(f"   - Must achieve sufficient content depth")
        
        for i, query in enumerate(teaching_queries):
            if i >= max_teaching_interactions:
                print("⚠️ Max teaching interactions reached")
                break
                
            result = self.send_interaction(query, f"teaching_{i+1}")
            
            if not result['success']:
                print(f"❌ Teaching interaction {i+1} failed")
                return False, True  # Failed, premature
            
            current_phase = result['phase']
            response_text = result['response']
            full_data = result['full_data']
            
            # Check for premature quiz triggering
            if 'quiz' in current_phase.lower() and self.teaching_interactions < min_teaching_interactions:
                print(f"❌ PREMATURE QUIZ DETECTED!")
                print(f"   Teaching interactions: {self.teaching_interactions}")
                print(f"   Minimum required: {min_teaching_interactions}")
                premature_quiz_detected = True
                return False, True
            
            # Check teaching progress
            if 'quiz' in current_phase.lower():
                print(f"✅ Teaching completed! Quiz phase triggered: {current_phase}")
                print(f"📊 Teaching interactions completed: {self.teaching_interactions}")
                
                # Validate teaching completion criteria
                if self.teaching_interactions >= min_teaching_interactions:
                    print("✅ Minimum teaching interactions satisfied")
                    teaching_complete = True
                    break
                else:
                    print(f"⚠️ Insufficient teaching interactions: {self.teaching_interactions} < {min_teaching_interactions}")
            
            # Check for lesson completion
            elif current_phase == 'completed':
                print("⚠️ Lesson completed during teaching - checking if valid")
                teaching_complete = True
                break
            
            # Log teaching progress
            objectives_covered = full_data.get('objectives_covered', 0)
            coverage_percentage = full_data.get('coverage_percentage', 0)
            content_depth = full_data.get('teaching_depth_score', 0)
            
            print(f"📊 Teaching Progress:")
            print(f"   - Objectives covered: {objectives_covered}")
            print(f"   - Coverage percentage: {coverage_percentage}%")
            print(f"   - Content depth: {content_depth}")
            print(f"   - Teaching interactions: {self.teaching_interactions}")
        
        return teaching_complete, premature_quiz_detected
    
    def complete_quiz_phase(self):
        """Complete the quiz phase."""
        print("\n🧩 PHASE 3: QUIZ COMPLETION")
        print("=" * 50)
        
        quiz_answers = [
            "Based on what we learned, I think the answer is A because it follows the mathematical principle we discussed.",
            "I believe the correct answer is B. This applies the concept we covered in the teaching phase.",
            "The answer should be C. This demonstrates the relationship between the variables we studied.",
            "I think it's D. This shows the practical application of the mathematical concept.",
            "The correct answer is A. This follows the pattern we identified in our lesson."
        ]
        
        quiz_complete = False
        max_quiz_attempts = 10
        
        for i, answer in enumerate(quiz_answers):
            if i >= max_quiz_attempts:
                print("⚠️ Max quiz attempts reached")
                break
                
            result = self.send_interaction(answer, f"quiz_answer_{i+1}")
            
            if not result['success']:
                print(f"❌ Quiz interaction {i+1} failed")
                return False
            
            current_phase = result['phase']
            
            # Check if quiz is complete
            if current_phase in ['completed', 'conclusion_summary', 'final_assessment_pending']:
                print(f"✅ Quiz completed! Transitioned to: {current_phase}")
                quiz_complete = True
                break
            elif 'quiz' not in current_phase.lower():
                print(f"✅ Quiz phase ended, moved to: {current_phase}")
                quiz_complete = True
                break
            
            print(f"📊 Quiz progress - Phase: {current_phase}")
        
        return quiz_complete
    
    def complete_conclusion_phase(self):
        """Complete the conclusion and final assessment phase."""
        print("\n🎉 PHASE 4: CONCLUSION & FINAL ASSESSMENT")
        print("=" * 50)
        
        conclusion_queries = [
            "Thank you for the lesson. I learned a lot about this topic.",
            "I feel confident about what we covered today.",
            "This lesson was very helpful. I understand the concepts much better now."
        ]
        
        lesson_complete = False
        max_conclusion_attempts = 5
        
        for i, query in enumerate(conclusion_queries):
            if i >= max_conclusion_attempts:
                print("⚠️ Max conclusion attempts reached")
                break
                
            result = self.send_interaction(query, f"conclusion_{i+1}")
            
            if not result['success']:
                print(f"❌ Conclusion interaction {i+1} failed")
                return False
            
            current_phase = result['phase']
            
            # Check if lesson is complete
            if current_phase == 'completed':
                print(f"✅ Lesson completed! Final phase: {current_phase}")
                lesson_complete = True
                break
            
            print(f"📊 Conclusion progress - Phase: {current_phase}")
        
        return lesson_complete
    
    def verify_lesson_reports(self):
        """Verify that end-of-lesson reports are generated and saved."""
        print("\n📊 PHASE 5: LESSON REPORTS VERIFICATION")
        print("=" * 50)
        
        try:
            # Check for lesson session data
            print("🔍 Checking lesson session data...")
            
            # Try to get session data from backend
            session_response = requests.get(
                f"{self.backend_url}/get_session_data",
                params={"session_id": self.test_session_id},
                timeout=10
            )
            
            if session_response.status_code == 200:
                session_data = session_response.json()
                print("✅ Session data retrieved successfully")
                
                # Check key fields
                data = session_data.get('data', {})
                lesson_complete = data.get('lesson_complete', False)
                quiz_complete = data.get('quiz_complete', False)
                teaching_complete = data.get('teaching_complete', False)
                
                print(f"📋 Session Status:")
                print(f"   - Lesson Complete: {lesson_complete}")
                print(f"   - Quiz Complete: {quiz_complete}")
                print(f"   - Teaching Complete: {teaching_complete}")
                print(f"   - Total Interactions: {data.get('total_interactions', 0)}")
                print(f"   - Teaching Interactions: {data.get('teaching_interactions', 0)}")
                print(f"   - Quiz Interactions: {data.get('quiz_interactions', 0)}")
                
                return {
                    'session_data_exists': True,
                    'lesson_complete': lesson_complete,
                    'quiz_complete': quiz_complete,
                    'teaching_complete': teaching_complete,
                    'session_data': data
                }
            else:
                print(f"⚠️ Could not retrieve session data: {session_response.status_code}")
                return {'session_data_exists': False}
                
        except Exception as e:
            print(f"❌ Error verifying reports: {e}")
            return {'session_data_exists': False, 'error': str(e)}
    
    def generate_test_report(self, results):
        """Generate a comprehensive test report."""
        print("\n📋 COMPREHENSIVE TEST REPORT")
        print("=" * 70)
        
        # Test Summary
        print(f"🎯 Test Session: {self.test_session_id}")
        print(f"⏱️  Total Duration: {time.time() - self.start_time:.1f} seconds")
        print(f"🔄 Total Interactions: {self.interaction_count}")
        print(f"🎓 Teaching Interactions: {self.teaching_interactions}")
        
        # Phase Transitions
        print(f"\n📊 Phase Transition History:")
        for i, transition in enumerate(self.phase_history):
            elapsed = transition['elapsed_time']
            print(f"   {i+1:2d}. {transition['from_phase']:20} → {transition['to_phase']:20} "
                  f"({elapsed:5.1f}s) [{transition['interaction_type']}]")
        
        # Test Results
        print(f"\n✅ Test Results Summary:")
        all_passed = True
        
        test_cases = [
            ("Lesson Start", results.get('lesson_started', False)),
            ("Diagnostic Completion", results.get('diagnostic_complete', False)),
            ("Teaching Completion", results.get('teaching_complete', False)),
            ("No Premature Quiz", not results.get('premature_quiz', False)),
            ("Quiz Completion", results.get('quiz_complete', False)),
            ("Lesson Conclusion", results.get('lesson_complete', False)),
            ("Reports Generated", results.get('reports_verified', False))
        ]
        
        for test_name, passed in test_cases:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {test_name:25} {status}")
            if not passed:
                all_passed = False
        
        # Teaching Rules Compliance
        print(f"\n🎓 Teaching Rules Compliance:")
        min_interactions = 8
        teaching_sufficient = self.teaching_interactions >= min_interactions
        print(f"   Minimum Interactions ({min_interactions}): {'✅ PASS' if teaching_sufficient else '❌ FAIL'} "
              f"({self.teaching_interactions} interactions)")
        
        if not teaching_sufficient:
            all_passed = False
        
        # Final Verdict
        print(f"\n🎉 FINAL VERDICT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        
        return all_passed
    
    def run_complete_test(self):
        """Run the complete lesson flow test."""
        results = {}
        
        # Phase 1: Start Lesson
        initial_phase = self.start_lesson()
        results['lesson_started'] = initial_phase is not None
        
        if not results['lesson_started']:
            print("❌ Cannot continue - lesson start failed")
            return self.generate_test_report(results)
        
        # Phase 2: Complete Diagnostic
        results['diagnostic_complete'] = self.complete_diagnostic_phase()
        
        if not results['diagnostic_complete']:
            print("❌ Cannot continue - diagnostic phase failed")
            return self.generate_test_report(results)
        
        # Phase 3: Complete Teaching
        teaching_complete, premature_quiz = self.complete_teaching_phase()
        results['teaching_complete'] = teaching_complete
        results['premature_quiz'] = premature_quiz
        
        if premature_quiz:
            print("❌ Critical issue - premature quiz detected")
            return self.generate_test_report(results)
        
        if not teaching_complete:
            print("❌ Cannot continue - teaching phase failed")
            return self.generate_test_report(results)
        
        # Phase 4: Complete Quiz
        results['quiz_complete'] = self.complete_quiz_phase()
        
        if not results['quiz_complete']:
            print("⚠️ Quiz phase incomplete - attempting conclusion anyway")
        
        # Phase 5: Complete Conclusion
        results['lesson_complete'] = self.complete_conclusion_phase()
        
        # Phase 6: Verify Reports
        report_verification = self.verify_lesson_reports()
        results['reports_verified'] = report_verification.get('session_data_exists', False)
        results['report_data'] = report_verification
        
        # Generate final report
        return self.generate_test_report(results)

def check_backend_status():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("🔧 COMPREHENSIVE LESSON FLOW TEST")
    print("Testing: All phases, teaching rules, and end-of-lesson reports")
    print("=" * 70)
    
    # Check backend status
    if not check_backend_status():
        print("❌ Backend is not running on localhost:5000")
        print("Please start the backend server first")
        sys.exit(1)
    
    # Run the comprehensive test
    test = CompleteLessonFlowTest()
    success = test.run_complete_test()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 COMPREHENSIVE TEST PASSED!")
        print("✅ All phases completed successfully")
        print("✅ Teaching rules followed correctly") 
        print("✅ No premature quiz triggering")
        print("✅ End-of-lesson reports generated")
    else:
        print("💥 COMPREHENSIVE TEST FAILED!")
        print("❌ Some phases or rules were not followed correctly")
        sys.exit(1)