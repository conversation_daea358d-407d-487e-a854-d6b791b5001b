#!/usr/bin/env python3
"""
Simple test to verify intelligent guardrails system is working
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_guardrails import IntelligentGuardrailsManager, GuardrailViolation, GuardrailSeverity

async def test_basic_functionality():
    """Test basic guardrails functionality"""
    print("🚀 Testing Intelligent Guardrails System")
    print("=" * 50)
    
    # Create guardrails manager
    guardrails = IntelligentGuardrailsManager()
    print("✅ GuardrailsManager created successfully")
    
    # Create test data
    lesson_context = {
        'subject': 'Mathematics',
        'grade_level': '8th Grade',
        'topic': 'Linear Equations',
        'learning_objectives': [
            'Understand linear equations',
            'Solve linear equations',
            'Graph linear equations',
            'Apply to real problems',
            'Identify slope and intercept'
        ]
    }
    
    session_data = {
        'lesson_context': lesson_context,
        'current_phase': 'teaching_start',
        'objectives_tracking': {
            'total_objectives': 5,
            'covered_objectives': 2,  # Only 40% covered
            'completion_percentage': 40.0,
            'high_confidence_count': 1
        },
        'interaction_count': 5,
        'elapsed_minutes': 8,  # Changed from teaching_time_minutes
        'content_depth_score': 0.6
    }
    
    # Test AI response that tries to transition to quiz prematurely
    ai_response = "Great! Now let's test your knowledge with a quiz."
    
    print(f"📊 Test scenario: {session_data['objectives_tracking']['completion_percentage']}% objectives covered")
    print(f"🤖 AI Response: {ai_response}")
    
    # Validate with guardrails
    try:
        is_valid, violations, enhanced_response = await guardrails.validate_ai_response(
            ai_response=ai_response,
            lesson_context=lesson_context,
            session_data=session_data,
            request_id="simple_test_001"
        )
        
        print(f"\n🔍 Validation Results:")
        print(f"   Valid: {is_valid}")
        print(f"   Violations: {len(violations)}")
        print(f"   Enhanced Response Length: {len(enhanced_response)} chars")
        
        # Check for blocking violations
        blocking_violations = [v for v in violations if v.severity == GuardrailSeverity.BLOCKING]
        
        if blocking_violations:
            print(f"🛑 BLOCKING violations found: {len(blocking_violations)}")
            for violation in blocking_violations:
                print(f"   - {violation.rule_id}: {violation.message}")
            print("✅ SUCCESS: Guardrails correctly blocked premature quiz transition")
        else:
            print("❌ FAILURE: No blocking violations found - guardrails may not be working")
            
        return len(blocking_violations) > 0
        
    except Exception as e:
        print(f"❌ ERROR during validation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    print(f"\n🎯 Test Result: {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
