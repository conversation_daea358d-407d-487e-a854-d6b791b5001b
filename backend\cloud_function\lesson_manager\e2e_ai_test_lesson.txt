2025-07-21 11:18:41,895 - INFO - [main.py:7115] - Incoming request: {"request_id": "28ea0e91-56d2-47d4-9d5f-7fb244b4e162", "timestamp": "2025-07-21T10:18:41.894795+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-088", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf", "chat_history": []}}
2025-07-21 11:18:41,896 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-21 11:18:41,898 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YSDtmeTkyNgbEZW1SYfLwpRuNUGAiKkepo8jS7PEf5S6k82HwHxEsdDwHJfYc2Y7M8Tyh0vh1MTwe7p-SM0N6rb49uBeNN_0wvDKCkR-DlLY3HyFTTFWmeZ44LZVp-plCbTuvKmUcvvgMU52MM5y05CABD3ncSVogRp0xi3i0emPQl-ZGuGB0-txlD1OmEK29gqVbAbHdaSxwv73IR8lyOfqxp9XtSuudbmy5mCBTU6CZnItqDxRL9Eu6JuZkgFC6coPxL9w4OBVWa0zUaAk4lJpzweKWK2mvSyXWro-iwLcP41p8u9W8QpQX2zvBNp1JqIL-kMWOlHs8YBHJepgtQ', 'User-Agent': 'axios/1.10.0', 'Content-Length': '301', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-21 11:18:41,899 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 28ea0e91-56d2-47d4-9d5f-7fb244b4e162
2025-07-21 11:18:41,900 - INFO - [auth_decorator.py:74] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-21 11:18:41,901 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-21 11:18:41,902 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-21 11:18:41,903 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-21 11:18:41,905 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-21 11:18:41,907 - INFO - [auth_decorator.py:95] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162][require_auth] Development mode detected - bypassing authentication
2025-07-21 11:18:41,909 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-21 11:18:41,912 - INFO - [auth_decorator.py:121] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-21 11:18:41,951 - INFO - [main.py:71] - ============================================================
2025-07-21 11:18:41,952 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-21 11:18:41,952 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 11:18:41,952 - INFO - [main.py:74] - Platform: win32
2025-07-21 11:18:41,953 - INFO - [main.py:75] - ============================================================
2025-07-21 11:18:41,966 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-21 11:18:41,968 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-21 11:18:41,969 - INFO - [main.py:634] - ================================================================================
2025-07-21 11:18:41,969 - INFO - [main.py:635] - LESSON MANAGER BACKEND STARTING UP
2025-07-21 11:18:41,969 - INFO - [main.py:636] - ================================================================================
2025-07-21 11:18:41,970 - INFO - [main.py:637] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 11:18:41,974 - INFO - [main.py:638] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
2025-07-21 11:18:41,975 - INFO - [main.py:639] - Log level: DEBUG
2025-07-21 11:18:41,975 - INFO - [main.py:640] - ================================================================================
2025-07-21 11:18:41,975 - INFO - [main.py:642] - Logging configuration complete with immediate console output
2025-07-21 11:18:41,976 - INFO - [main.py:643] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-21 11:18:41,980 - INFO - [main.py:1340] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-21 11:18:41,980 - INFO - [main.py:1347] - [OK] Enhanced state and auth managers initialized successfully
2025-07-21 11:18:41,985 - INFO - [main.py:1579] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-21 11:18:41,985 - INFO - [main.py:1608] - Phase transition fixes imported successfully
2025-07-21 11:18:41,993 - INFO - [main.py:5039] - Successfully imported utils functions
2025-07-21 11:18:41,993 - INFO - [main.py:5047] - Successfully imported extract_ai_state functions
2025-07-21 11:18:41,997 - INFO - [main.py:5497] - FLASK: Using unified Firebase initialization approach...
2025-07-21 11:18:41,998 - INFO - [unified_firebase_init.py:42] - Firebase already initialized
2025-07-21 11:18:41,998 - INFO - [main.py:5505] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-21 11:18:41,999 - INFO - [main.py:5595] - Gemini API will be initialized on first use (lazy loading).
2025-07-21 11:18:42,019 - INFO - [main.py:19682] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-21 11:18:42,025 - INFO - [main.py:19725] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-21 11:18:42,047 - INFO - [main.py:2067] - Successfully imported timetable_generator functions
2025-07-21 11:18:42,074 - INFO - [main.py:25819] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-21 11:18:42,274 - INFO - [main.py:25822] - Google Cloud Storage client initialized successfully.
2025-07-21 11:18:42,588 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-21 11:18:42,588 - INFO - [auth_decorator.py:164] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-21 11:18:42,589 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-21 11:18:42,596 - INFO - [main.py:7292] -
================================================================================
2025-07-21 11:18:42,597 - WARNING - [main.py:7293] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-21 11:18:42,597 - WARNING - [main.py:7294] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
2025-07-21 11:18:42,597 - INFO - [main.py:7299] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-088","content_to_enhance":"Start diagnostic assessment","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf","chat_history":[]}...
2025-07-21 11:18:42,598 - INFO - [main.py:7301] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-088', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf', 'chat_history': []}
2025-07-21 11:18:42,598 - INFO - [main.py:7303] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍  - Session ID from payload: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:42,598 - INFO - [main.py:7304] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-21 11:18:42,599 - INFO - [main.py:7305] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍  - Lesson Ref from payload: P5-AI-088
2025-07-21 11:18:42,599 - DEBUG - [main.py:7341] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf', lesson_ref='P5-AI-088'
2025-07-21 11:18:42,599 - INFO - [main.py:7342] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf', lesson_ref='P5-AI-088'
2025-07-21 11:18:42,600 - INFO - [main.py:7382] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Level not provided, determined from grade 'primary-5': 5
2025-07-21 11:18:43,082 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-21 11:18:43,512 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-21 11:18:43,798 - INFO - [main.py:6636] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-21 11:18:43,798 - INFO - [main.py:7399] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-088
2025-07-21 11:18:43,799 - INFO - [main.py:7400] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-21 11:18:43,800 - INFO - [main.py:2457] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 PARAMETER MAPPING:
2025-07-21 11:18:43,801 - INFO - [main.py:2458] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📥 Original: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=5
2025-07-21 11:18:43,801 - INFO - [main.py:2459] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📤 Mapped: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=P5
2025-07-21 11:18:43,803 - INFO - [main.py:2464] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🎓 Grade mapping: primary-5 → Primary 5
2025-07-21 11:18:43,806 - INFO - [main.py:2466] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] fetch_lesson_data: Fetching lesson with parameters:
2025-07-21 11:18:43,807 - INFO - [main.py:2467] -   • Country (original): Nigeria
2025-07-21 11:18:43,807 - INFO - [main.py:2468] -   • Country (for Firestore): Nigeria
2025-07-21 11:18:43,808 - INFO - [main.py:2469] -   • Curriculum (original): National Curriculum
2025-07-21 11:18:43,808 - INFO - [main.py:2470] -   • Curriculum (for Firestore): National Curriculum
2025-07-21 11:18:43,809 - INFO - [main.py:2471] -   • Grade (original): primary-5
2025-07-21 11:18:43,809 - INFO - [main.py:2472] -   • Grade (normalized for Firestore): Primary 5
2025-07-21 11:18:43,809 - INFO - [main.py:2473] -   • Level (original): 5
2025-07-21 11:18:43,809 - INFO - [main.py:2474] -   • Level (for Firestore): P5
2025-07-21 11:18:43,810 - INFO - [main.py:2475] -   • Subject (original): Artificial Intelligence
2025-07-21 11:18:43,810 - INFO - [main.py:2476] -   • Subject (for Firestore): Artificial Intelligence
2025-07-21 11:18:43,810 - INFO - [main.py:2477] -   • Lesson ID: P5-AI-088
2025-07-21 11:18:43,810 - INFO - [main.py:2486] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔧 PATH CONSTRUCTION:
2025-07-21 11:18:43,811 - INFO - [main.py:2487] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]    └─ countries/Nigeria
2025-07-21 11:18:43,811 - INFO - [main.py:2488] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]       └─ curriculums/National Curriculum
2025-07-21 11:18:43,811 - INFO - [main.py:2489] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]          └─ grades/Primary 5
2025-07-21 11:18:43,812 - INFO - [main.py:2490] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]             └─ levels/P5
2025-07-21 11:18:43,812 - INFO - [main.py:2491] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]                └─ subjects/Artificial Intelligence
2025-07-21 11:18:43,813 - INFO - [main.py:2492] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]                   └─ lessonRef/P5-AI-088
2025-07-21 11:18:43,813 - INFO - [main.py:2493] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📍 FULL PATH: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-088
2025-07-21 11:18:43,813 - INFO - [main.py:2494] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🆔 LESSON REF: P5-AI-088
2025-07-21 11:18:43,813 - INFO - [main.py:2507] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-088
2025-07-21 11:18:44,332 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,333 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,333 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,334 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,334 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,335 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,337 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,341 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,345 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,345 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,346 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,346 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,346 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,347 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,348 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,348 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,348 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,349 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:44,349 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:44,349 - INFO - [main.py:2576] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Successfully retrieved and sanitized document with keys: ['additionalNotes', 'content', 'subject', 'existingAssessments', 'id', 'instructionalSteps', 'lessonTimeLength', 'learningObjectives', 'metadata', 'digitalMaterials', 'gradeLevel', 'lessonRef', 'lessonTitle', 'topic', 'country', 'extensionActivities', 'theme', 'adaptiveStrategies', 'conclusion', 'curriculumType', 'quizzesAndAssessments', 'introduction']
2025-07-21 11:18:44,349 - INFO - [main.py:16464] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔧 Enhancing lesson data compatibility
2025-07-21 11:18:44,350 - INFO - [main.py:16509] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Set grade: Primary 5
2025-07-21 11:18:44,350 - INFO - [main.py:16577] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Lesson data enhancement complete
2025-07-21 11:18:44,350 - INFO - [main.py:2665] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🗺️ Starting robust field mapping for lesson P5-AI-088
2025-07-21 11:18:44,351 - INFO - [main.py:2693] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Core fields mapped: subject=Artificial Intelligence, topic=AI in Transportation, grade=Primary 5
2025-07-21 11:18:44,351 - INFO - [main.py:2863] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 Extracting key concepts from lesson data
2025-07-21 11:18:44,351 - INFO - [main.py:2870] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Found 0 existing key concepts
2025-07-21 11:18:44,352 - INFO - [main.py:2886] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Extracted concepts from 2 learning objectives
2025-07-21 11:18:44,352 - INFO - [main.py:2912] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Extracted concepts from instructional steps
2025-07-21 11:18:44,353 - INFO - [main.py:2930] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Added topic and subject-based concepts
2025-07-21 11:18:44,353 - INFO - [main.py:2968] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Key concepts extraction complete: 10 unique concepts
2025-07-21 11:18:44,355 - INFO - [main.py:3142] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Universal content extraction: 1149 characters from 4 steps
2025-07-21 11:18:44,359 - INFO - [main.py:3250] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Universal conversion: 4 steps → 4 sections
2025-07-21 11:18:44,360 - INFO - [main.py:2805] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Field mapping completed successfully:
2025-07-21 11:18:44,361 - INFO - [main.py:2806] -   - Subject: Artificial Intelligence
2025-07-21 11:18:44,361 - INFO - [main.py:2807] -   - Topic: AI in Transportation
2025-07-21 11:18:44,362 - INFO - [main.py:2808] -   - Grade: Primary 5
2025-07-21 11:18:44,362 - INFO - [main.py:2809] -   - Key Concepts: 10 extracted
2025-07-21 11:18:44,362 - INFO - [main.py:2810] -   - Instructional Steps: 4
2025-07-21 11:18:44,363 - INFO - [main.py:2811] -   - Total Fields: 38
2025-07-21 11:18:44,363 - INFO - [main.py:3297] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Universal content structure recognized: instructionalSteps (4 steps)
2025-07-21 11:18:44,363 - INFO - [main.py:3318] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ All required fields present after universal mapping
2025-07-21 11:18:44,364 - INFO - [main.py:3327] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📊 Lesson validation complete: 38 fields available for processing
2025-07-21 11:18:44,364 - INFO - [main.py:2591] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Successfully mapped lesson fields for AI inference
2025-07-21 11:18:44,364 - DEBUG - [main.py:682] - Cached result for fetch_lesson_data
2025-07-21 11:18:44,661 - INFO - [main.py:7493] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-21 11:18:44,661 - INFO - [main.py:7618] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Successfully retrieved lesson from primary path
2025-07-21 11:18:44,662 - INFO - [main.py:7629] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ All required fields present after lesson content parsing and mapping
2025-07-21 11:18:44,662 - INFO - [main.py:7668] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-21 11:18:44,663 - INFO - [main.py:4241] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Transportation'.
2025-07-21 11:18:44,664 - INFO - [main.py:5586] - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
2025-07-21 11:18:45,178 - INFO - [main.py:4307] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-21 11:18:45,180 - INFO - [main.py:4307] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-21 11:18:45,189 - INFO - [main.py:4307] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-21 11:18:45,190 - INFO - [main.py:4307] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-21 11:18:45,192 - INFO - [main.py:4307] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-21 11:18:45,199 - INFO - [main.py:4376] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-21 11:18:45,200 - DEBUG - [main.py:4390] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-21 11:18:45,200 - DEBUG - [main.py:4393] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Transportation. Topic: AI in Transportation. Learning Objectives: Understand how AI is used in self-driving cars.; Explore the role of AI in traffic management.. Key Concepts: used; self; driving; cars; Explore; role; traffic; management; Introduction; Transportation. Introduct...
2025-07-21 11:18:45,201 - DEBUG - [main.py:4394] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Transportation. Topic: AI in Transportation. Learning Objectives: Understand how AI is used in self-driving cars.; Explore the role of AI in traffic management.. Key Concepts: used; self; driving; cars; Explore; role; traffic; management; Introduction; Transportation. Introductio...
2025-07-21 11:18:45,201 - DEBUG - [main.py:4395] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-21 11:18:45,204 - INFO - [main.py:4399] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Calling Gemini API for module inference...
2025-07-21 11:18:46,300 - INFO - [main.py:4409] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Gemini API call completed in 1.09s. Raw response: 'ai_tools_and_applications'
2025-07-21 11:18:46,300 - DEBUG - [main.py:4431] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-21 11:18:46,301 - INFO - [main.py:4436] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-21 11:18:46,304 - INFO - [main.py:7702] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-21 11:18:46,308 - INFO - [main.py:4485] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] CACHE MISS or fetch: Getting GS levels for subject 'artificial_intelligence', module 'ai_tools_and_applications'.
2025-07-21 11:18:46,582 - INFO - [main.py:4508] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Fetched metadata for module: 'AI Tools & Applications'
2025-07-21 11:18:47,121 - INFO - [main.py:4540] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Successfully fetched 10 levels for module 'ai_tools_and_applications'.
2025-07-21 11:18:47,122 - INFO - [main.py:7739] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-21 11:18:47,430 - INFO - [main.py:3956] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-21 11:18:48,011 - DEBUG - [main.py:7792] - 🔍 SESSION STATE RETRIEVAL:
2025-07-21 11:18:48,012 - DEBUG - [main.py:7793] - 🔍   - Session ID: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:48,012 - DEBUG - [main.py:7794] - 🔍   - Document Exists: False
2025-07-21 11:18:48,013 - DEBUG - [main.py:7795] - 🔍   - Current Phase: Not found
2025-07-21 11:18:48,013 - DEBUG - [main.py:7796] - 🔍   - Probing Level: Not found
2025-07-21 11:18:48,013 - DEBUG - [main.py:7797] - 🔍   - Question Index: Not found
2025-07-21 11:18:48,014 - WARNING - [main.py:7803] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 SESSION STATE DEBUG:
2025-07-21 11:18:48,014 - WARNING - [main.py:7804] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   - Session exists: False
2025-07-21 11:18:48,014 - WARNING - [main.py:7805] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   - Current phase: None
2025-07-21 11:18:48,015 - WARNING - [main.py:7806] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   - State data keys: []
2025-07-21 11:18:48,015 - DEBUG - [main.py:7824] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-21 11:18:48,015 - DEBUG - [main.py:7825] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Retrieved Phase: 'None'
2025-07-21 11:18:48,015 - DEBUG - [main.py:7826] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Diagnostic Completed: False
2025-07-21 11:18:48,016 - DEBUG - [main.py:7827] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Assigned Level: None
2025-07-21 11:18:48,016 - WARNING - [main.py:7828] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔒 STATE PROTECTION: phase='None', diagnostic_done=False, level=None
2025-07-21 11:18:48,016 - INFO - [main.py:7860] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-21 11:18:48,016 - INFO - [main.py:7861] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] State protection not triggered
2025-07-21 11:18:48,017 - INFO - [main.py:7909] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-21 11:18:48,017 - INFO - [main.py:7910] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   assigned_level_for_teaching (session): None
2025-07-21 11:18:48,017 - INFO - [main.py:7911] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   latest_assessed_level (profile): None
2025-07-21 11:18:48,017 - INFO - [main.py:7912] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   teaching_level_for_returning_student: None
2025-07-21 11:18:48,018 - INFO - [main.py:7913] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   has_completed_diagnostic_before: False
2025-07-21 11:18:48,018 - INFO - [main.py:7914] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   is_first_encounter_for_module: True
2025-07-21 11:18:48,018 - WARNING - [main.py:7919] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-21 11:18:48,018 - INFO - [main.py:7927] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 PHASE INVESTIGATION:
2025-07-21 11:18:48,019 - INFO - [main.py:7928] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Retrieved from Firestore: 'None'
2025-07-21 11:18:48,019 - INFO - [main.py:7929] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-21 11:18:48,019 - INFO - [main.py:7930] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Is first encounter: True
2025-07-21 11:18:48,019 - INFO - [main.py:7931] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Diagnostic completed: False
2025-07-21 11:18:48,020 - INFO - [main.py:7944] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   No stored phase found, starting with: 'smart_diagnostic_start'
2025-07-21 11:18:48,020 - INFO - [main.py:7951] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-21 11:18:48,020 - INFO - [main.py:7953] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Final phase for AI logic: smart_diagnostic_start
2025-07-21 11:18:48,021 - INFO - [main.py:3902] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-21 11:18:48,022 - INFO - [main.py:3618] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-21 11:18:48,023 - INFO - [main.py:3627] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-21 11:18:48,023 - INFO - [main.py:3690] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-21 11:18:48,023 - INFO - [main.py:3910] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-21 11:18:48,023 - WARNING - [main.py:3935] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] State key 'current_probing_level_number' had non-numeric value 'None', using default 2.
2025-07-21 11:18:48,024 - INFO - [main.py:7973] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] NEW SESSION: Forcing question_index to 0 (was: N/A)
2025-07-21 11:18:48,024 - INFO - [main.py:5868] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Diagnostic context validation passed
2025-07-21 11:18:48,024 - INFO - [main.py:5897] - DETERMINE_PHASE: Preserving smart diagnostic phase: 'smart_diagnostic_start'
2025-07-21 11:18:48,025 - WARNING - [main.py:8113] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_start' for first encounter
2025-07-21 11:18:48,025 - INFO - [main.py:8136] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_start
2025-07-21 11:18:48,025 - DEBUG - [main.py:8145] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_start'
2025-07-21 11:18:48,026 - DEBUG - [main.py:8146] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_start'
2025-07-21 11:18:48,026 - INFO - [main.py:8148] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Robust context prepared successfully. Phase: smart_diagnostic_start
2025-07-21 11:18:48,026 - DEBUG - [main.py:8149] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-21 11:18:48,026 - WARNING - [main.py:8372] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖 AI PROMPT GENERATION:
2025-07-21 11:18:48,027 - WARNING - [main.py:8373] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - Current phase: smart_diagnostic_start
2025-07-21 11:18:48,027 - WARNING - [main.py:8374] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - Student query: Start diagnostic assessment...
2025-07-21 11:18:48,027 - WARNING - [main.py:8375] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-21 11:18:48,028 - DEBUG - [main.py:8378] - 🤖 GENERATING AI PROMPT:
2025-07-21 11:18:48,028 - DEBUG - [main.py:8379] - 🤖   Phase: smart_diagnostic_start
2025-07-21 11:18:48,028 - DEBUG - [main.py:8380] - 🤖   Query: Start diagnostic assessment...
2025-07-21 11:18:48,029 - DEBUG - [main.py:8381] - 🤖   Student: Andrea
2025-07-21 11:18:48,029 - INFO - [main.py:10275] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:48,029 - INFO - [main.py:10279] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🚀 First interaction - initializing chat session
2025-07-21 11:18:48,029 - INFO - [main.py:5645] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🚀 Initializing chat session for lesson: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:48,030 - INFO - [main.py:5729] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Creating system prompt for Andrea, Grade primary-5, Topic: AI in Transportation
2025-07-21 11:18:48,030 - INFO - [main.py:5660] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💰 Making SINGLE API call to initialize lesson session
2025-07-21 11:18:48,661 - INFO - [main.py:5668] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ Lesson session fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf initialized successfully
2025-07-21 11:18:48,662 - INFO - [main.py:5669] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💰 COST OPTIMIZATION: All subsequent interactions will use NO additional API calls
2025-07-21 11:18:48,662 - INFO - [main.py:8403] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-21 11:18:48,662 - INFO - [teaching_rules.py:156] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-21 11:18:48,662 - INFO - [teaching_rules.py:157] -    Grade: primary-5 → Multiplier: 1.0
2025-07-21 11:18:48,663 - INFO - [teaching_rules.py:158] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-21 11:18:48,663 - INFO - [teaching_rules.py:159] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-21 11:18:48,663 - INFO - [teaching_rules.py:160] -    Adaptive Min Interactions: 8
2025-07-21 11:18:48,663 - INFO - [teaching_rules.py:161] -    Adaptive Content Depth: 0.75
2025-07-21 11:18:48,664 - INFO - [teaching_rules.py:162] -    Adaptive Teaching Time: 12 min
2025-07-21 11:18:48,664 - INFO - [teaching_rules.py:387] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-21 11:18:48,665 - INFO - [teaching_rules.py:388] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-21 11:18:48,665 - INFO - [teaching_rules.py:389] -       Grade: primary-5 (×1.0)
2025-07-21 11:18:48,666 - INFO - [teaching_rules.py:390] -       Teaching Level: Level 5 (×1.0)
2025-07-21 11:18:48,666 - INFO - [teaching_rules.py:391] -       Lesson Complexity: moderate (×1.0)
2025-07-21 11:18:48,666 - INFO - [teaching_rules.py:392] -       Calculation: 8 × 1.0 × 1.0 × 1.0 = 8
2025-07-21 11:18:48,667 - INFO - [teaching_rules.py:393] -    📈 CURRENT PROGRESS:
2025-07-21 11:18:48,667 - INFO - [teaching_rules.py:394] -       Interactions: 0/8 (❌)
2025-07-21 11:18:48,668 - INFO - [teaching_rules.py:395] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-21 11:18:48,668 - INFO - [teaching_rules.py:396] -       Content Depth: 0.00/0.75 (❌)
2025-07-21 11:18:48,668 - INFO - [teaching_rules.py:397] -       Teaching Time: 0.0/12 min (❌)
2025-07-21 11:18:48,669 - INFO - [teaching_rules.py:398] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_continue_instruction
2025-07-21 11:18:48,669 - INFO - [intelligent_guardrails.py:130] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-21 11:18:48,671 - INFO - [intelligent_guardrails.py:183] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-21 11:18:48,672 - INFO - [intelligent_guardrails.py:639] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-21 11:18:48,672 - INFO - [main.py:8473] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-21 11:18:48,673 - WARNING - [main.py:8484] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖 AI RESPONSE RECEIVED:
2025-07-21 11:18:48,673 - WARNING - [main.py:8485] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - Content length: 293 chars
2025-07-21 11:18:48,673 - WARNING - [main.py:8486] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q1'}
2025-07-21 11:18:48,674 - WARNING - [main.py:8487] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🤖   - Raw state block: None...
2025-07-21 11:18:48,674 - DEBUG - [main.py:8956] - 🤖 AI RESPONSE PROCESSED:
2025-07-21 11:18:48,674 - DEBUG - [main.py:8957] - 🤖   Content: Hello Andrea! Welcome to your lesson on AI in Transportation. To get started, I'll ask a few questio...
2025-07-21 11:18:48,675 - DEBUG - [main.py:8958] - 🤖   State: {'new_phase': 'smart_diagnostic_q1'}
2025-07-21 11:18:48,675 - INFO - [main.py:8984] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-21 11:18:48,675 - INFO - [main.py:8985] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q1, Session=smart_diagnostic_start, Final=smart_diagnostic_q1
2025-07-21 11:18:48,946 - WARNING - [main.py:9074] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_start', new_phase='smart_diagnostic_q1'
2025-07-21 11:18:48,946 - INFO - [main.py:6109] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,947 - WARNING - [main.py:9083] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ STATE UPDATE VALIDATION PASSED
2025-07-21 11:18:48,947 - WARNING - [main.py:9102] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔄 PHASE TRANSITION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,947 - INFO - [main.py:9113] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-21 11:18:48,948 - INFO - [phase_transition_integrity.py:328] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,948 - INFO - [phase_transition_integrity.py:291] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📸 DATA SNAPSHOT CREATED: Session fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf, Phase smart_diagnostic_start
2025-07-21 11:18:48,948 - INFO - [phase_transition_integrity.py:153] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,949 - INFO - [phase_transition_integrity.py:233] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ TRANSITION VALIDATED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,949 - INFO - [phase_transition_integrity.py:358] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ TRANSITION APPLIED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,950 - DEBUG - [phase_transition_integrity.py:841] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-21 11:18:48,951 - DEBUG - [phase_transition_integrity.py:874] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 📝 TRANSITION RECORDED: smart_diagnostic_start → smart_diagnostic_q1 (valid)
2025-07-21 11:18:48,951 - INFO - [phase_transition_integrity.py:404] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_q1
2025-07-21 11:18:48,952 - INFO - [main.py:9179] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ TRANSITION VALIDATED: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,953 - WARNING - [main.py:9184] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-21 11:18:48,954 - WARNING - [main.py:9185] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   1. Input phase: 'smart_diagnostic_start'
2025-07-21 11:18:48,955 - WARNING - [main.py:9186] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-21 11:18:48,955 - WARNING - [main.py:9187] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   3. Proposed phase: 'smart_diagnostic_q1'
2025-07-21 11:18:48,956 - WARNING - [main.py:9188] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   4. Integrity validation result: 'valid'
2025-07-21 11:18:48,957 - WARNING - [main.py:9189] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍   5. Final phase to save: 'smart_diagnostic_q1'
2025-07-21 11:18:48,957 - WARNING - [main.py:9192] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾 FINAL STATE APPLICATION:
2025-07-21 11:18:48,958 - WARNING - [main.py:9193] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾   - Current phase input: 'smart_diagnostic_start'
2025-07-21 11:18:48,958 - WARNING - [main.py:9194] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾   - Validated state updates: 24 fields
2025-07-21 11:18:48,959 - WARNING - [main.py:9195] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾   - Final phase to save: 'smart_diagnostic_q1'
2025-07-21 11:18:48,959 - WARNING - [main.py:9196] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾   - Phase change: True
2025-07-21 11:18:48,960 - WARNING - [main.py:9197] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 💾   - Integrity applied: True
2025-07-21 11:18:48,960 - INFO - [main.py:6141] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] DIAGNOSTIC_FLOW_METRICS:
2025-07-21 11:18:48,961 - INFO - [main.py:6142] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Phase transition: smart_diagnostic_start -> smart_diagnostic_q1
2025-07-21 11:18:48,961 - INFO - [main.py:6143] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Current level: 2
2025-07-21 11:18:48,961 - INFO - [main.py:6144] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Question index: 0
2025-07-21 11:18:48,961 - INFO - [main.py:6145] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   First encounter: True
2025-07-21 11:18:48,961 - INFO - [main.py:6150] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Answers collected: 0
2025-07-21 11:18:48,962 - INFO - [main.py:6151] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Levels failed: 0
2025-07-21 11:18:48,962 - INFO - [main.py:6109] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:48,962 - INFO - [main.py:6155] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   State update valid: True
2025-07-21 11:18:48,962 - INFO - [main.py:6162] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162]   Diagnostic complete: False
2025-07-21 11:18:48,963 - WARNING - [main.py:9215] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-21 11:18:48,963 - INFO - [main.py:9223] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-21 11:18:48,963 - INFO - [main.py:9224] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🔍 DEBUG original teaching_interactions: 0
2025-07-21 11:18:49,525 - WARNING - [main.py:9269] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-21 11:18:49,526 - WARNING - [main.py:9270] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Phase: smart_diagnostic_q1
2025-07-21 11:18:49,526 - WARNING - [main.py:9271] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Probing Level: 2
2025-07-21 11:18:49,526 - WARNING - [main.py:9272] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Question Index: 0
2025-07-21 11:18:49,527 - WARNING - [main.py:9273] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Diagnostic Complete: False
2025-07-21 11:18:49,527 - WARNING - [main.py:9280] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Quiz Questions Saved: 0
2025-07-21 11:18:49,528 - WARNING - [main.py:9281] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Quiz Answers Saved: 0
2025-07-21 11:18:49,528 - WARNING - [main.py:9282] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅   - Quiz Started: False
2025-07-21 11:18:49,529 - DEBUG - [main.py:9331] - 🔥 STATE SAVED - Session: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf, Phase: smart_diagnostic_q1
2025-07-21 11:18:49,529 - DEBUG - [main.py:9332] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-21 11:18:50,368 - INFO - [main.py:9376] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Created new session document: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:50,369 - INFO - [main.py:17384] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-21 11:18:50,369 - DEBUG - [main.py:4728] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-21 11:18:50,370 - DEBUG - [main.py:9484] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] No final assessment data found in AI response
2025-07-21 11:18:50,370 - DEBUG - [main.py:9507] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] No lesson completion detected (Phase: smart_diagnostic_q1, Complete: False)
2025-07-21 11:18:50,371 - DEBUG - [main.py:9531] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-21 11:18:50,371 - DEBUG - [main.py:9532] - 🔒   Current Phase: smart_diagnostic_start
2025-07-21 11:18:50,372 - DEBUG - [main.py:9533] - 🔒   Final Phase: smart_diagnostic_q1
2025-07-21 11:18:50,372 - DEBUG - [main.py:9534] - 🔒   Diagnostic Complete: False
2025-07-21 11:18:50,373 - DEBUG - [main.py:9535] - 🔒   Assigned Level: None
2025-07-21 11:18:50,373 - INFO - [main.py:9608] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-21 11:18:50,374 - INFO - [main.py:9642] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-21 11:18:50,374 - INFO - [main.py:9650] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-21 11:18:50,375 - INFO - [main.py:9655] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-21 11:18:50,375 - ERROR - [main.py:9727] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ❌ GUARDRAILS VALIDATION ERROR: IntelligentGuardrailsManager.validate_ai_response() missing 1 required positional argument: 'teaching_truly_complete'
2025-07-21 11:18:50,376 - WARNING - [main.py:9729] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] ⚠️ CONTINUING WITH ORIGINAL RESPONSE DUE TO GUARDRAILS ERROR
2025-07-21 11:18:50,376 - DEBUG - [main.py:9768] - 🎯 RESPONSE READY:
2025-07-21 11:18:50,376 - DEBUG - [main.py:9769] - 🎯   Session: fallback-ef50ab75-a6ad-4f4f-99ba-a460331850bf
2025-07-21 11:18:50,377 - DEBUG - [main.py:9770] - 🎯   Phase: smart_diagnostic_start → smart_diagnostic_q1
2025-07-21 11:18:50,377 - DEBUG - [main.py:9771] - 🎯   Content: Hello Andrea! Welcome to your lesson on AI in Tran...
2025-07-21 11:18:50,377 - DEBUG - [main.py:9772] - 🎯   Request ID: 28ea0e91-56d2-47d4-9d5f-7fb244b4e162
2025-07-21 11:18:50,378 - INFO - [main.py:9778] - [28ea0e91-56d2-47d4-9d5f-7fb244b4e162] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-21 11:18:50,378 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,378 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,379 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,379 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:50,379 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:50,379 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:50,379 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:50,380 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,380 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-21 11:18:50,380 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,380 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,380 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,381 - DEBUG - [main.py:22509] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-21 11:18:50,381 - WARNING - [main.py:705] - High response time detected: 7.79s for enhance_content_api
