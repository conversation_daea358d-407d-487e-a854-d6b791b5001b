#!/usr/bin/env python3
"""
Validate Teaching Rules Implementation

This script validates that the teaching rules enforcement fixes have been properly
implemented in the main.py file and are working as expected.
"""

import os
import sys
import re
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_main_py_fixes():
    """Validate that the fixes have been properly applied to main.py"""
    print("🔍 VALIDATING MAIN.PY FIXES")
    print("=" * 60)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    main_py_path = os.path.join(backend_path, 'main.py')
    
    if not os.path.exists(main_py_path):
        print(f"❌ main.py not found at {main_py_path}")
        return False
    
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    fixes_validated = []
    
    # Validation 1: Check for stricter teaching rules
    print("\n1. Validating Stricter Teaching Rules...")
    
    strict_rules_patterns = [
        r'STRICT_MIN_INTERACTIONS\s*=\s*12',
        r'STRICT_MIN_OBJECTIVES\s*=\s*0\.85',
        r'STRICT_MIN_CONTENT_DEPTH\s*=\s*0\.75',
        r'STRICT_MIN_TIME\s*=\s*15\.0'
    ]
    
    strict_rules_found = 0
    for pattern in strict_rules_patterns:
        if re.search(pattern, content):
            strict_rules_found += 1
    
    if strict_rules_found >= 3:  # At least 3 of 4 patterns should be found
        print("   ✅ Stricter teaching rules constants found")
        fixes_validated.append("Stricter Teaching Rules")
    else:
        print(f"   ❌ Stricter teaching rules not found (found {strict_rules_found}/4 patterns)")
    
    # Check for strict criteria validation
    if re.search(r'strict_criteria_met\s*=\s*sum', content):
        print("   ✅ Strict criteria validation logic found")
    else:
        print("   ❌ Strict criteria validation logic not found")
    
    # Validation 2: Check for enhanced phase transition logic
    print("\n2. Validating Enhanced Phase Transitions...")
    
    phase_transition_patterns = [
        r'phase_transition_timestamp',
        r'current_phase.*=.*new_phase_to_save',
        r'PHASE TRANSITION:.*→',
        r'VALID PHASE TRANSITION TO'
    ]
    
    phase_transitions_found = 0
    for pattern in phase_transition_patterns:
        if re.search(pattern, content):
            phase_transitions_found += 1
    
    if phase_transitions_found >= 2:
        print("   ✅ Enhanced phase transition logic found")
        fixes_validated.append("Enhanced Phase Transitions")
    else:
        print(f"   ❌ Enhanced phase transition logic not found (found {phase_transitions_found}/4 patterns)")
    
    # Validation 3: Check for lesson completion progression
    print("\n3. Validating Lesson Completion Progression...")
    
    completion_patterns = [
        r'FORCING LESSON COMPLETION PROGRESSION',
        r'lesson_complete.*=.*True',
        r'lesson_summary.*=',
        r'completion_status.*completed'
    ]
    
    completion_found = 0
    for pattern in completion_patterns:
        if re.search(pattern, content):
            completion_found += 1
    
    if completion_found >= 2:
        print("   ✅ Lesson completion progression logic found")
        fixes_validated.append("Lesson Completion Progression")
    else:
        print(f"   ❌ Lesson completion progression logic not found (found {completion_found}/4 patterns)")
    
    # Validation 4: Check for teaching rules override logic
    print("\n4. Validating Teaching Rules Override...")
    
    override_patterns = [
        r'STRICT RULES OVERRIDE',
        r'teaching_truly_complete\s*=\s*False',
        r'strict teaching rules:',
        r'criteria met'
    ]
    
    override_found = 0
    for pattern in override_patterns:
        if re.search(pattern, content):
            override_found += 1
    
    if override_found >= 2:
        print("   ✅ Teaching rules override logic found")
        fixes_validated.append("Teaching Rules Override")
    else:
        print(f"   ❌ Teaching rules override logic not found (found {override_found}/4 patterns)")
    
    # Summary
    print(f"\n📊 VALIDATION SUMMARY")
    print("-" * 40)
    print(f"Fixes validated: {len(fixes_validated)}/4")
    for fix in fixes_validated:
        print(f"✅ {fix}")
    
    missing_fixes = 4 - len(fixes_validated)
    if missing_fixes > 0:
        print(f"\n⚠️ {missing_fixes} fix(es) not properly implemented")
        return False
    else:
        print("\n🎉 ALL FIXES PROPERLY IMPLEMENTED!")
        return True

def validate_backup_exists():
    """Validate that backup was created"""
    print("\n💾 VALIDATING BACKUP FILE")
    print("-" * 40)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    backup_path = os.path.join(backend_path, 'main.py.backup_teaching_rules_fix')
    
    if os.path.exists(backup_path):
        backup_size = os.path.getsize(backup_path)
        print(f"✅ Backup file exists: {backup_path}")
        print(f"   Size: {backup_size:,} bytes")
        return True
    else:
        print(f"❌ Backup file not found: {backup_path}")
        return False

def check_code_syntax():
    """Check that the modified main.py has valid Python syntax"""
    print("\n🐍 VALIDATING PYTHON SYNTAX")
    print("-" * 40)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    main_py_path = os.path.join(backend_path, 'main.py')
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to compile the code
        compile(content, main_py_path, 'exec')
        print("✅ Python syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error found:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False

def analyze_teaching_rules_strength():
    """Analyze the strength of the implemented teaching rules"""
    print("\n💪 ANALYZING TEACHING RULES STRENGTH")
    print("-" * 40)
    
    backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
    main_py_path = os.path.join(backend_path, 'main.py')
    
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract the minimum requirements
    requirements = {}
    
    # Find minimum interactions
    interactions_match = re.search(r'STRICT_MIN_INTERACTIONS\s*=\s*(\d+)', content)
    if interactions_match:
        requirements['min_interactions'] = int(interactions_match.group(1))
    
    # Find minimum objectives
    objectives_match = re.search(r'STRICT_MIN_OBJECTIVES\s*=\s*(0\.\d+)', content)
    if objectives_match:
        requirements['min_objectives'] = float(objectives_match.group(1))
    
    # Find minimum content depth
    depth_match = re.search(r'STRICT_MIN_CONTENT_DEPTH\s*=\s*(0\.\d+)', content)
    if depth_match:
        requirements['min_content_depth'] = float(depth_match.group(1))
    
    # Find minimum time
    time_match = re.search(r'STRICT_MIN_TIME\s*=\s*(\d+\.?\d*)', content)
    if time_match:
        requirements['min_time'] = float(time_match.group(1))
    
    print("Teaching Rules Requirements:")
    for key, value in requirements.items():
        if key == 'min_interactions':
            print(f"   Minimum Interactions: {value}")
            if value >= 12:
                print("     ✅ Sufficiently strict (≥12)")
            else:
                print("     ⚠️ May be too lenient (<12)")
        elif key == 'min_objectives':
            print(f"   Minimum Objectives: {value:.1%}")
            if value >= 0.85:
                print("     ✅ Sufficiently strict (≥85%)")
            else:
                print("     ⚠️ May be too lenient (<85%)")
        elif key == 'min_content_depth':
            print(f"   Minimum Content Depth: {value}")
            if value >= 0.75:
                print("     ✅ Sufficiently strict (≥0.75)")
            else:
                print("     ⚠️ May be too lenient (<0.75)")
        elif key == 'min_time':
            print(f"   Minimum Time: {value} minutes")
            if value >= 15.0:
                print("     ✅ Sufficiently strict (≥15 min)")
            else:
                print("     ⚠️ May be too lenient (<15 min)")
    
    # Check if all requirements are strict enough
    strict_enough = (
        requirements.get('min_interactions', 0) >= 10 and
        requirements.get('min_objectives', 0) >= 0.85 and
        requirements.get('min_content_depth', 0) >= 0.75 and
        requirements.get('min_time', 0) >= 15.0
    )
    
    if strict_enough:
        print("\n✅ Teaching rules are sufficiently strict")
        return True
    else:
        print("\n⚠️ Teaching rules may need to be stricter")
        return False

def generate_implementation_report():
    """Generate a comprehensive implementation report"""
    print("\n📋 GENERATING IMPLEMENTATION REPORT")
    print("=" * 60)
    
    report = {
        'timestamp': str(datetime.now()),
        'fixes_applied': [],
        'validation_results': {},
        'recommendations': []
    }
    
    # Validate each component
    main_py_valid = validate_main_py_fixes()
    backup_exists = validate_backup_exists()
    syntax_valid = check_code_syntax()
    rules_strict = analyze_teaching_rules_strength()
    
    report['validation_results'] = {
        'main_py_fixes': main_py_valid,
        'backup_created': backup_exists,
        'syntax_valid': syntax_valid,
        'rules_sufficiently_strict': rules_strict
    }
    
    # Generate recommendations
    if not main_py_valid:
        report['recommendations'].append("Re-run the fix script to ensure all changes are applied")
    
    if not backup_exists:
        report['recommendations'].append("Create a backup of main.py before making changes")
    
    if not syntax_valid:
        report['recommendations'].append("Fix syntax errors in main.py")
    
    if not rules_strict:
        report['recommendations'].append("Consider making teaching rules even stricter")
    
    # Overall status
    all_valid = all(report['validation_results'].values())
    
    print(f"\n🎯 OVERALL STATUS: {'✅ READY' if all_valid else '⚠️ NEEDS ATTENTION'}")
    
    if report['recommendations']:
        print("\n📝 RECOMMENDATIONS:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")
    
    return report

def main():
    """Main validation function"""
    print("🔍 TEACHING RULES IMPLEMENTATION VALIDATION")
    print("=" * 60)
    print("Validating that all fixes have been properly implemented:")
    print("1. Stricter teaching rules enforcement")
    print("2. Enhanced phase transition logic")
    print("3. Lesson completion progression")
    print("4. Teaching rules override mechanisms")
    print("=" * 60)
    
    # Import datetime here to avoid issues
    from datetime import datetime
    
    # Run all validations
    try:
        report = generate_implementation_report()
        
        # Check overall success
        all_valid = all(report['validation_results'].values())
        
        if all_valid:
            print("\n🎉 VALIDATION SUCCESSFUL!")
            print("All teaching rules enforcement fixes have been properly implemented.")
            print("\nThe lesson flow should now:")
            print("✅ Require minimum 12 teaching interactions before quiz")
            print("✅ Require 85% objective coverage before quiz")
            print("✅ Require 0.75 content depth score before quiz")
            print("✅ Require minimum 15 minutes of teaching time")
            print("✅ Properly transition through all lesson phases")
            print("✅ Generate lesson summaries upon completion")
            return True
        else:
            print("\n⚠️ VALIDATION INCOMPLETE")
            print("Some fixes may not have been properly applied.")
            print("Please review the validation results above.")
            return False
            
    except Exception as e:
        print(f"\n❌ VALIDATION ERROR: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)